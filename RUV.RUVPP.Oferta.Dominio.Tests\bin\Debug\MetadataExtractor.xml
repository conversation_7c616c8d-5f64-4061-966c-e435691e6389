<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MetadataExtractor</name>
    </assembly>
    <members>
        <member name="T:MetadataExtractor.Age">
            <summary>Represents an age in years, months, days, hours, minutes and seconds.</summary>
            <remarks>
            Used by certain Panasonic cameras which have face recognition features.
            </remarks>
            <author><PERSON> https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Age.FromPanasonicString(System.String)">
            <summary>
            Parses an age object from the string format used by Panasonic cameras:
            <c>0031:07:15 00:00:00</c>
            </summary>
            <param name="s">The string in format <c>0031:07:15 00:00:00</c>.</param>
            <returns>The parsed Age object, or null if the value could not be parsed</returns>
        </member>
        <member name="T:MetadataExtractor.Directory">
            <summary>
            Abstract base class for all directory implementations, having methods for getting and setting tag values of various
            data types.
            </summary>
            <author><PERSON> https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Directory._tagMap">
            <summary>Map of values hashed by type identifiers.</summary>
        </member>
        <member name="F:MetadataExtractor.Directory._definedTagList">
            <summary>Holds tags in the order in which they were stored.</summary>
        </member>
        <member name="F:MetadataExtractor.Directory._descriptor">
            <summary>The descriptor used to interpret tag values.</summary>
        </member>
        <member name="P:MetadataExtractor.Directory.Name">
            <summary>Provides the name of the directory, for display purposes.</summary>
            <value>the name of the directory</value>
        </member>
        <member name="P:MetadataExtractor.Directory.Parent">
            <summary>
            The parent <see cref="T:MetadataExtractor.Directory"/>, when available, which may be used to construct information about the hierarchical structure of metadata.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Directory.TryGetTagName(System.Int32,System.String@)">
            <summary>Attempts to find the name of the specified tag.</summary>
            <param name="tagType">The tag to look up.</param>
            <param name="tagName">The found name, if any.</param>
            <returns><c>true</c> if the tag is known and <paramref name="tagName"/> was set, otherwise <c>false</c>.</returns>
        </member>
        <member name="P:MetadataExtractor.Directory.IsEmpty">
            <summary>Gets a value indicating whether the directory is empty, meaning it contains no errors and no tag values.</summary>
        </member>
        <member name="M:MetadataExtractor.Directory.ContainsTag(System.Int32)">
            <summary>Indicates whether the specified tag type has been set.</summary>
            <param name="tagType">the tag type to check for</param>
            <returns>true if a value exists for the specified tag type, false if not</returns>
        </member>
        <member name="P:MetadataExtractor.Directory.Tags">
            <summary>Returns all <see cref="T:MetadataExtractor.Tag"/> objects that have been set in this <see cref="T:MetadataExtractor.Directory"/>.</summary>
            <value>The list of <see cref="T:MetadataExtractor.Tag"/> objects.</value>
        </member>
        <member name="P:MetadataExtractor.Directory.TagCount">
            <summary>Returns the number of tags set in this Directory.</summary>
            <value>the number of tags set in this Directory</value>
        </member>
        <member name="M:MetadataExtractor.Directory.SetDescriptor(MetadataExtractor.ITagDescriptor)">
            <summary>Sets the descriptor used to interpret tag values.</summary>
            <param name="descriptor">the descriptor used to interpret tag values</param>
        </member>
        <member name="M:MetadataExtractor.Directory.AddError(System.String)">
            <summary>Registers an error message with this directory.</summary>
            <param name="message">an error message.</param>
        </member>
        <member name="P:MetadataExtractor.Directory.HasError">
            <summary>Gets a value indicating whether this directory has one or more errors.</summary>
            <remarks>Error messages are accessible via <see cref="P:MetadataExtractor.Directory.Errors"/>.</remarks>
            <returns><c>true</c> if the directory contains errors, otherwise <c>false</c></returns>
        </member>
        <member name="P:MetadataExtractor.Directory.Errors">
            <summary>Used to iterate over any error messages contained in this directory.</summary>
            <value>The collection of error message strings.</value>
        </member>
        <member name="M:MetadataExtractor.Directory.Set(System.Int32,System.Object)">
            <summary>Sets a <c>Object</c> for the specified tag.</summary>
            <remarks>Any previous value for this tag is overwritten.</remarks>
            <param name="tagType">the tag's value as an int</param>
            <param name="value">the value for the specified tag</param>
            <exception cref="T:System.ArgumentNullException">if value is <c>null</c></exception>
        </member>
        <member name="M:MetadataExtractor.Directory.GetObject(System.Int32)">
            <summary>Returns the object hashed for the particular tag type specified, if available.</summary>
            <param name="tagType">the tag type identifier</param>
            <returns>the tag's value as an Object if available, else <c>null</c></returns>
        </member>
        <member name="M:MetadataExtractor.Directory.GetTagName(System.Int32)">
            <summary>Returns the name of a specified tag as a String.</summary>
            <param name="tagType">the tag type identifier</param>
            <returns>the tag's name as a String</returns>
        </member>
        <member name="M:MetadataExtractor.Directory.HasTagName(System.Int32)">
            <summary>Gets whether the specified tag is known by the directory and has a name.</summary>
            <param name="tagType">the tag type identifier</param>
            <returns>whether this directory has a name for the specified tag</returns>
        </member>
        <member name="M:MetadataExtractor.Directory.GetDescription(System.Int32)">
            <summary>
            Provides a description of a tag's value using the descriptor set by <see cref="M:MetadataExtractor.Directory.SetDescriptor(MetadataExtractor.ITagDescriptor)"/>.
            </summary>
            <param name="tagType">the tag type identifier</param>
            <returns>the tag value's description as a String</returns>
        </member>
        <member name="T:MetadataExtractor.ErrorDirectory">
            <summary>
            A directory to use for the reporting of errors. No values may be added to this directory, only warnings and errors.
            </summary>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetByte(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as a <see cref="T:System.Byte"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetInt16(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as a <see cref="T:System.Int16"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetUInt16(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as a <see cref="T:System.UInt16"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetInt32(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as an <see cref="T:System.Int32"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetUInt32(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as a <see cref="T:System.UInt32"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetInt64(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as an <see cref="T:System.Int32"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetSingle(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as a <see cref="T:System.Single"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetDouble(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as an <see cref="T:System.Double"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetBoolean(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as an <see cref="T:System.Boolean"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetStringArray(MetadataExtractor.Directory,System.Int32)">
            <summary>Gets the specified tag's value as a String array, if possible.</summary>
            <remarks>Only supported where the tag is set as String[], String, int[], byte[] or Rational[].</remarks>
            <returns>the tag's value as an array of Strings. If the value is unset or cannot be converted, <c>null</c> is returned.</returns>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetStringValueArray(MetadataExtractor.Directory,System.Int32)">
            <summary>Gets the specified tag's value as a StringValue array, if possible.</summary>
            <remarks>Only succeeds if the tag is set as StringValue[], or String.</remarks>
            <returns>the tag's value as an array of StringValues. If the value is unset or cannot be converted, <c>null</c> is returned.</returns>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetInt32Array(MetadataExtractor.Directory,System.Int32)">
            <summary>Gets the specified tag's value as an int array, if possible.</summary>
            <remarks>Only supported where the tag is set as String, Integer, int[], byte[] or Rational[].</remarks>
            <returns>the tag's value as an int array</returns>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetByteArray(MetadataExtractor.Directory,System.Int32)">
            <summary>Gets the specified tag's value as an byte array, if possible.</summary>
            <remarks>Only supported where the tag is set as StringValue, String, Integer, int[], byte[] or Rational[].</remarks>
            <returns>the tag's value as a byte array</returns>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetDateTime(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns a tag's value as a <see cref="T:System.DateTime"/>, or throws if conversion is not possible.</summary>
            <remarks>
            If the value is <see cref="T:System.IConvertible"/>, then that interface is used for conversion of the value.
            If the value is an array of <see cref="T:System.IConvertible"/> having length one, then the single item is converted.
            </remarks>
            <exception cref="T:MetadataExtractor.MetadataException">No value exists for <paramref name="tagType"/>, or the value is not convertible to the requested type.</exception>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.TryGetDateTime(MetadataExtractor.Directory,System.Int32,System.DateTime@)">
            <summary>Attempts to return the specified tag's value as a DateTime.</summary>
            <remarks>If the underlying value is a <see cref="T:System.String"/>, then attempts will be made to parse it.</remarks>
            <returns><c>true</c> if a DateTime was returned, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.TryGetRational(MetadataExtractor.Directory,System.Int32,MetadataExtractor.Rational@)">
            <summary>Returns the specified tag's value as a Rational.</summary>
            <remarks>If the value is unset or cannot be converted, <c>null</c> is returned.</remarks>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetRationalArray(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns the specified tag's value as an array of Rational.</summary>
            <remarks>If the value is unset or cannot be converted, <c>null</c> is returned.</remarks>
        </member>
        <member name="M:MetadataExtractor.DirectoryExtensions.GetString(MetadataExtractor.Directory,System.Int32)">
            <summary>Returns the specified tag's value as a String.</summary>
            <remarks>
            This value is the 'raw' value.  A more presentable decoding
            of this value may be obtained from the corresponding Descriptor.
            </remarks>
            <returns>
            the String representation of the tag's value, or
            <c>null</c> if the tag hasn't been defined.
            </returns>
        </member>
        <member name="T:MetadataExtractor.Face">
            <summary>Models information about a face in an image.</summary>
            <remarks>
            When a face is detected, the camera believes that a face is present at a given location in
            the image, but is not sure whose face it is.  When a face is recognised, then the face is
            both detected and identified as belonging to a known person.
            <para />
            Currently this is only used by <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory"/>.
            </remarks>
            <author>Philipp Sandhaus, Drew Noakes</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Adobe.AdobeJpegDescriptor">
            <summary>Provides human-readable string versions of the tags stored in an AdobeJpegDirectory.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Adobe.AdobeJpegDirectory">
            <summary>Contains image encoding information for DCT filters, as stored by Adobe.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Adobe.AdobeJpegDirectory.TagApp14Flags0">
            <remarks>
            The convention for TAG_APP14_FLAGS0 and TAG_APP14_FLAGS1 is that 0 bits are benign.
            1 bits in TAG_APP14_FLAGS0 pass information that is possibly useful but not essential for decoding.
            <para />
            0x8000 bit: Encoder used Blend=1 downsampling
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Adobe.AdobeJpegDirectory.TagApp14Flags1">
            <remarks>
            The convention for TAG_APP14_FLAGS0 and TAG_APP14_FLAGS1 is that 0 bits are benign.
            1 bits in TAG_APP14_FLAGS1 pass information essential for decoding. DCTDecode could reject a compressed
            image, if there are 1 bits in TAG_APP14_FLAGS1 or color transform codes that it cannot interpret.
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Adobe.AdobeJpegReader">
            <summary>Decodes Adobe formatted data stored in JPEG files, normally in the APPE (App14) segment.</summary>
            <author>Philip</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Avi.AviDescriptor">
            <author>Payton Garland</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Avi.AviDirectory">
            <author>Payton Garland</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Avi.AviMetadataReader">
            <summary>Obtains metadata from Avi files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Avi.AviMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Avi.AviMetadataReader.ReadMetadata(System.IO.Stream)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Avi.AviRiffHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Riff.IRiffHandler"/> specialising in AVI support.
            </summary>
            <remarks>
            Extracts data from chunk/list types:
            <list type="bullet">
              <item><c>"avih"</c>: width, height, streams</item>
              <item><c>"strh"</c>: frames/second, samples/second, duration, video codec</item>
            </list>
            Sources:
            http://www.alexander-noe.com/video/documentation/avi.pdf
            https://msdn.microsoft.com/en-us/library/ms899422.aspx
            https://www.loc.gov/preservation/digital/formats/fdd/fdd000025.shtml
            </remarks>
            <author>Payton Garland</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Bmp.BmpHeaderDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType">
            <summary>
            Possible "magic bytes" indicating bitmap type
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType.Bitmap">
            "BM" - Windows or OS/2 bitmap 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType.OS2BitmapArray">
            "BA" - OS/2 Bitmap array (multiple bitmaps) 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType.OS2Icon">
            "IC" - OS/2 Icon 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType.OS2ColorIcon">
            "CI" - OS/2 Color icon 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType.OS2ColorPointer">
            "CP" - OS/2 Color pointer 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.BitmapType.OS2Pointer">
            "PT" - OS/2 Pointer 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Rgb">
            0 = None 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Rle8">
            1 = RLE 8-bit/pixel 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Rle4">
            2 = RLE 4-bit/pixel 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.BitFields">
            3 = Bit fields (not OS22XBITMAPHEADER (size 64)) 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Huffman1D">
            3 = Huffman 1D (if OS22XBITMAPHEADER (size 64)) 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Jpeg">
            4 = JPEG (not OS22XBITMAPHEADER (size 64)) 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Rle24">
            4 = RLE 24-bit/pixel (if OS22XBITMAPHEADER (size 64)) 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Png">
            5 = PNG 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.AlphaBitFields">
            6 = RGBA bit fields 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.Cmyk">
            11 = CMYK 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.CmykRle8">
            12 = CMYK RLE-8 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.Compression.CmykRle4">
            13 = CMYK RLE-4 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingHalftoningAlgorithm.None">
            No halftoning algorithm 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingHalftoningAlgorithm.ErrorDiffusion">
            Error Diffusion Halftoning 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingHalftoningAlgorithm.Panda">
            Processing Algorithm for Noncoded Document Acquisition 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingHalftoningAlgorithm.SuperCircle">
            Super-circle Halftoning 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.ColorSpaceType.LcsCalibratedRgb">
            0 = Calibrated RGB 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.ColorSpaceType.LcsSRgb">
            "sRGB" = sRGB Color Space 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.ColorSpaceType.LcsWindowsColorSpace">
            "Win " = System Default Color Space, sRGB 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.ColorSpaceType.ProfileLinked">
            "LINK" = Linked Profile 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.ColorSpaceType.ProfileEmbedded">
            "MBED" = Embedded Profile 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingIntent.LcsGmBusiness">
            Graphic, Saturation 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingIntent.LcsGmGraphics">
            Proof, Relative Colorimetric 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingIntent.LcsGmImages">
            Picture, Perceptual 
        </member>
        <member name="F:MetadataExtractor.Formats.Bmp.BmpHeaderDirectory.RenderingIntent.LcsGmAbsColorimetric">
            Match, Absolute Colorimetric 
        </member>
        <member name="T:MetadataExtractor.Formats.Bmp.BmpMetadataReader">
            <summary>Obtains metadata from BMP files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Bmp.BmpMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Bmp.BmpReader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Eps.EpsDescriptor">
            <summary>Provides human-readable string versions of the tags stored in a <see cref="T:MetadataExtractor.Formats.Eps.EpsDirectory"/>.</summary>
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Eps.EpsDirectory">
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Eps.EpsMetadataReader">
            <summary>Obtains metadata from EPS files.</summary>
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Eps.EpsReader">
            <summary>Reads file passed in through SequentialReader and parses encountered data:</summary>
            <remarks>
            <list type="bullet">
              <item>Basic EPS Comments</item>
              <item>EXIF</item>
              <item>Photoshop</item>
              <item>IPTC</item>
              <item>ICC Profile</item>
              <item>XMP</item>
            </list>
            EPS comments are retrieved from EPS directory.  Photoshop, ICC Profile, and XMP processing
            is passed to their respective reader.
            <para />
            EPS Constraints (Source: https://www-cdf.fnal.gov/offline/PostScript/5001.PDF pg.18):
            <list type = "bullet" >
              <item>Max line length is 255 characters</item>
              <item>Lines end with a CR(0xD) or LF(0xA) character (or both, in practice)</item>
              <item>':' separates keywords (considered part of the keyword)</item>
              <item>Whitespace is either a space(0x20) or tab(0x9)</item>
              <item>If there is more than one header, the 1st is truth</item>
            </list>
            </remarks>
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.Extract(MetadataExtractor.Formats.Eps.EpsDirectory,System.Collections.Generic.List{MetadataExtractor.Directory},MetadataExtractor.IO.SequentialReader)">
            <summary>
            Main method that parses all comments and then distributes data extraction among other methods that parse the
            rest of file and store encountered data in metadata(if there exists an entry in EpsDirectory
            for the found data).  Reads until a begin data/binary comment is found or reader's estimated
            available data has run out (or AI09 End Private Data).  Will extract data from normal EPS comments, Photoshop, ICC, and XMP.
            </summary>
            <param name="directory"></param>
            <param name="directories">list to add directory to and extracted data</param>
            <param name="reader"></param>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.AddToDirectory(MetadataExtractor.Formats.Eps.EpsDirectory,System.String,System.String)">
            <summary>
            Default case that adds comment with keyword to directory
            </summary>
            <param name="directory">EpsDirectory to add extracted data to</param>
            <param name="name">String that holds name of current comment</param>
            <param name="value">String that holds value of current comment</param>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.ExtractImageData(MetadataExtractor.Formats.Eps.EpsDirectory,System.String)">
            <summary>
            Parses '%ImageData' comment which holds several values including width in px,
            height in px and color type.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.ExtractPhotoshopData(System.Collections.Generic.List{MetadataExtractor.Directory},MetadataExtractor.IO.SequentialReader)">
            <summary>
            Decodes a commented hex section, and uses <see cref="T:MetadataExtractor.Formats.Photoshop.PhotoshopReader"/> to decode the resulting data.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.ExtractIccData(System.Collections.Generic.List{MetadataExtractor.Directory},MetadataExtractor.IO.SequentialReader)">
            <summary>
            Decodes a commented hex section, and uses <see cref="T:MetadataExtractor.Formats.Icc.IccReader"/> to decode the resulting data.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.ExtractXmpData(System.Collections.Generic.List{MetadataExtractor.Directory},MetadataExtractor.IO.SequentialReader)">
            <summary>
            Extracts an XMP xpacket, and uses <see cref="T:MetadataExtractor.Formats.Xmp.XmpReader"/> to decode the resulting data.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.ReadUntil(MetadataExtractor.IO.SequentialReader,System.Byte[])">
            <summary>
            Reads all bytes until the given sentinel is observed.
            The sentinel will be included in the returned bytes.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.DecodeHexCommentBlock(MetadataExtractor.IO.SequentialReader)">
             EPS files can contain hexadecimal-encoded ASCII blocks, each prefixed with <c>"% "</c>.
             This method reads such a block and returns a byte[] of the decoded contents.
             Reading stops at the first invalid line, which is discarded (it's a terminator anyway).
             <p/>
             For example:
             <pre><code>
             %BeginPhotoshop: 9564
             % 3842494D040400000000005D1C015A00031B25471C0200000200041C02780004
             % 6E756C6C1C027A00046E756C6C1C025000046E756C6C1C023700083230313630
             % 3331311C023C000B3131343335362B303030301C023E00083230313630333131
             % 48000000010000003842494D03FD0000000000080101000000000000
             %EndPhotoshop
             </code></pre>
             When calling this method, the reader must be positioned at the start of the first line containing
             hex data, not at the introductory line.
            
             @return The decoded bytes, or <code>null</code> if decoding failed.
            <remarks>
            EPS files can contain hexadecimal-encoded ASCII blocks, each prefixed with "% ".
            This method reads such a block and returns a byte[] of the decoded contents.
            Reading stops at the first invalid line, which is discarded(it's a terminator anyway).
            <para />
            For example:
            <para />
            %BeginPhotoshop: 9564
            % 3842494D040400000000005D1C015A00031B25471C0200000200041C02780004
            % 6E756C6C1C027A00046E756C6C1C025000046E756C6C1C023700083230313630
            % 3331311C023C000B3131343335362B303030301C023E00083230313630333131
            % 48000000010000003842494D03FD0000000000080101000000000000
            %EndPhotoshop
            <para />
            When calling this method, the reader must be positioned at the start of the first line containing
            hex data, not at the introductory line.
            </remarks>
            <returns>The decoded bytes, or null if decoding failed.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Eps.EpsReader.TryHexToInt(System.Byte)">
            <summary>
            Treats a byte as an ASCII character, and returns its numerical value in hexadecimal.
            If conversion is not possible, returns -1.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifDescriptorBase`1">
            <summary>Base class for several Exif format descriptor classes.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifDescriptorBase`1.GetUnicodeDescription(System.Int32)">
            <summary>The Windows specific tags uses plain Unicode.</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifDescriptorBase`1.GetCfaPatternDescription">
             <summary>
             String description of CFA Pattern
             </summary>
             <remarks>
             Converted from Exiftool version 10.33 created by Phil Harvey
             http://www.sno.phy.queensu.ca/~phil/exiftool/
             lib\Image\ExifTool\Exif.pm
            
             Indicates the color filter array (CFA) geometric pattern of the image sensor when a one-chip color area sensor is used.
             It does not apply to all sensing methods.
             </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifDescriptorBase`1.GetCfaPattern2Description">
             <summary>
             String description of CFA Pattern
             </summary>
             <remarks>
             Indicates the color filter array (CFA) geometric pattern of the image sensor when a one-chip color area sensor is used.
             It does not apply to all sensing methods.
            
             <see cref="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagCfaPattern2"/> holds only the pixel pattern. <see cref="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagCfaRepeatPatternDim"/> is expected to exist and pass
             some conditional tests.
             </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifDescriptorBase`1.DecodeCfaPattern(System.Int32)">
             <summary>
             Decode raw CFAPattern value
             </summary>
             <remarks>
             Converted from Exiftool version 10.33 created by Phil Harvey
             http://www.sno.phy.queensu.ca/~phil/exiftool/
             lib\Image\ExifTool\Exif.pm
            
             The value consists of:
             - Two short, being the grid width and height of the repeated pattern.
             - Next, for every pixel in that pattern, an identification code.
             </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifDirectoryBase">
            <summary>Base class for several Exif format tag directories.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagNewSubfileType">
            <summary>The new subfile type tag.</summary>
            <remarks>
            0 = Full-resolution Image
            1 = Reduced-resolution image
            2 = Single page of multi-page image
            3 = Single page of multi-page reduced-resolution image
            4 = Transparency mask
            5 = Transparency mask of reduced-resolution image
            6 = Transparency mask of multi-page image
            7 = Transparency mask of reduced-resolution multi-page image
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSubfileType">
            <summary>The old subfile type tag.</summary>
            <remarks>
            1 = Full-resolution image (Main image)
            2 = Reduced-resolution image (Thumbnail)
            3 = Single page of multi-page image
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagBitsPerSample">
            <summary>
            When image format is no compression, this value shows the number of bits
            per component for each pixel.
            </summary>
            <remarks>
            Usually this value is '8,8,8'.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagPhotometricInterpretation">
            <summary>Shows the color space of the image data components.</summary>
            <remarks>
            0 = WhiteIsZero
            1 = BlackIsZero
            2 = RGB
            3 = RGB Palette
            4 = Transparency Mask
            5 = CMYK
            6 = YCbCr
            8 = CIELab
            9 = ICCLab
            10 = ITULab
            32803 = Color Filter Array
            32844 = Pixar LogL
            32845 = Pixar LogLuv
            34892 = Linear Raw
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagThresholding">
            <summary>
            1 = No dithering or halftoning
            2 = Ordered dither or halftone
            3 = Randomized dither
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagFillOrder">
            <summary>
            1 = Normal
            2 = Reversed
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagStripOffsets">
            <summary>The position in the file of raster data.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSamplesPerPixel">
            <summary>Each pixel is composed of this many samples.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagRowsPerStrip">
            <summary>The raster is codified by a single block of data holding this many rows.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagStripByteCounts">
            <summary>The size of the raster data in bytes.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagPlanarConfiguration">
            <summary>
            When image format is no compression YCbCr, this value shows byte aligns of YCbCr data.
            </summary>
            <remarks>
            If value is '1', Y/Cb/Cr value is chunky format, contiguous for
            each subsampling pixel. If value is '2', Y/Cb/Cr value is separated and
            stored to Y plane/Cb plane/Cr plane format.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSubIfdOffset">
            <summary>Tag is a pointer to one or more sub-IFDs.</summary>
            <remarks>Seems to be used exclusively by raw formats, referencing one or two IFDs.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagCfaPattern2">
            <summary>There are two definitions for CFA pattern, I don't know the difference...</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagExposureTime">
            <summary>Exposure time (reciprocal of shutter speed).</summary>
            <remarks>Unit is second.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagFNumber">
            <summary>The actual F-number(F-stop) of lens when the image was taken.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagExposureProgram">
            <summary>Exposure program that the camera used when image was taken.</summary>
            <remarks>
            '1' means
            manual control, '2' program normal, '3' aperture priority, '4' shutter
            priority, '5' program creative (slow program), '6' program action
            (high-speed program), '7' portrait mode, '8' landscape mode.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagOptoElectricConversionFunction">
            <summary>Indicates the Opto-Electric Conversion Function (OECF) specified in ISO 14524.</summary>
            <remarks>
            OECF is the relationship between the camera optical input and the image values.
            <para />
            The values are:
            <list type="bullet">
            <item>Two shorts, indicating respectively number of columns, and number of rows.</item>
            <item>For each column, the column name in a null-terminated ASCII string.</item>
            <item>For each cell, an SRATIONAL value.</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSensitivityType">
            <summary>Applies to ISO tag.</summary>
            <remarks>
            0 = Unknown
            1 = Standard Output Sensitivity
            2 = Recommended Exposure Index
            3 = ISO Speed
            4 = Standard Output Sensitivity and Recommended Exposure Index
            5 = Standard Output Sensitivity and ISO Speed
            6 = Recommended Exposure Index and ISO Speed
            7 = Standard Output Sensitivity, Recommended Exposure Index and ISO Speed
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagTimeZoneOffset">
            <summary>Non-standard, but in use.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagCompressedAverageBitsPerPixel">
            <summary>Average (rough estimate) compression level in JPEG bits per pixel.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagShutterSpeed">
            <summary>Shutter speed by APEX value.</summary>
            <remarks>
            To convert this value to ordinary 'Shutter Speed';
            calculate this value's power of 2, then reciprocal. For example, if the
            ShutterSpeedValue is '4', shutter speed is 1/(24)=1/16 second.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagAperture">
            <summary>The actual aperture value of lens when the image was taken.</summary>
            <remarks>
            Unit is APEX.
            To convert this value to ordinary F-number (F-stop), calculate this value's
            power of root 2 (=1.4142). For example, if the ApertureValue is '5',
            F-number is 1.4142^5 = F5.6.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagMaxAperture">
            <summary>Maximum aperture value of lens.</summary>
            <remarks>
            You can convert to F-number by calculating
            power of root 2 (same process of ApertureValue:0x9202).
            The actual aperture value of lens when the image was taken. To convert this
            value to ordinary f-number(f-stop), calculate the value's power of root 2
            (=1.4142). For example, if the ApertureValue is '5', f-number is 1.41425^5 = F5.6.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSubjectDistance">
            <summary>Indicates the distance the autofocus camera is focused to.</summary>
            <remarks>Tends to be less accurate as distance increases.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagMeteringMode">
            <summary>Exposure metering method.</summary>
            <remarks>
            '0' means unknown, '1' average, '2' center weighted average,
            '3' spot, '4' multi-spot, '5' multi-segment, '6' partial,
            '255' other.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWhiteBalance">
            <summary>White balance (aka light source).</summary>
            <remarks>
            '0' means unknown, '1' daylight,
            '2' fluorescent, '3' tungsten, '10' flash, '17' standard light A,
            '18' standard light B, '19' standard light C, '20' D55, '21' D65,
            '22' D75, '255' other.
            </remarks>
            <seealso cref="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWhiteBalanceMode" />
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagFlash">
            <summary>
            0x0  = 0000000 = No Flash
            0x1  = 0000001 = Fired
            0x5  = 0000101 = Fired, Return not detected
            0x7  = 0000111 = Fired, Return detected
            0x9  = 0001001 = On
            0xd  = 0001101 = On, Return not detected
            0xf  = 0001111 = On, Return detected
            0x10 = 0010000 = Off
            0x18 = 0011000 = Auto, Did not fire
            0x19 = 0011001 = Auto, Fired
            0x1d = 0011101 = Auto, Fired, Return not detected
            0x1f = 0011111 = Auto, Fired, Return detected
            0x20 = 0100000 = No flash function
            0x41 = 1000001 = Fired, Red-eye reduction
            0x45 = 1000101 = Fired, Red-eye reduction, Return not detected
            0x47 = 1000111 = Fired, Red-eye reduction, Return detected
            0x49 = 1001001 = On, Red-eye reduction
            0x4d = 1001101 = On, Red-eye reduction, Return not detected
            0x4f = 1001111 = On, Red-eye reduction, Return detected
            0x59 = 1011001 = Auto, Fired, Red-eye reduction
            0x5d = 1011101 = Auto, Fired, Red-eye reduction, Return not detected
            0x5f = 1011111 = Auto, Fired, Red-eye reduction, Return detected
                   6543210 (positions)
            This is a bitmask.
            0 = flash fired
            1 = return detected
            2 = return able to be detected
            3 = unknown
            4 = auto used
            5 = unknown
            6 = red eye reduction used
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagFocalLength">
            <summary>Focal length of lens used to take image.</summary>
            <remarks>
            Unit is millimeter.
            Nice digital cameras actually save the focal length as a function of how far they are zoomed in.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagMakernote">
            <summary>This tag holds the Exif Makernote.</summary>
            <remarks>
            Makernotes are free to be in any format, though they are often IFDs.
            To determine the format, we consider the starting bytes of the makernote itself and sometimes the
            camera model and make.
            <para />
            The component count for this tag includes all of the bytes needed for the makernote.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWinTitle">
            <summary>The image title, as used by Windows XP.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWinComment">
            <summary>The image comment, as used by Windows XP.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWinAuthor">
            <summary>The image author, as used by Windows XP (called Artist in the Windows shell).</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWinKeywords">
            <summary>The image keywords, as used by Windows XP.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWinSubject">
            <summary>The image subject, as used by Windows XP.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagColorSpace">
            <summary>Defines Color Space.</summary>
            <remarks>
            DCF image must use sRGB color space so value is
            always '1'. If the picture uses the other color space, value is
            '65535':Uncalibrated.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagFocalPlaneResolutionUnit">
            <summary>Unit of FocalPlaneXResolution/FocalPlaneYResolution.</summary>
            <remarks>
            '1' means no-unit, '2' inch, '3' centimeter.
            Note: Some of Fujifilm's digicam(e.g.FX2700,FX2900,Finepix4700Z/40i etc)
            uses value '3' so it must be 'centimeter', but it seems that they use a
            '8.3mm?'(1/3in.?) to their ResolutionUnit. Fuji's BUG? Finepix4900Z has
            been changed to use value '2' but it doesn't match to actual value also.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagCustomRendered">
            <summary>
            This tag indicates the use of special processing on image data, such as rendering
            geared to output.
            </summary>
            <remarks>
            When special processing is performed, the reader is expected to
            disable or minimize any further processing.
            Tag = 41985 (A401.H)
            Type = SHORT
            Count = 1
            Default = 0
            0 = Normal process
            1 = Custom process
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagExposureMode">
            <summary>This tag indicates the exposure mode set when the image was shot.</summary>
            <remarks>
            In auto-bracketing mode, the camera shoots a series of frames of the
            same scene at different exposure settings.
            Tag = 41986 (A402.H)
            Type = SHORT
            Count = 1
            Default = none
            0 = Auto exposure
            1 = Manual exposure
            2 = Auto bracket
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWhiteBalanceMode">
            <summary>This tag indicates the white balance mode set when the image was shot.</summary>
            <remarks>
            Tag = 41987 (A403.H)
            Type = SHORT
            Count = 1
            Default = none
            0 = Auto white balance
            1 = Manual white balance
            Other = reserved
            </remarks>
            <seealso cref="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagWhiteBalance" />
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagDigitalZoomRatio">
            <summary>This tag indicates the digital zoom ratio when the image was shot.</summary>
            <remarks>
            If the numerator of the recorded value is 0, this indicates that digital zoom was
            not used.
            Tag = 41988 (A404.H)
            Type = RATIONAL
            Count = 1
            Default = none
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.Tag35MMFilmEquivFocalLength">
            <summary>
            This tag indicates the equivalent focal length assuming a 35mm film camera, in mm.
            </summary>
            <remarks>
            A value of 0 means the focal length is unknown. Note that this tag
            differs from the FocalLength tag.
            Tag = 41989 (A405.H)
            Type = SHORT
            Count = 1
            Default = none
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSceneCaptureType">
            <summary>This tag indicates the type of scene that was shot.</summary>
            <remarks>
            It can also be used to
            record the mode in which the image was shot. Note that this differs from
            the scene type (SceneType) tag.
            Tag = 41990 (A406.H)
            Type = SHORT
            Count = 1
            Default = 0
            0 = Standard
            1 = Landscape
            2 = Portrait
            3 = Night scene
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagGainControl">
            <summary>This tag indicates the degree of overall image gain adjustment.</summary>
            <remarks>
            Tag = 41991 (A407.H)
            Type = SHORT
            Count = 1
            Default = none
            0 = None
            1 = Low gain up
            2 = High gain up
            3 = Low gain down
            4 = High gain down
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagContrast">
            <summary>
            This tag indicates the direction of contrast processing applied by the camera
            when the image was shot.
            </summary>
            <remarks>
            Tag = 41992 (A408.H)
            Type = SHORT
            Count = 1
            Default = 0
            0 = Normal
            1 = Soft
            2 = Hard
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSaturation">
            <summary>
            This tag indicates the direction of saturation processing applied by the camera
            when the image was shot.
            </summary>
            <remarks>
            Tag = 41993 (A409.H)
            Type = SHORT
            Count = 1
            Default = 0
            0 = Normal
            1 = Low saturation
            2 = High saturation
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSharpness">
            <summary>
            This tag indicates the direction of sharpness processing applied by the camera
            when the image was shot.
            </summary>
            <remarks>
            Tag = 41994 (A40A.H)
            Type = SHORT
            Count = 1
            Default = 0
            0 = Normal
            1 = Soft
            2 = Hard
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagDeviceSettingDescription">
            <summary>
            This tag indicates information on the picture-taking conditions of a particular
            camera model.
            </summary>
            <remarks>
            The tag is used only to indicate the picture-taking conditions in the reader.
            Tag = 41995 (A40B.H)
            Type = UNDEFINED
            Count = Any
            Default = none
            The information is recorded in the format shown below. The data is recorded
            in Unicode using SHORT type for the number of display rows and columns and
            UNDEFINED type for the camera settings. The Unicode (UCS-2) string including
            Signature is NULL terminated. The specifics of the Unicode string are as given
            in ISO/IEC 10464-1.
            Length  Type        Meaning
            ------+-----------+------------------
            2       <USER>       <GROUP> columns
            2       SHORT       Display rows
            Any     UNDEFINED   Camera setting-1
            Any     UNDEFINED   Camera setting-2
            :       :           :
            Any     UNDEFINED   Camera setting-n
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagSubjectDistanceRange">
            <summary>This tag indicates the distance to the subject.</summary>
            <remarks>
            Tag = 41996 (A40C.H)
            Type = SHORT
            Count = 1
            Default = none
            0 = unknown
            1 = Macro
            2 = Close view
            3 = Distant view
            Other = reserved
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagImageUniqueId">
            <summary>This tag indicates an identifier assigned uniquely to each image.</summary>
            <remarks>
            It is recorded as an ASCII string equivalent to hexadecimal notation and 128-bit
            fixed length.
            Tag = 42016 (A420.H)
            Type = ASCII
            Count = 33
            Default = none
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagCameraOwnerName">
            <summary>String.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagBodySerialNumber">
            <summary>String.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagLensSpecification">
            <summary>An array of four Rational64u numbers giving focal and aperture ranges.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagLensMake">
            <summary>String.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagLensModel">
            <summary>String.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagLensSerialNumber">
            <summary>String.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifDirectoryBase.TagGamma">
            <summary>Rational64u.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifIfd0Descriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.ExifIfd0Directory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifIfd0Directory">
            <summary>Describes Exif tags from the IFD0 directory.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifIfd0Directory.TagExifSubIfdOffset">
            <summary>This tag is a pointer to the Exif SubIFD.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifIfd0Directory.TagGpsInfoOffset">
            <summary>This tag is a pointer to the Exif GPS IFD.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifImageDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.ExifImageDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifImageDirectory">
            <summary>One of several Exif directories.</summary>
            <remarks>Holds information about image IFD's in a chain after the first. The first page is stored in IFD0.</remarks>
            <remarks>Currently, this only applies to multi-page TIFF images</remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifInteropDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.ExifInteropDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifInteropDirectory">
            <summary>Describes Exif interoperability tags.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifReader">
            <summary>
            Decodes Exif binary data into potentially many <see cref="T:MetadataExtractor.Directory"/> objects such as
            <see cref="T:MetadataExtractor.Formats.Exif.ExifSubIfdDirectory"/>, <see cref="T:MetadataExtractor.Formats.Exif.ExifThumbnailDirectory"/>, <see cref="T:MetadataExtractor.Formats.Exif.ExifInteropDirectory"/>,
            <see cref="T:MetadataExtractor.Formats.Exif.GpsDirectory"/>, camera makernote directories and more.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifReader.JpegSegmentPreamble">
            <summary>Exif data stored in JPEG files' APP1 segment are preceded by this six character preamble "Exif\0\0".</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifReader.StartsWithJpegExifPreamble(System.Byte[])">
            <summary>
            Indicates whether <paramref name="bytes"/> starts with <see cref="F:MetadataExtractor.Formats.Exif.ExifReader.JpegSegmentPreamble"/>.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifReader.Extract(MetadataExtractor.IO.IndexedReader)">
            <summary>
            Reads TIFF formatted Exif data a specified offset within a <see cref="T:MetadataExtractor.IO.IndexedReader"/>.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifSubIfdDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.ExifSubIfdDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifSubIfdDirectory">
            <summary>Describes Exif tags from the SubIFD directory.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifSubIfdDirectory.TagInteropOffset">
            <summary>This tag is a pointer to the Exif Interop IFD.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifThumbnailDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.ExifThumbnailDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifThumbnailDirectory">
            <summary>One of several Exif directories.</summary>
            <remarks>Otherwise known as IFD1, this directory holds information about an embedded thumbnail image.</remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifThumbnailDirectory.TagThumbnailOffset">
            <summary>The offset to thumbnail image bytes.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.ExifThumbnailDirectory.TagThumbnailLength">
            <summary>The size of the thumbnail image data in bytes.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.ExifTiffHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Tiff.ITiffHandler"/> used for handling TIFF tags according to the Exif standard.
            <para />
            Includes support for camera manufacturer makernotes.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifTiffHandler.SetTiffMarker(System.Int32)">
            <exception cref="T:MetadataExtractor.Formats.Tiff.TiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifTiffHandler.ProcessMakernote(System.Int32,System.Collections.Generic.ICollection{System.Int32},MetadataExtractor.IO.IndexedReader)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.ExifTiffHandler.ProcessPrintIM(MetadataExtractor.Formats.Exif.PrintIMDirectory,System.Int32,MetadataExtractor.IO.IndexedReader,System.Int32)">
            <summary>
            Process PrintIM IFD
            </summary>
            <remarks>
            Converted from Exiftool version 10.33 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\PrintIM.pm
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.GpsDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.GpsDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.GpsDirectory">
            <summary>Describes Exif tags that contain Global Positioning System (GPS) data.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagVersionId">
            <summary>GPS tag version GPSVersionID 0 0 BYTE 4</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagLatitudeRef">
            <summary>North or South Latitude GPSLatitudeRef 1 1 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagLatitude">
            <summary>Latitude GPSLatitude 2 2 RATIONAL 3</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagLongitudeRef">
            <summary>East or West Longitude GPSLongitudeRef 3 3 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagLongitude">
            <summary>Longitude GPSLongitude 4 4 RATIONAL 3</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagAltitudeRef">
            <summary>Altitude reference GPSAltitudeRef 5 5 BYTE 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagAltitude">
            <summary>Altitude GPSAltitude 6 6 RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagTimeStamp">
            <summary>GPS time (atomic clock) GPSTimeStamp 7 7 RATIONAL 3</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagSatellites">
            <summary>GPS satellites used for measurement GPSSatellites 8 8 ASCII Any</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagStatus">
            <summary>GPS receiver status GPSStatus 9 9 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagMeasureMode">
            <summary>GPS measurement mode GPSMeasureMode 10 A ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDop">
            <summary>Measurement precision GPSDOP 11 B RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagSpeedRef">
            <summary>Speed unit GPSSpeedRef 12 C ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagSpeed">
            <summary>Speed of GPS receiver GPSSpeed 13 D RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagTrackRef">
            <summary>Reference for direction of movement GPSTrackRef 14 E ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagTrack">
            <summary>Direction of movement GPSTrack 15 F RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagImgDirectionRef">
            <summary>Reference for direction of image GPSImgDirectionRef 16 10 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagImgDirection">
            <summary>Direction of image GPSImgDirection 17 11 RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagMapDatum">
            <summary>Geodetic survey data used GPSMapDatum 18 12 ASCII Any</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestLatitudeRef">
            <summary>Reference for latitude of destination GPSDestLatitudeRef 19 13 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestLatitude">
            <summary>Latitude of destination GPSDestLatitude 20 14 RATIONAL 3</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestLongitudeRef">
            <summary>Reference for longitude of destination GPSDestLongitudeRef 21 15 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestLongitude">
            <summary>Longitude of destination GPSDestLongitude 22 16 RATIONAL 3</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestBearingRef">
            <summary>Reference for bearing of destination GPSDestBearingRef 23 17 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestBearing">
            <summary>Bearing of destination GPSDestBearing 24 18 RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestDistanceRef">
            <summary>Reference for distance to destination GPSDestDistanceRef 25 19 ASCII 2</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDestDistance">
            <summary>Distance to destination GPSDestDistance 26 1A RATIONAL 1</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagProcessingMethod">
            <summary>Values of "GPS", "CELLID", "WLAN" or "MANUAL" by the EXIF spec.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagHPositioningError">
            <summary>GPSHPositioningError	Horizontal positioning error RATIONAL 1</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.GpsDirectory.GetGeoLocation">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the latitude and longitude
            at which this image was captured.
            </summary>
            <returns>The geographical location of this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.GpsDirectory.TryGetGpsDate(System.DateTime@)">
            <summary>
            Parses values for <see cref="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagDateStamp"/> and <see cref="F:MetadataExtractor.Formats.Exif.GpsDirectory.TagTimeStamp"/> to produce a single
            <see cref="T:System.DateTime"/> value representing when this image was captured according to the GPS unit.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.AppleMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.AppleMakernoteDirectory"/>.
            </summary>
            <remarks>Using information from http://owl.phy.queensu.ca/~phil/exiftool/TagNames/Apple.html</remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.AppleMakernoteDirectory">
            <summary>Describes tags specific to Apple cameras.</summary>
            <remarks>Using information from http://owl.phy.queensu.ca/~phil/exiftool/TagNames/Apple.html</remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.AppleMakernoteDirectory.TagAccelerationVector">
            <summary>
            XYZ coordinates of the acceleration vector in units of g.
            As viewed from the front of the phone,
            positive X is toward the left side,
            positive Y is toward the bottom,
            positive Z points into the face of the phone
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.AppleMakernoteDirectory.TagBurstUuid">
            <summary>
            Unique ID for all images in a burst.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDescriptor.DecodeCanonEv(System.Int32)">
            <summary>
            Canon hex-based EV (modulo 0x20) to real number
            </summary>
            <remarks>
            <code>
            0x00 -> 0
            0x0c -> 0.33333
            0x10 -> 0.5
            0x14 -> 0.66666
            0x20 -> 1   ... etc
            </code>
            <para />
            Converted from Exiftool version 10.10 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Canon.pm
            </remarks>
            <param name="val">value to convert</param>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDescriptor._lensTypeById">
            <summary>
            Map from <see cref="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagLensType"/> to string descriptions.
            </summary>
            <remarks>
            Data sourced from http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Canon.html#LensType
            <para />
            Note that only Canon lenses are listed. Lenses from other manufacturers may identify themselves to the camera
            as being from this set, but in fact may be quite different. This limits the usefulness of this data, unfortunately.
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory">
            <summary>Describes tags specific to Canon cameras.</summary>
            <remarks>
            Describes tags specific to Canon cameras.
            Thanks to Bill Richards for his contribution to this makernote directory.
            Many tag definitions explained here: http://www.ozhiker.com/electronics/pjmt/jpeg_info/canon_mn.html
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagMacroMode">
            <summary>
            1 = Macro
            2 = Normal
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagQuality">
            <summary>
            2 = Normal
            3 = Fine
            5 = Superfine
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagFlashMode">
            <summary>
            0 = Flash Not Fired
            1 = Auto
            2 = On
            3 = Red Eye Reduction
            4 = Slow Synchro
            5 = Auto + Red Eye Reduction
            6 = On + Red Eye Reduction
            16 = External Flash
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagContinuousDriveMode">
            <summary>
            0 = Single Frame or Timer Mode
            1 = Continuous
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagFocusMode1">
            <summary>
            0 = One-Shot
            1 = AI Servo
            2 = AI Focus
            3 = Manual Focus
            4 = Single
            5 = Continuous
            6 = Manual Focus
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagImageSize">
            <summary>
            0 = Large
            1 = Medium
            2 = Small
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagEasyShootingMode">
            <summary>
            0 = Full Auto
            1 = Manual
            2 = Landscape
            3 = Fast Shutter
            4 = Slow Shutter
            5 = Night
            6 = Black &amp; White
            7 = Sepia
            8 = Portrait
            9 = Sports
            10 = Macro / Close-Up
            11 = Pan Focus
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagDigitalZoom">
            <summary>
            0 = No Digital Zoom
            1 = 2x
            2 = 4x
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagContrast">
            <summary>
            0 = Normal
            1 = High
            65535 = Low
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagSaturation">
            <summary>
            0 = Normal
            1 = High
            65535 = Low
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagSharpness">
            <summary>
            0 = Normal
            1 = High
            65535 = Low
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagIso">
            <summary>
            0 = Check ISOSpeedRatings EXIF tag for ISO Speed
            15 = Auto ISO
            16 = ISO 50
            17 = ISO 100
            18 = ISO 200
            19 = ISO 400
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagMeteringMode">
            <summary>
            3 = Evaluative
            4 = Partial
            5 = Centre Weighted
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagFocusType">
            <summary>
            0 = Manual
            1 = Auto
            3 = Close-up (Macro)
            8 = Locked (Pan Mode)
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagAfPointSelected">
            <summary>
            12288 = None (Manual Focus)
            12289 = Auto Selected
            12290 = Right
            12291 = Centre
            12292 = Left
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagExposureMode">
            <summary>
            0 = Easy Shooting (See Easy Shooting Mode)
            1 = Program
            2 = Tv-Priority
            3 = Av-Priority
            4 = Manual
            5 = A-DEP
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagFlashActivity">
            <summary>
            0 = Flash Did Not Fire
            1 = Flash Fired
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.CameraSettings.TagFocusMode2">
            <summary>
            0 = Focus Mode: Single
            1 = Focus Mode: Continuous
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.FocalLength.TagWhiteBalance">
            <summary>
            0 = Auto
            1 = Sunny
            2 = Cloudy
            3 = Tungsten
            4 = Florescent
            5 = Flash
            6 = Custom
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CanonMakernoteDirectory.FocalLength.TagFlashBias">
            <summary>The value of this tag may be translated into a flash bias value, in EV.</summary>
            <remarks>
            The value of this tag may be translated into a flash bias value, in EV.
            0xffc0 = -2 EV
            0xffcc = -1.67 EV
            0xffd0 = -1.5 EV
            0xffd4 = -1.33 EV
            0xffe0 = -1 EV
            0xffec = -0.67 EV
            0xfff0 = -0.5 EV
            0xfff4 = -0.33 EV
            0x0000 = 0 EV
            0x000c = 0.33 EV
            0x0010 = 0.5 EV
            0x0014 = 0.67 EV
            0x0020 = 1 EV
            0x002c = 1.33 EV
            0x0030 = 1.5 EV
            0x0034 = 1.67 EV
            0x0040 = 2 EV
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.CasioType1MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.CasioType1MakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.CasioType1MakernoteDirectory">
            <summary>Describes tags specific to Casio (type 1) cameras.</summary>
            <remarks>
            Describes tags specific to Casio (type 1) cameras.
            A standard TIFF IFD directory but always uses Motorola (Big-Endian) Byte Alignment.
            Makernote data begins immediately (no header).
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory">
            <summary>Describes tags specific to Casio (type 2) cameras.</summary>
            <remarks>
            A standard TIFF IFD directory but always uses Motorola (Big-Endian) Byte Alignment.
            Makernote data begins after a 6-byte header: <c>"QVC\x00\x00\x00"</c>.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagThumbnailDimensions">
            <summary>2 values - x,y dimensions in pixels.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagThumbnailSize">
            <summary>Size in bytes</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagThumbnailOffset">
            <summary>Offset of Preview Thumbnail</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagQualityMode">
            <summary>
            1 = Fine
            2 = Super Fine
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagImageSize">
            <summary>
            0 = 640 x 480 pixels
            4 = 1600 x 1200 pixels
            5 = 2048 x 1536 pixels
            20 = 2288 x 1712 pixels
            21 = 2592 x 1944 pixels
            22 = 2304 x 1728 pixels
            36 = 3008 x 2008 pixels
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagFocusMode1">
            <summary>
            0 = Normal
            1 = Macro
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagIsoSensitivity">
            <summary>
            3 = 50
            4 = 64
            6 = 100
            9 = 200
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagWhiteBalance1">
            <summary>
            0 = Auto
            1 = Daylight
            2 = Shade
            3 = Tungsten
            4 = Fluorescent
            5 = Manual
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagFocalLength">
            <summary>Units are tenths of a millimetre</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagSaturation">
            <summary>
            0 = -1
            1 = Normal
            2 = +1
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagContrast">
            <summary>
            0 = -1
            1 = Normal
            2 = +1
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagSharpness">
            <summary>
            0 = -1
            1 = Normal
            2 = +1
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagPrintImageMatchingInfo">
            <summary>See PIM specification here: http://www.ozhiker.com/electronics/pjmt/jpeg_info/pim.html</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagPreviewThumbnail">
            <summary>Alternate thumbnail offset</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagWhiteBalance2">
            <summary>
            12 = Flash
            0 = Manual
            1 = Auto?
            4 = Flash?
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagObjectDistance">
            <summary>Units are millimetres</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagFlashDistance">
            <summary>0 = Off</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagRecordMode">
            <summary>2 = Normal Mode</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagSelfTimer">
            <summary>1 = Off?</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagQuality">
            <summary>3 = Fine</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagFocusMode2">
            <summary>
            1 = Fixation
            6 = Multi-Area Auto Focus
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagTimeZone">
            <summary>(string)</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagCcdIsoSensitivity">
            <summary>
            0 = Off
            1 = On?
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagColourMode">
            <summary>0 = Off</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagEnhancement">
            <summary>0 = Off</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.CasioType2MakernoteDirectory.TagFilter">
            <summary>0 = Off</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory"/>.
            </summary>
            <remarks>Using information from https://metacpan.org/pod/distribution/Image-ExifTool/lib/Image/ExifTool/TagNames.pod#DJI-Tags</remarks>
            <author>Charlie Matherne, adapted from Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory">
            <summary>Describes tags specific to DJI aircraft cameras.</summary>
            <remarks>Using information from https://metacpan.org/pod/distribution/Image-ExifTool/lib/Image/ExifTool/TagNames.pod#DJI-Tags</remarks>
            <author>Charlie Matherne, adapted from Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetAircraftSpeedX">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the x speed,
            of the aircraft, at which this image was captured.
            </summary>
            <returns>The x speed of the aircraft for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetAircraftSpeedY">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the y speed,
            of the aircraft, at which this image was captured.
            </summary>
            <returns>The y speed of the aircraft for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetAircraftSpeedZ">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the z speed,
            of the aircraft, at which this image was captured.
            </summary>
            <returns>The z speed of the aircraft for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetAircraftPitch">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the pitch,
            of the aircraft, at which this image was captured.
            </summary>
            <returns>The pitch of the aircraft for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetAircraftYaw">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the yaw,
            of the aircraft, at which this image was captured.
            </summary>
            <returns>The yaw of the aircraft for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetAircraftRoll">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the roll,
            of the aircraft, at which this image was captured.
            </summary>
            <returns>The roll of the aircraft for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetCameraPitch">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the pitch,
            of the camera, at which this image was captured.
            </summary>
            <returns>The pitch of the camera for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetCameraYaw">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the yaw,
            of the camera, at which this image was captured.
            </summary>
            <returns>The yaw of the camera for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.DjiMakernoteDirectory.GetCameraRoll">
            <summary>
            Parses various tags in an attempt to obtain a single object representing the roll,
            of the camera, at which this image was captured.
            </summary>
            <returns>The roll of the camera for this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.FujifilmMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.FujifilmMakernoteDirectory"/>.
            </summary>
            <summary>
            Fujifilm added their Makernote tag from the Year 2000's models (e.g.Finepix1400,
            Finepix4700). It uses IFD format and start from ASCII character 'FUJIFILM', and next 4
            bytes (value 0x000c) points the offset to first IFD entry.
            <code>
            :0000: 46 55 4A 49 46 49 4C 4D-0C 00 00 00 0F 00 00 00 :0000: FUJIFILM........
            :0010: 07 00 04 00 00 00 30 31-33 30 00 10 02 00 08 00 :0010: ......0130......
            </code>
            There are two big differences to the other manufacturers.
            <list type="bullet">
            <item>Fujifilm's Exif data uses Motorola align, but Makernote ignores it and uses Intel align.</item>
            <item>
            The other manufacturer's Makernote counts the "offset to data" from the first byte of TIFF header
            (same as the other IFD), but Fujifilm counts it from the first byte of Makernote itself.
            </item>
            </list>
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.FujifilmMakernoteDirectory">
            <summary>Describes tags specific to Fujifilm cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.FujifilmMakernoteDirectory.TagFacePositions">
            <summary>Left, top, right and bottom coordinates in full-sized image for each face detected.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.KodakMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.KodakMakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.KodakMakernoteDirectory">
            <summary>Describes tags specific to Kodak cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.KyoceraMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.KyoceraMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some information about this makernote taken from here:
            http://www.ozhiker.com/electronics/pjmt/jpeg_info/kyocera_mn.html
            <para />
            Most manufacturer's Makernote counts the "offset to data" from the first byte
            of TIFF header (same as the other IFD), but Kyocera (along with Fujifilm) counts
            it from the first byte of Makernote itself.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.KyoceraMakernoteDirectory">
            <summary>Describes tags specific to Kyocera and Contax cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.LeicaMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.LeicaMakernoteDirectory"/>.
            <para />
            Tag reference from: http://gvsoft.homedns.org/exif/makernote-leica-type1.html
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.LeicaMakernoteDirectory">
            <summary>Describes tags specific to certain Leica cameras.</summary>
            <remarks>
            Tag reference from: http://gvsoft.homedns.org/exif/makernote-leica-type1.html
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.LeicaType5MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.LeicaType5MakernoteDirectory"/>.
            <para />
            Tag reference from: http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Panasonic.html
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.LeicaType5MakernoteDescriptor.GetExposureModeDescription">
            <summary>
            4 values
            </summary>
            <returns></returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.LeicaType5MakernoteDirectory">
            <summary>Describes tags specific to certain Leica cameras.</summary>
            <remarks>
            Tag reference from: http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Panasonic.html
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.NikonType1MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.NikonType1MakernoteDirectory"/>.
            </summary>
            <remarks>
            Type-1 is for E-Series cameras prior to (not including) E990.  For example: E700, E800, E900,
            E900S, E910, E950.
            <para />
            Makernote starts from ASCII string "Nikon". Data format is the same as IFD, but it starts from
            offset 0x08. This is the same as Olympus except start string. Example of actual data
            structure is shown below.
            <pre><c>
            :0000: 4E 69 6B 6F 6E 00 01 00-05 00 02 00 02 00 06 00 Nikon...........
            :0010: 00 00 EC 02 00 00 03 00-03 00 01 00 00 00 06 00 ................
            </c></pre>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.NikonType1MakernoteDirectory">
            <summary>Describes tags specific to Nikon (type 1) cameras.</summary>
            <remarks>
            Describes tags specific to Nikon (type 1) cameras.  Type-1 is for E-Series cameras prior to (not including) E990.
            There are 3 formats of Nikon's Makernote. Makernote of E700/E800/E900/E900S/E910/E950
            starts from ASCII string "Nikon". Data format is the same as IFD, but it starts from
            offset 0x08. This is the same as Olympus except start string. Example of actual data
            structure is shown below.
            <pre><c>
            :0000: 4E 69 6B 6F 6E 00 01 00-05 00 02 00 02 00 06 00 Nikon...........
            :0010: 00 00 EC 02 00 00 03 00-03 00 01 00 00 00 06 00 ................
            </c></pre>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory"/>.
            Type-2 applies to the E990 and D-series cameras such as the D1, D70 and D100.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory">
            <summary>Describes tags specific to Nikon (type 2) cameras.</summary>
            <remarks>
            Type-2 applies to the E990 and D-series cameras such as the E990, D1, D70 and D100.
            <para />
            Thanks to Fabrizio Giudici for publishing his reverse-engineering of the D100 makernote data.
            http://www.timelesswanderings.net/equipment/D100/NEF.html
            <para />
            Note that the camera implements image protection (locking images) via the file's 'readonly' attribute.  Similarly
            image hiding uses the 'hidden' attribute (observed on the D70).  Consequently, these values are not available here.
            <para />
            Additional sample images have been observed, and their tag values recorded in API documentation for each tag's field.
            New tags have subsequently been added since Fabrizio's observations.
            <para />
            In earlier models (such as the E990 and D1), this directory begins at the first byte of the makernote IFD.  In
            later models, the IFD was given the standard prefix to indicate the camera models (most other manufacturers also
            provide this prefix to aid in software decoding).
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagFirmwareVersion">
            <summary>
            Values observed
            - 0200 (D70)
            - 0200 (D1X)
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagIso1">
            <summary>
            Values observed
            - 0 250
            - 0 400
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagColorMode">
            <summary>The camera's color mode, as an uppercase string.</summary>
            <remarks>
            The camera's color mode, as an uppercase string.  Examples include:
            <list type="bullet">
            <item><c>B &amp; W</c></item>
            <item><c>COLOR</c></item>
            <item><c>COOL</c></item>
            <item><c>SEPIA</c></item>
            <item><c>VIVID</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagQualityAndFileFormat">
            <summary>The camera's quality setting, as an uppercase string.</summary>
            <remarks>
            The camera's quality setting, as an uppercase string.  Examples include:
            <list type="bullet">
            <item><c>BASIC</c></item>
            <item><c>FINE</c></item>
            <item><c>NORMAL</c></item>
            <item><c>RAW</c></item>
            <item><c>RAW2.7M</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraWhiteBalance">
            <summary>The camera's white balance setting, as an uppercase string.</summary>
            <remarks>
            The camera's white balance setting, as an uppercase string.  Examples include:
            <list type="bullet">
            <item><c>AUTO</c></item>
            <item><c>CLOUDY</c></item>
            <item><c>FLASH</c></item>
            <item><c>FLUORESCENT</c></item>
            <item><c>INCANDESCENT</c></item>
            <item><c>PRESET</c></item>
            <item><c>PRESET0</c></item>
            <item><c>PRESET1</c></item>
            <item><c>PRESET3</c></item>
            <item><c>SUNNY</c></item>
            <item><c>WHITE PRESET</c></item>
            <item><c>4350K</c></item>
            <item><c>5000K</c></item>
            <item><c>DAY WHITE FL</c></item>
            <item><c>SHADE</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraSharpening">
            <summary>The camera's sharpening setting, as an uppercase string.</summary>
            <remarks>
            The camera's sharpening setting, as an uppercase string.  Examples include:
            <list type="bullet">
            <item><c>AUTO</c></item>
            <item><c>HIGH</c></item>
            <item><c>LOW</c></item>
            <item><c>NONE</c></item>
            <item><c>NORMAL</c></item>
            <item><c>MED.H</c></item>
            <item><c>MED.L</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAfType">
            <summary>The camera's auto-focus mode, as an uppercase string.</summary>
            <remarks>
            The camera's auto-focus mode, as an uppercase string.  Examples include:
            <list type="bullet">
            <item><c>AF-C</c></item>
            <item><c>AF-S</c></item>
            <item><c>MANUAL</c></item>
            <item><c>AF-A</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagFlashSyncMode">
            <summary>The camera's flash setting, as an uppercase string.</summary>
            <remarks>
            The camera's flash setting, as an uppercase string.  Examples include:
            <list type="bullet">
            <item><c></c></item>
            <item><c>NORMAL</c></item>
            <item><c>RED-EYE</c></item>
            <item><c>SLOW</c></item>
            <item><c>NEW_TTL</c></item>
            <item><c>REAR</c></item>
            <item><c>REAR SLOW</c></item>
            </list>
            Note: when TAG_AUTO_FLASH_MODE is blank (whitespace), Nikon Browser displays "Flash Sync Mode: Not Attached"
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAutoFlashMode">
            <summary>The type of flash used in the photograph, as a string.</summary>
            <remarks>
            The type of flash used in the photograph, as a string.  Examples include:
            <list type="bullet">
            <item><c></c></item>
            <item><c>Built-in,TTL</c></item>
            <item><c>NEW_TTL</c> Nikon Browser interprets as "D-TTL"</item>
            <item><c>Built-in,M</c></item>
            <item><c>Optional,TTL</c> with speedlight SB800, flash sync mode as "NORMAL"</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagUnknown34">
            <summary>An unknown tag, as a rational.</summary>
            <remarks>
            An unknown tag, as a rational.  Several values given here:
            http://gvsoft.homedns.org/exif/makernote-nikon-type2.html#0x000b
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraWhiteBalanceFine">
            <summary>The camera's white balance bias setting, as an uint16 array having either one or two elements.</summary>
            <remarks>
            The camera's white balance bias setting, as an uint16 array having either one or two elements.
            <list type="bullet">
            <item><c>0</c></item>
            <item><c>1</c></item>
            <item><c>-3</c></item>
            <item><c>-2</c></item>
            <item><c>-1</c></item>
            <item><c>0,0</c></item>
            <item><c>1,0</c></item>
            <item><c>5,-5</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraWhiteBalanceRbCoeff">
            <summary>
            The first two numbers are coefficients to multiply red and blue channels according to white balance as set in the
            camera.
            </summary>
            <remarks>
            The first two numbers are coefficients to multiply red and blue channels according to white balance as set in the
            camera. The meaning of the third and the fourth numbers is unknown.
            Values observed
            - 2.25882352 1.76078431 0.0 0.0
            - 10242/1 34305/1 0/1 0/1
            - 234765625/100000000 1140625/1000000 1/1 1/1
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagProgramShift">
            <summary>The camera's program shift setting, as an array of four integers.</summary>
            <remarks>
            The camera's program shift setting, as an array of four integers.
            The value, in EV, is calculated as <c>a*b/c</c>.
            <list type="bullet">
            <item><c>0,1,3,0</c> = 0 EV</item>
            <item><c>1,1,3,0</c> = 0.33 EV</item>
            <item><c>-3,1,3,0</c> = -1 EV</item>
            <item><c>1,1,2,0</c> = 0.5 EV</item>
            <item><c>2,1,6,0</c> = 0.33 EV</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagExposureDifference">
            <summary>The exposure difference, as an array of four integers.</summary>
            <remarks>
            The exposure difference, as an array of four integers.
            The value, in EV, is calculated as <c>a*b/c</c>.
            <list type="bullet">
            <item><c>-105,1,12,0</c> = -8.75 EV</item>
            <item><c>-72,1,12,0</c> = -6.00 EV</item>
            <item><c>-11,1,12,0</c> = -0.92 EV</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagIsoMode">
            <summary>The camera's ISO mode, as an uppercase string.</summary>
            <remarks>
            The camera's ISO mode, as an uppercase string.
            <list type="bullet">
            <item><c>AUTO</c></item>
            <item><c>MANUAL</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagDataDump">
            <summary>Added during merge of Type2 &amp; Type3.</summary>
            <remarks>Added during merge of Type2 &amp; Type3.  May apply to earlier models, such as E990 and D1.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagPreviewIfd">
            <summary>
            Preview to another IFD (?)
            <para />
            Details here: http://gvsoft.homedns.org/exif/makernote-nikon-2-tag0x0011.html
            // TODO if this is another IFD, decode it
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAutoFlashCompensation">
            <summary>The flash compensation, as an array of four integers.</summary>
            <remarks>
            The flash compensation, as an array of four integers.
            The value, in EV, is calculated as <c>a*b/c</c>.
            <list type="bullet">
            <item><c>-18,1,6,0</c> = -3 EV</item>
            <item><c>4,1,6,0</c> = 0.67 EV</item>
            <item><c>6,1,6,0</c> = 1 EV</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagIsoRequested">
            <summary>The requested ISO value, as an array of two integers.</summary>
            <remarks>
            The requested ISO value, as an array of two integers.
            <list type="bullet">
            <item><c>0,0</c></item>
            <item><c>0,125</c></item>
            <item><c>1,2500</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagImageBoundary">
            <summary>Defines the photo corner coordinates, in 8 bytes.</summary>
            <remarks>
            Defines the photo corner coordinates, in 8 bytes.  Treated as four 16-bit integers, they
            decode as: top-left (x,y); bot-right (x,y)
            - 0 0 49163 53255
            - 0 0 3008 2000 (the image dimensions were 3008x2000) (D70)
            <list type="bullet">
            <item><c>0,0,4288,2848</c> The max resolution of the D300 camera</item>
            <item><c>0,0,3008,2000</c> The max resolution of the D70 camera</item>
            <item><c>0,0,4256,2832</c> The max resolution of the D3 camera</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagFlashExposureCompensation">
            <summary>The flash exposure compensation, as an array of four integers.</summary>
            <remarks>
            The flash exposure compensation, as an array of four integers.
            The value, in EV, is calculated as <c>a*b/c</c>.
            <list type="bullet">
            <item><c>0,0,0,0</c> = 0 EV</item>
            <item><c>0,1,6,0</c> = 0 EV</item>
            <item><c>4,1,6,0</c> = 0.67 EV</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagFlashBracketCompensation">
            <summary>The flash bracket compensation, as an array of four integers.</summary>
            <remarks>
            The flash bracket compensation, as an array of four integers.
            The value, in EV, is calculated as <c>a*b/c</c>.
            <list type="bullet">
            <item><c>0,0,0,0</c> = 0 EV</item>
            <item><c>0,1,6,0</c> = 0 EV</item>
            <item><c>4,1,6,0</c> = 0.67 EV</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAeBracketCompensation">
            <summary>The AE bracket compensation, as a rational number.</summary>
            <remarks>
            The AE bracket compensation, as a rational number.
            <list type="bullet">
            <item><c>0/0</c></item>
            <item><c>0/1</c></item>
            <item><c>0/6</c></item>
            <item><c>4/6</c></item>
            <item><c>6/6</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagFlashMode">
            <summary>Flash mode, as a string.</summary>
            <remarks>
            Flash mode, as a string.
            <list type="bullet">
            <item><c></c></item>
            <item><c>Red Eye Reduction</c></item>
            <item><c>D-Lighting</c></item>
            <item><c>Distortion control</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraSerialNumber">
            <summary>The camera's serial number, as a string.</summary>
            <remarks>
            The camera's serial number, as a string.
            Note that D200 is always blank, and D50 is always <c>"D50"</c>.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagColorSpace">
            <summary>The camera's color space setting.</summary>
            <remarks>
            The camera's color space setting.
            <list type="bullet">
            <item><c>1</c> sRGB</item>
            <item><c>2</c> Adobe RGB</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagActiveDLighting">
            <summary>The active D-Lighting setting.</summary>
            <remarks>
            The active D-Lighting setting.
            <list type="bullet">
            <item><c>0</c> Off</item>
            <item><c>1</c> Low</item>
            <item><c>3</c> Normal</item>
            <item><c>5</c> High</item>
            <item><c>7</c> Extra High</item>
            <item><c>65535</c> Auto</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagVignetteControl">
            <summary>The camera's vignette control setting.</summary>
            <remarks>
            The camera's vignette control setting.
            <list type="bullet">
            <item><c>0</c> Off</item>
            <item><c>1</c> Low</item>
            <item><c>3</c> Normal</item>
            <item><c>5</c> High</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagImageAdjustment">
            <summary>The camera's image adjustment setting, as a string.</summary>
            <remarks>
            The camera's image adjustment setting, as a string.
            <list type="bullet">
            <item><c>AUTO</c></item>
            <item><c>CONTRAST(+)</c></item>
            <item><c>CONTRAST(-)</c></item>
            <item><c>NORMAL</c></item>
            <item><c>B &amp; W</c></item>
            <item><c>BRIGHTNESS(+)</c></item>
            <item><c>BRIGHTNESS(-)</c></item>
            <item><c>SEPIA</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraToneCompensation">
            <summary>The camera's tone compensation setting, as a string.</summary>
            <remarks>
            The camera's tone compensation setting, as a string.
            <list type="bullet">
            <item><c>NORMAL</c></item>
            <item><c>LOW</c></item>
            <item><c>MED.L</c></item>
            <item><c>MED.H</c></item>
            <item><c>HIGH</c></item>
            <item><c>AUTO</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAdapter">
            <summary>A description of any auxiliary lens, as a string.</summary>
            <remarks>
            A description of any auxiliary lens, as a string.
            <list type="bullet">
            <item><c>OFF</c></item>
            <item><c>FISHEYE 1</c></item>
            <item><c>FISHEYE 2</c></item>
            <item><c>TELEPHOTO 2</c></item>
            <item><c>WIDE ADAPTER</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagLensType">
            <summary>The type of lens used, as a byte.</summary>
            <remarks>
            The type of lens used, as a byte.
            <list type="bullet">
            <item><c>0x00</c> AF</item>
            <item><c>0x01</c> MF</item>
            <item><c>0x02</c> D</item>
            <item><c>0x06</c> G, D</item>
            <item><c>0x08</c> VR</item>
            <item><c>0x0a</c> VR, D</item>
            <item><c>0x0e</c> VR, G, D</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagLens">
            <summary>A pair of focal/max-fstop values that describe the lens used.</summary>
            <remarks>
            A pair of focal/max-fstop values that describe the lens used.
            Values observed
            - 180.0,180.0,2.8,2.8 (D100)
            - 240/10 850/10 35/10 45/10
            - 18-70mm f/3.5-4.5 (D70)
            - 17-35mm f/2.8-2.8 (D1X)
            - 70-200mm f/2.8-2.8 (D70)
            Nikon Browser identifies the lens as "18-70mm F/3.5-4.5 G" which
            is identical to metadata extractor, except for the "G".  This must
            be coming from another tag...
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagManualFocusDistance">
            <summary>Added during merge of Type2 &amp; Type3.</summary>
            <remarks>Added during merge of Type2 &amp; Type3.  May apply to earlier models, such as E990 and D1.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagDigitalZoom">
            <summary>The amount of digital zoom used.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagFlashUsed">
            <summary>Whether the flash was used in this image.</summary>
            <remarks>
            Whether the flash was used in this image.
            <list type="bullet">
            <item><c>0</c> Flash Not Used</item>
            <item><c>1</c> Manual Flash</item>
            <item><c>3</c> Flash Not Ready</item>
            <item><c>7</c> External Flash</item>
            <item><c>8</c> Fired, Commander Mode</item>
            <item><c>9</c> Fired, TTL Mode</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAfFocusPosition">
            <summary>The position of the autofocus target.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagShootingMode">
            <summary>The camera's shooting mode.</summary>
            <remarks>
            The camera's shooting mode.
            <para />
            A bit-array with:
            <list type="bullet">
            <item><c>0</c> Single Frame</item>
            <item><c>1</c> Continuous</item>
            <item><c>2</c> Delay</item>
            <item><c>8</c> PC Control</item>
            <item><c>16</c> Exposure Bracketing</item>
            <item><c>32</c> Auto ISO</item>
            <item><c>64</c> White-Balance Bracketing</item>
            <item><c>128</c> IR Control</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagLensStops">
            <summary>Lens stops, as an array of four integers.</summary>
            <remarks>
            Lens stops, as an array of four integers.
            The value, in EV, is calculated as <c>a*b/c</c>.
            <list type="bullet">
            <item><c>64,1,12,0</c> = 5.33 EV</item>
            <item><c>72,1,12,0</c> = 6 EV</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraColorMode">
            <summary>The color space as set in the camera, as a string.</summary>
            <remarks>
            The color space as set in the camera, as a string.
            <list type="bullet">
            <item><c>MODE1</c> = Mode 1 (sRGB)</item>
            <item><c>MODE1a</c> = Mode 1 (sRGB)</item>
            <item><c>MODE2</c> = Mode 2 (Adobe RGB)</item>
            <item><c>MODE3</c> = Mode 2 (sRGB): Higher Saturation</item>
            <item><c>MODE3a</c> = Mode 2 (sRGB): Higher Saturation</item>
            <item><c>B &amp; W</c> = B &amp; W</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagSceneMode">
            <summary>The camera's scene mode, as a string.</summary>
            <remarks>
            The camera's scene mode, as a string.  Examples include:
            <list type="bullet">
            <item><c>BEACH/SNOW</c></item>
            <item><c>CLOSE UP</c></item>
            <item><c>NIGHT PORTRAIT</c></item>
            <item><c>PORTRAIT</c></item>
            <item><c>ANTI-SHAKE</c></item>
            <item><c>BACK LIGHT</c></item>
            <item><c>BEST FACE</c></item>
            <item><c>BEST</c></item>
            <item><c>COPY</c></item>
            <item><c>DAWN/DUSK</c></item>
            <item><c>FACE-PRIORITY</c></item>
            <item><c>FIREWORKS</c></item>
            <item><c>FOOD</c></item>
            <item><c>HIGH SENS.</c></item>
            <item><c>LAND SCAPE</c></item>
            <item><c>MUSEUM</c></item>
            <item><c>PANORAMA ASSIST</c></item>
            <item><c>PARTY/INDOOR</c></item>
            <item><c>SCENE AUTO</c></item>
            <item><c>SMILE</c></item>
            <item><c>SPORT</c></item>
            <item><c>SPORT CONT.</c></item>
            <item><c>SUNSET</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagLightSource">
            <summary>The lighting type, as a string.</summary>
            <remarks>
            The lighting type, as a string.  Examples include:
            <list type="bullet">
            <item><c></c></item>
            <item><c>NATURAL</c></item>
            <item><c>SPEEDLIGHT</c></item>
            <item><c>COLORED</c></item>
            <item><c>MIXED</c></item>
            <item><c>NORMAL</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagShotInfo">
            <summary>Advertised as ASCII, but actually isn't.</summary>
            <remarks>
            Advertised as ASCII, but actually isn't.  A variable number of bytes (eg. 18 to 533).  Actual number of bytes
            appears fixed for a given camera model.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraHueAdjustment">
            <summary>The hue adjustment as set in the camera.</summary>
            <remarks>The hue adjustment as set in the camera.  Values observed are either 0 or 3.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagNefCompression">
            <summary>The NEF (RAW) compression.</summary>
            <remarks>
            The NEF (RAW) compression.  Examples include:
            <list type="bullet">
            <item><c>1</c> Lossy (Type 1)</item>
            <item><c>2</c> Uncompressed</item>
            <item><c>3</c> Lossless</item>
            <item><c>4</c> Lossy (Type 2)</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagSaturation">
            <summary>The saturation level, as a signed integer.</summary>
            <remarks>
            The saturation level, as a signed integer.  Examples include:
            <list type="bullet">
            <item><c>+3</c></item>
            <item><c>+2</c></item>
            <item><c>+1</c></item>
            <item><c>0</c> Normal</item>
            <item><c>-1</c></item>
            <item><c>-2</c></item>
            <item><c>-3</c> (B&amp;W)</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagNoiseReduction">
            <summary>The type of noise reduction, as a string.</summary>
            <remarks>
            The type of noise reduction, as a string.  Examples include:
            <list type="bullet">
            <item><c>OFF</c></item>
            <item><c>FPNR</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagNefThumbnailSize">
            <summary>The NEF (RAW) thumbnail size, as an integer array with two items representing [width,height].</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagSensorPixelSize">
            <summary>The sensor pixel size, as a pair of rational numbers.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagCameraSerialNumber2">
            <summary>The camera serial number, as a string.</summary>
            <remarks>
            The camera serial number, as a string.
            <list type="bullet">
            <item><c>NO= 00002539</c></item>
            <item><c>NO= -1000d71</c></item>
            <item><c>PKG597230621263</c></item>
            <item><c>PKG5995671330625116</c></item>
            <item><c>PKG49981281631130677</c></item>
            <item><c>BU672230725063</c></item>
            <item><c>NO= 200332c7</c></item>
            <item><c>NO= 30045efe</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagExposureSequenceNumber">
            <summary>The number of total shutter releases.</summary>
            <remarks>The number of total shutter releases.  This value increments for each exposure (observed on D70).</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagImageOptimisation">
            <summary>The camera's image optimisation, as a string.</summary>
            <remarks>
            The camera's image optimisation, as a string.
            <list type="bullet">
            <item><c></c></item>
            <item><c>NORMAL</c></item>
            <item><c>CUSTOM</c></item>
            <item><c>BLACK AND WHITE</c></item>
            <item><c>LAND SCAPE</c></item>
            <item><c>MORE VIVID</c></item>
            <item><c>PORTRAIT</c></item>
            <item><c>SOFT</c></item>
            <item><c>VIVID</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagSaturation2">
            <summary>The camera's saturation level, as a string.</summary>
            <remarks>
            The camera's saturation level, as a string.
            <list type="bullet">
            <item><c></c></item>
            <item><c>NORMAL</c></item>
            <item><c>AUTO</c></item>
            <item><c>ENHANCED</c></item>
            <item><c>MODERATE</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagDigitalVariProgram">
            <summary>The camera's digital vari-program setting, as a string.</summary>
            <remarks>
            The camera's digital vari-program setting, as a string.
            <list type="bullet">
            <item><c></c></item>
            <item><c>AUTO</c></item>
            <item><c>AUTO(FLASH OFF)</c></item>
            <item><c>CLOSE UP</c></item>
            <item><c>LANDSCAPE</c></item>
            <item><c>NIGHT PORTRAIT</c></item>
            <item><c>PORTRAIT</c></item>
            <item><c>SPORT</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagImageStabilisation">
            <summary>The camera's digital vari-program setting, as a string.</summary>
            <remarks>
            The camera's digital vari-program setting, as a string.
            <list type="bullet">
            <item><c></c></item>
            <item><c>VR-ON</c></item>
            <item><c>VR-OFF</c></item>
            <item><c>VR-HYBRID</c></item>
            <item><c>VR-ACTIVE</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagAfResponse">
            <summary>The camera's digital vari-program setting, as a string.</summary>
            <remarks>
            The camera's digital vari-program setting, as a string.
            <list type="bullet">
            <item><c></c></item>
            <item><c>HYBRID</c></item>
            <item><c>STANDARD</c></item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagHighIsoNoiseReduction">
            <summary>The camera's high ISO noise reduction setting, as an integer.</summary>
            <remarks>
            The camera's high ISO noise reduction setting, as an integer.
            <list type="bullet">
            <item><c>0</c> Off</item>
            <item><c>1</c> Minimal</item>
            <item><c>2</c> Low</item>
            <item><c>4</c> Normal</item>
            <item><c>6</c> High</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.NikonType2MakernoteDirectory.TagNikonCaptureData">
            <summary>Data about changes set by Nikon Capture Editor.</summary>
            <remarks>
            Data about changes set by Nikon Capture Editor.
            Values observed
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDirectory"/>.
            </summary>
            <remarks>
            Many Description functions and the Filter type list converted from Exiftool version 10.10 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetAfAreasDescription">
            <summary>
            coordinates range from 0 to 255
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetAfPointSelectedDescription">
            <summary>
            coordinates expressed as a percent
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetFlashControlModeDescription">
            <summary>
            3 or 4 values
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetFlashIntensityDescription">
            <summary>
            3 or 4 values
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetGradationDescription">
            <summary>
            3 or 4 values
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetPictureModeDescription">
            <summary>
            1 or 2 values
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetDriveModeDescription">
            <summary>
            2 or 3 numbers: 1. Mode, 2. Shot number, 3. Mode bits
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetPanoramaModeDescription">
            <summary>
            2 numbers: 1. Mode, 2. Shot number
            </summary>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetManometerPressureDescription">
            <remarks>
            TODO: need better image examples to test this function
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetManometerReadingDescription">
            <remarks>
            TODO: need better image examples to test this function
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetRollAngleDescription">
            <summary>
            converted to degrees of clockwise camera rotation
            </summary>
            <remarks>
            TODO: need better image examples to test this function
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDescriptor.GetPitchAngleDescription">
            <summary>
            converted to degrees of upward camera tilt
            </summary>
            <remarks>
            TODO: need better image examples to test this function
            </remarks>
            <returns></returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusCameraSettingsMakernoteDirectory">
            <summary>
            The Olympus camera settings makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusEquipmentMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusEquipmentMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some Description functions and the Extender and Lens types lists converted from Exiftool version 10.10 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusEquipmentMakernoteDirectory">
            <summary>
            The Olympus equipment makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some Description functions converted from Exiftool version 10.10 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDescriptor.GetFocusDistanceDescription">
            <remarks>
            this rational value looks like it is in mm when the denominator is
            1 (E-1), and cm when denominator is 10 (E-300), so if we ignore the
            denominator we are consistently in mm - PH
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDescriptor.GetAfPointDescription">
            <remarks>
            <para>TODO: Complete when Camera Model is available.</para>
            <para>There are differences in how to interpret this tag that can only be reconciled by knowing the model.</para>
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDescriptor.GetSensorTemperatureDescription">
            <remarks>
            <para>TODO: Complete when Camera Model is available.</para>
            <para>There are differences in how to interpret this tag that can only be reconciled by knowing the model.</para>
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDescriptor.GetImageStabilizationDescription">
            <remarks>
            <para> if the first 4 bytes are non-zero, then bit 0x01 of byte 44 gives the stabilization mode</para>
            <notes>(the other value is more reliable, so ignore this totally if the other exists)</notes>
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusFocusInfoMakernoteDirectory">
            <summary>
            The Olympus focus info makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusImageProcessingMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusImageProcessingMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some Description functions converted from Exiftool version 10.33 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusImageProcessingMakernoteDirectory">
            <summary>
            The Olympus image processing makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory">
            <summary>
            The Olympus makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagMakernoteVersion">
            <summary>Used by Konica / Minolta cameras.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagCameraSettings1">
            <summary>Used by Konica / Minolta cameras.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagCameraSettings2">
            <summary>Alternate Camera Settings Tag.</summary>
            <remarks>Alternate Camera Settings Tag. Used by Konica / Minolta cameras.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagCompressedImageSize">
            <summary>Used by Konica / Minolta cameras.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagMinoltaThumbnailOffset1">
            <summary>Used by Konica / Minolta cameras.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagMinoltaThumbnailOffset2">
            <summary>Alternate Thumbnail Offset.</summary>
            <remarks>Alternate Thumbnail Offset. Used by Konica / Minolta cameras.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagMinoltaThumbnailLength">
            <summary>Length of thumbnail in bytes.</summary>
            <remarks>Length of thumbnail in bytes. Used by Konica / Minolta cameras.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagColourMode">
            <summary>
            Used by Konica / Minolta cameras
            0 = Natural Colour
            1 = Black &amp; White
            2 = Vivid colour
            3 = Solarization
            4 = AdobeRGB
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagImageQuality1">
            <summary>Used by Konica / Minolta cameras.</summary>
            <remarks>
            Used by Konica / Minolta cameras.
            0 = Raw
            1 = Super Fine
            2 = Fine
            3 = Standard
            4 = Extra Fine
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagImageQuality2">
            <summary>Not 100% sure about this tag.</summary>
            <remarks>
            Not 100% sure about this tag.
            <para />
            Used by Konica / Minolta cameras.
            0 = Raw
            1 = Super Fine
            2 = Fine
            3 = Standard
            4 = Extra Fine
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagSpecialMode">
            <summary>
            Three values:
            Value 1: 0=Normal, 2=Fast, 3=Panorama
            Value 2: Sequence Number Value 3:
            1 = Panorama Direction: Left to Right
            2 = Panorama Direction: Right to Left
            3 = Panorama Direction: Bottom to Top
            4 = Panorama Direction: Top to Bottom
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagJpegQuality">
            <summary>
            1 = Standard Quality
            2 = High Quality
            3 = Super High Quality
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagMacroMode">
            <summary>
            0 = Normal (Not Macro)
            1 = Macro
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagBwMode">
            <summary>0 = Off, 1 = On</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagImageWidth">
            <summary>
            Used by Epson cameras
            Units = pixels
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagImageHeight">
            <summary>
            Used by Epson cameras
            Units = pixels
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagOriginalManufacturerModel">
            <summary>A string.</summary>
            <remarks>A string. Used by Epson cameras.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.TagPrintImageMatchingInfo">
            <summary>
            See the PIM specification here:
            http://www.ozhiker.com/electronics/pjmt/jpeg_info/pim.html
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.OlympusMakernoteDirectory.OlympusCameraTypes">
            <summary>
            These values are currently decoded only for Olympus models.  Models with
            Olympus-style maker notes from other brands such as Acer, BenQ, Hitachi, HP,
            Premier, Konica-Minolta, Maginon, Ricoh, Rollei, SeaLife, Sony, Supra,
            Vivitar are not listed.
            </summary>
            <remarks>
            Converted from Exiftool version 10.33 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawDevelopment2MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawDevelopment2MakernoteDirectory"/>.
            </summary>
            <remarks>
            Some Description functions and the Filter type list converted from Exiftool version 10.10 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawDevelopment2MakernoteDirectory">
            <summary>
            The Olympus raw development 2 makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawDevelopmentMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusEquipmentMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some Description functions converted from Exiftool version 10.10 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawDevelopmentMakernoteDirectory">
            <summary>
            The Olympus raw development makernote is used by many manufacturers (Epson, Konica, Minolta and Agfa...), and as such contains some tags
            that appear specific to those manufacturers.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawInfoMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawInfoMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some Description functions converted from Exiftool version 10.33 created by Phil Harvey
            http://www.sno.phy.queensu.ca/~phil/exiftool/
            lib\Image\ExifTool\Olympus.pm
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.OlympusRawInfoMakernoteDirectory">
            <summary>
            These tags are found only in ORF images of some models (eg. C8080WZ)
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some information about this makernote taken from here:
            <list type="bullet">
              <item><a href="http://www.ozhiker.com/electronics/pjmt/jpeg_info/panasonic_mn.html">http://www.ozhiker.com/electronics/pjmt/jpeg_info/panasonic_mn.html</a></item>
              <item><a href="http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Panasonic.html">http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Panasonic.html</a></item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Philipp Sandhaus</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory">
            <summary>Describes tags specific to Panasonic and Leica cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Philipp Sandhaus</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagQualityMode">
            <summary>
            2 = High
            3 = Normal
            6 = Very High
            7 = Raw
            9 = Motion Picture
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagWhiteBalance">
            <summary>
            1 = Auto
            2 = Daylight
            3 = Cloudy
            4 = Incandescent
            5 = Manual
            8 = Flash
            10 = Black &amp; White
            11 = Manual
            12 = Shade
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagFocusMode">
            <summary>
            1 = Auto
            2 = Manual
            4 =  Auto, Focus Button
            5 = Auto, Continuous
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagAfAreaMode">
            <summary>
            2 bytes
            (DMC-FZ10)
            '0 1' = Spot Mode On
            '0 16' = Spot Mode Off
            '(other models)
            16 = Normal?
            '0 1' = 9-area
            '0 16' = 3-area (high speed)
            '1 0' = Spot Focusing
            '1 1' = 5-area
            '16 0' = 1-area
            '16 16' = 1-area (high speed)
            '32 0' = Auto or Face Detect
            '32 1' = 3-area (left)?
            '32 2' = 3-area (center)?
            '32 3' = 3-area (right)?
            '64 0' = Face Detect
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagImageStabilization">
            <summary>
            2 = On, Mode 1
            3 = Off
            4 = On, Mode 2
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagMacroMode">
            <summary>
            1 = On
            2 = Off
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagRecordMode">
            <summary>
            1 = Normal
            2 = Portrait
            3 = Scenery
            4 = Sports
            5 = Night Portrait
            6 = Program
            7 = Aperture Priority
            8 = Shutter Priority
            9 = Macro
            10= Spot
            11= Manual
            12= Movie Preview
            13= Panning
            14= Simple
            15= Color Effects
            16= Self Portrait
            17= Economy
            18= Fireworks
            19= Party
            20= Snow
            21= Night Scenery
            22= Food
            23= Baby
            24= Soft Skin
            25= Candlelight
            26= Starry Night
            27= High Sensitivity
            28= Panorama Assist
            29= Underwater
            30= Beach
            31= Aerial Photo
            32= Sunset
            33= Pet
            34= Intelligent ISO
            35= Clipboard
            36= High Speed Continuous Shooting
            37= Intelligent Auto
            39= Multi-aspect
            41= Transform
            42= Flash Burst
            43= Pin Hole
            44= Film Grain
            45= My Color
            46= Photo Frame
            51= HDR
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagAudio">
            <summary>
            1 = Yes
            2 = No
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagUnknownDataDump">
            <summary>No idea, what this is</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagInternalSerialNumber">
            <summary>
            this number is unique, and contains the date of manufacture,
            but is not the same as the number printed on the camera body
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagExifVersion">
            <summary>Panasonic Exif Version</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagColorEffect">
            <summary>
            1 = Off
            2 = Warm
            3 = Cool
            4 = Black &amp; White
            5 = Sepia
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagUptime">
            <summary>
            4 Bytes
            Time in 1/100 s from when the camera was powered on to when the
            image is written to memory card
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagBurstMode">
            <summary>
            0 = Off
            1 = On
            2 = Infinite
            4 = Unlimited
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagContrastMode">
            <summary>
            (this decoding seems to work for some models such as the LC1, LX2, FZ7, FZ8, FZ18 and FZ50, but may not be correct for other models such as the FX10, G1, L1, L10 and LC80)
            0x0 = Normal
            0x1 = Low
            0x2 = High
            0x6 = Medium Low
            0x7 = Medium High
            0x100 = Low
            0x110 = Normal
            0x120 = High
            (these values are used by the GF1)
            0 = -2
            1 = -1
            2 = Normal
            3 = +1
            4 = +2
            7 = Nature (Color Film)
            12 = Smooth (Color Film) or Pure (My Color)
            17 = Dynamic (B&amp;W Film)
            22 = Smooth (B&amp;W Film)
            27 = Dynamic (Color Film)
            32 = Vibrant (Color Film) or Expressive (My Color)
            33 = Elegant (My Color)
            37 = Nostalgic (Color Film)
            41 = Dynamic Art (My Color)
            42 = Retro (My Color)
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagNoiseReduction">
            <summary>
            0 = Standard
            1 = Low (-1)
            2 = High (+1)
            3 = Lowest (-2)
            4 = Highest (+2)
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagSelfTimer">
            <summary>
            1 = Off
            2 = 10 s
            3 = 2 s
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagRotation">
            <summary>
            1 = 0 DG
            3 = 180 DG
            6 =  90 DG
            8 = 270 DG
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagAfAssistLamp">
            <summary>
            1 = Fired
            2 = Enabled nut not used
            3 = Disabled but required
            4 = Disabled and not required
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagColorMode">
            <summary>
            0 = Normal
            1 = Natural
            2 = Vivid
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagOpticalZoomMode">
            <summary>
            1 = Standard
            2 = Extended
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagConversionLens">
            <summary>
            1 = Off
            2 = Wide
            3 = Telephoto
            4 = Macro
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagContrast">
            <summary>0 = Normal</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagWorldTimeLocation">
             <summary>
            
             1 = Home
             2 = Destination
             </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagTextStamp">
            <summary>
            1 = Off
            2 = On
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagAdvancedSceneMode">
             <summary>
            
             1 = Normal
             2 = Outdoor/Illuminations/Flower/HDR Art
             3 = Indoor/Architecture/Objects/HDR B&amp;W
             4 = Creative
             5 = Auto
             7 = Expressive
             8 = Retro
             9 = Pure
             10 = Elegant
             12 = Monochrome
             13 = Dynamic Art
             14 = Silhouette
             </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagTextStamp1">
            <summary>
            1 = Off
            2 = On
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagWbAdjustAb">
            <summary>WB adjust AB.</summary>
            <remarks>WB adjust AB. Positive is a shift toward blue.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagWbAdjustGm">
            <summary>WB adjust GM.</summary>
            <remarks>WB adjust GM. Positive is a shift toward green.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagFaceDetectionInfo">
             <summary>
            
             Integer (16Bit) Indexes:
             0  Number Face Positions (maybe less than Faces Detected)
             1-4 Face Position 1
             5-8 Face Position 2
             and so on
            
             The four Integers are interpreted as follows:
             (XYWH)  X,Y Center of Face,  (W,H) Width and Height
             All values are in respect to double the size of the thumbnail image
             </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagTransform">
            <summary>
            (decoded as two 16-bit signed integers)
            '-1 1' = Slim Low
            '-3 2' = Slim High
            '0 0' = Off
            '1 1' = Stretch Low
            '3 2' = Stretch High
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagIntelligentExposure">
            <summary>
            0 = Off
            1 = Low
            2 = Standard
            3 = High
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagPrintImageMatchingInfo">
            <summary>Info at http://www.ozhiker.com/electronics/pjmt/jpeg_info/pim.html</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagFaceRecognitionInfo">
             <summary>
             Byte Indexes:
             0    Int (2  Byte) Number of Recognized Faces
             4    String(20 Byte)    Recognized Face 1 Name
             24    4 Int (8 Byte)     Recognized Face 1 Position  (Same Format as Face Detection)
             32    String(20 Byte)    Recognized Face 1 Age
             52    String(20 Byte)    Recognized Face 2 Name
             72    4 Int (8 Byte)     Recognized Face 2 Position  (Same Format as Face Detection)
             80    String(20 Byte)    Recognized Face 2 Age
            
             And so on
            
             The four Integers are interpreted as follows:
             (XYWH)  X,Y Center of Face,  (W,H) Width and Height
             All values are in respect to double the size of the thumbnail image
             </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagFlashWarning">
            <summary>
            0 = No
            1 = Yes
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagIntelligentResolution">
            <summary>
            0 = Off
            2 = Auto
            3 = On
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.TagTransform1">
            <summary>
            (decoded as two 16-bit signed integers)
            '-1 1' = Slim Low
            '-3 2' = Slim High
            '0 0' = Off
            '1 1' = Stretch Low
            '3 2' = Stretch High
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Exif.Makernotes.PanasonicMakernoteDirectory.GetAge(System.Int32)">
            <summary>Attempts to convert the underlying string value (as stored in the directory) into an Age object.</summary>
            <param name="tag">The tag identifier.</param>
            <returns>The parsed Age object, or null if the tag was empty of the value unable to be parsed.</returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory"/>.
            <para />
            Some information about this makernote taken from here:
            http://www.ozhiker.com/electronics/pjmt/jpeg_info/pentax_mn.html
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory">
            <summary>Describes tags specific to Pentax and Asahi cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagCaptureMode">
            <summary>
            0 = Auto
            1 = Night-scene
            2 = Manual
            4 = Multiple
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagQualityLevel">
            <summary>
            0 = Good
            1 = Better
            2 = Best
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagFocusMode">
            <summary>
            2 = Custom
            3 = Auto
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagFlashMode">
            <summary>
            1 = Auto
            2 = Flash on
            4 = Flash off
            6 = Red-eye Reduction
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagWhiteBalance">
            <summary>
            0 = Auto
            1 = Daylight
            2 = Shade
            3 = Tungsten
            4 = Fluorescent
            5 = Manual
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagDigitalZoom">
            <summary>(0 = Off)</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagSharpness">
            <summary>
            0 = Normal
            1 = Soft
            2 = Hard
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagContrast">
            <summary>
            0 = Normal
            1 = Low
            2 = High
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagSaturation">
            <summary>
            0 = Normal
            1 = Low
            2 = High
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagIsoSpeed">
            <summary>
            10 = ISO 100
            16 = ISO 200
            100 = ISO 100
            200 = ISO 200
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagColour">
            <summary>
            1 = Normal
            2 = Black &amp; White
            3 = Sepia
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagPrintImageMatchingInfo">
            <summary>See Print Image Matching for specification.</summary>
            <remarks>
            See Print Image Matching for specification.
            http://www.ozhiker.com/electronics/pjmt/jpeg_info/pim.html
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagTimeZone">
            <summary>(String).</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.PentaxMakernoteDirectory.TagDaylightSavings">
            <summary>(String).</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFire2MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFire2MakernoteDirectory"/>.
            </summary>
            <remarks>Reconyx uses a fixed makernote block. Tag values are the byte index of the tag within the makernote.</remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFire2MakernoteDirectory">
            <summary>Describes tags specific to Reconyx HyperFire 2 cameras.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFireMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFireMakernoteDirectory"/>.
            </summary>
            <author>Todd West http://cascadescarnivoreproject.blogspot.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFireMakernoteDirectory">
            <summary>Describes tags specific to Reconyx HyperFire cameras.</summary>
            <author>Todd West http://cascadescarnivoreproject.blogspot.com</author>
            <remarks>Reconyx uses a fixed makernote block.  Tag values are the byte index of the tag within the makernote.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFireMakernoteDirectory.MakernoteVersion">
            <summary>
            Version number used for identifying makernotes from Reconyx HyperFire cameras.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxUltraFireMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxHyperFireMakernoteDirectory"/>.
            </summary>
            <author>Todd West http://cascadescarnivoreproject.blogspot.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.ReconyxUltraFireMakernoteDirectory">
            <summary>Describes tags specific to Reconyx UltraFire cameras.</summary>
            <author>Todd West http://cascadescarnivoreproject.blogspot.com</author>
            <remarks>Reconyx uses a fixed makernote block.  Tag values are the byte index of the tag within the makernote.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.ReconyxUltraFireMakernoteDirectory.MakernoteId">
            <summary>
            Version number used for identifying makernotes from Reconyx UltraFire cameras.
            </summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.ReconyxUltraFireMakernoteDirectory.MakernotePublicId">
            <summary>
            Version number used for identifying the public portion of makernotes from Reconyx UltraFire cameras.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.RicohMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.RicohMakernoteDirectory"/>.
            </summary>
            <remarks>
            Some information about this makernote taken from here:
            http://www.ozhiker.com/electronics/pjmt/jpeg_info/ricoh_mn.html
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.RicohMakernoteDirectory">
            <summary>Describes tags specific to Ricoh cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SamsungType2MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.SamsungType2MakernoteDirectory"/>.
            <para />
            Tag reference from: http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Samsung.html
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SamsungType2MakernoteDirectory">
            <summary>Describes tags specific to certain 'newer' Samsung cameras.</summary>
            <remarks>
            Tag reference from: http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/Samsung.html
            </remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SanyoMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType6MakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SanyoMakernoteDirectory">
            <summary>Describes tags specific to Sanyo cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SigmaMakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.SigmaMakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SigmaMakernoteDirectory">
            <summary>Describes tags specific to Sigma / Foveon cameras.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType1MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType1MakernoteDirectory"/>.
            Thanks to David Carson for the initial version of this class.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType1MakernoteDirectory">
            <summary>Describes tags specific to Sony cameras that use the Sony Type 1 makernote tags.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Exif.Makernotes.SonyType1MakernoteDirectory.TagLongExposureNoiseReductionOrFocusMode">
            <summary>
            (FocusMode for RX100)
            0 = Manual
            2 = AF-S
            3 = AF-C
            5 = Semi-manual
            6 = Direct Manual Focus
            (LongExposureNoiseReduction for other models)
            0 = Off
            1 = On
            2 = On 2
            65535 = n/a
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType6MakernoteDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType6MakernoteDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.Makernotes.SonyType6MakernoteDirectory">
            <summary>Describes tags specific to Sony cameras that use the Sony Type 6 makernote tags.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawDistortionDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.PanasonicRawDistortionDirectory"/>.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawDistortionDirectory">
            <remarks>These tags are found in Panasonic/Leica RAW, RW2 and RWL images. The index values are 'fake' but
            chosen specifically to make processing easier</remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawIfd0Descriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.PanasonicRawIfd0Directory"/>.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawIfd0Directory">
            <remarks>These tags are found in IFD0 of Panasonic/Leica RAW, RW2 and RWL images.</remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawWbInfo2Descriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.PanasonicRawWbInfo2Directory"/>.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawWbInfo2Directory">
            <remarks>These tags can be found in Panasonic/Leica RAW, RW2 and RWL images. The index values are 'fake' but
            chosen specifically to make processing easier</remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawWbInfoDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.PanasonicRawWbInfoDirectory"/>.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PanasonicRawWbInfoDirectory">
            <remarks>These tags can be found in Panasonic/Leica RAW, RW2 and RWL images. The index values are 'fake' but
            chosen specifically to make processing easier</remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PrintIMDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Exif.PrintIMDirectory"/>.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Exif.PrintIMDirectory">
            <remarks>These tags can be found in Epson proprietary metadata. The index values are 'fake' but
            chosen specifically to make processing easier</remarks>
            <author>Kevin Mott https://github.com/kwhopper</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.FileSystem.FileMetadataDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.FileSystem.FileMetadataDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.FileSystem.FileMetadataReader.Read(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.FileType.FileTypeDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.FileType.FileTypeDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifAnimationDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifAnimationDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifCommentDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifCommentDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifControlDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifControlDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifHeaderDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifHeaderDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifImageDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifImageDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifMetadataReader">
            <summary>Obtains metadata from GIF files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Gif.GifMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Gif.GifReader">
            <summary>Reader of GIF encoded data.</summary>
            <remarks>
            Resources:
            <list type="bullet">
              <item>https://wiki.whatwg.org/wiki/GIF</item>
              <item>https://www.w3.org/Graphics/GIF/spec-gif89a.txt</item>
              <item>http://web.archive.org/web/20100929230301/http://www.etsimo.uniovi.es/gifanim/gif87a.txt</item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Gif.GifReader.ReadGifDirectoriesInternal(MetadataExtractor.IO.SequentialReader)">
            <summary>This method exists because generator methods cannot yield in try/catch blocks.</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Gif.GifReader.ReadGifHeader(MetadataExtractor.IO.SequentialReader)">
            <summary>Reads the fixed-position GIF header.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Icc.IccDescriptor">
            <author>Yuri Binev</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Icc.IccDirectory">
            <author>Yuri Binev</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Icc.IccReader">
            <summary>Reads ICC profile data.</summary>
            <remarks>
            ICC is the International Color Consortium.
            <list type="bullet">
              <item>http://en.wikipedia.org/wiki/ICC_profile</item>
              <item>http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/ICC_Profile.html</item>
              <item>https://developer.apple.com/library/mac/samplecode/ImageApp/Listings/ICC_h.html</item>
            </list>
            </remarks>
            <author>Yuri Binev</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Ico.IcoDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Ico.IcoDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Ico.IcoMetadataReader">
            <summary>Obtains metadata from ICO (Windows Icon) files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Ico.IcoMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Ico.IcoReader">
            <summary>Reads ICO (Windows Icon) file metadata.</summary>
            <remarks>
            <list type="bullet">
            <item>https://en.wikipedia.org/wiki/ICO_(file_format)</item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Iptc.IptcDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Iptc.IptcDirectory"/>.
            <para />
            As the IPTC directory already stores values as strings, this class simply returns the tag's value.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Akihiko Kusanagi https://github.com/nagix</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Iptc.IptcDirectory">
            <summary>Describes tags used by the International Press Telecommunications Council (IPTC) metadata format.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Akihiko Kusanagi https://github.com/nagix</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcDirectory.GetKeywords">
            <summary>Returns any keywords contained in the IPTC data.</summary>
            <remarks>This value may be <c>null</c>.</remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcDirectory.GetDateSent">
            <summary>
            Combines tags <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagDateSent" /> and <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagTimeSent"/> to obtain a single
            <see cref="T:System.DateTimeOffset"/> representing when the service sent this image.
            </summary>
            <returns>When the service sent this image, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcDirectory.GetReleaseDate">
            <summary>
            Combines tags <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagReleaseDate" /> and <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagReleaseTime"/> to obtain a single
            <see cref="T:System.DateTimeOffset"/> representing when this image was released.
            </summary>
            <returns>When this image was released, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcDirectory.GetExpirationDate">
            <summary>
            Combines tags <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagExpirationDate" /> and <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagExpirationTime"/> to obtain a single
            <see cref="T:System.DateTimeOffset"/> after which this image should not be used.
            </summary>
            <returns>When this image should expire, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcDirectory.GetDateCreated">
            <summary>
            Combines tags <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagDateCreated" /> and <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagTimeCreated"/> to obtain a single
            <see cref="T:System.DateTimeOffset"/> representing when this image was captured.
            </summary>
            <returns>When this image was released, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcDirectory.GetDigitalDateCreated">
            <summary>
            Combines tags <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagDateCreated" /> and <see cref="F:MetadataExtractor.Formats.Iptc.IptcDirectory.TagTimeCreated"/> to obtain a single
            <see cref="T:System.DateTimeOffset"/> representing when the digital representation of this image was created.
            </summary>
            <returns>When the digital representation of this image was created, if possible, otherwise <c>null</c>.</returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Iptc.IptcReader">
            <summary>Reads IPTC data.</summary>
            <remarks>
            Extracted values are returned from <see cref="M:MetadataExtractor.Formats.Iptc.IptcReader.Extract(MetadataExtractor.IO.SequentialReader,System.Int64)"/> in an <see cref="T:MetadataExtractor.Formats.Iptc.IptcDirectory"/>.
            <para />
            See the IPTC specification: http://www.iptc.org/std/IIM/4.1/specification/IIMV4.1.pdf
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.IptcReader.Extract(MetadataExtractor.IO.SequentialReader,System.Int64)">
            <summary>Reads IPTC values and returns them in an <see cref="T:MetadataExtractor.Formats.Iptc.IptcDirectory"/>.</summary>
            <remarks>
            Note that IPTC data does not describe its own length, hence <paramref name="length"/> is required.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.Iso2022Converter.ConvertEscapeSequenceToEncodingName(System.Byte[])">
            <summary>Attempts to convert the given ISO2022 escape sequence to an encoding name.</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Iptc.Iso2022Converter.GuessEncoding(System.Byte[])">
            <summary>Attempts to guess the encoding of a string provided as a byte array.</summary>
            <remarks>
            Encodings trialled are, in order:
            <list type="bullet">
              <item>UTF-8</item>
              <item>ISO-8859-1</item>
              <item>ASCII</item>
            </list>
            <para />
            Its only purpose is to guess the encoding if and only if iptc tag coded character set is not set. If the
            encoding is not UTF-8, the tag should be set. Otherwise it is bad practice. This method tries to
            workaround this issue since some metadata manipulating tools do not prevent such bad practice.
            <para />
            About the reliability of this method: The check if some bytes are UTF-8 or not has a very high reliability.
            The two other checks are less reliable.
            </remarks>
            <param name="bytes">some text as bytes</param>
            <returns>the name of the encoding or null if none could be guessed</returns>
        </member>
        <member name="P:MetadataExtractor.Formats.Iso14496.BoxLocation.Origin">
            <summary>
            Gets the offset within the file at which the box resides.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.Iso14496.BoxLocation.Length">
            <summary>
            Gets the length of this box in bytes.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.Iso14496.BoxLocation.NextPosition">
            <summary>
            Gets the offset within the file at which the next box resides.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.Iso14496.BoxLocation.TypeString">
            <summary>
            Gets the four character code which identifies this box.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jfif.JfifDescriptor">
            <summary>Provides human-readable string versions of the tags stored in a <see cref="T:MetadataExtractor.Formats.Jfif.JfifDirectory"/>.</summary>
            <remarks>
            Provides human-readable string versions of the tags stored in a <see cref="T:MetadataExtractor.Formats.Jfif.JfifDirectory"/>.
            <list type="bullet">
              <item>http://en.wikipedia.org/wiki/JPEG_File_Interchange_Format</item>
              <item>http://www.w3.org/Graphics/JPEG/jfif3.pdf</item>
            </list>
            </remarks>
            <author>Yuri Binev, Drew Noakes</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jfif.JfifDirectory">
            <summary>Directory of tags and values for the SOF0 Jfif segment.</summary>
            <author>Yuri Binev, Drew Noakes</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Jfif.JfifDirectory.TagUnits">
            <summary>Units for pixel density fields.</summary>
            <remarks>One of None, Pixels per Inch, Pixels per Centimetre.</remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Jfif.JfifReader">
            <summary>Reads JFIF (JPEG File Interchange Format) data.</summary>
            <remarks>
            JFIF is found in JPEG APP0 segments.
            <list type="bullet">
              <item>http://en.wikipedia.org/wiki/JPEG_File_Interchange_Format</item>
              <item>http://www.w3.org/Graphics/JPEG/jfif3.pdf</item>
            </list>
            </remarks>
            <author>Yuri Binev, Drew Noakes, Markus Meyer</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jfif.JfifReader.Extract(MetadataExtractor.IO.IndexedReader)">
            <summary>Reads JFIF values and returns them in an <see cref="T:MetadataExtractor.Formats.Jfif.JfifDirectory"/>.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jfxx.JfxxDescriptor">
            <summary>Provides human-readable string versions of the tags stored in a <see cref="T:MetadataExtractor.Formats.Jfxx.JfxxDirectory"/>.</summary>
            <remarks>
            Provides human-readable string versions of the tags stored in a <see cref="T:MetadataExtractor.Formats.Jfxx.JfxxDirectory"/>.
            <list type="bullet">
              <item>http://en.wikipedia.org/wiki/JPEG_File_Interchange_Format</item>
              <item>http://www.w3.org/Graphics/JPEG/jfif3.pdf</item>
            </list>
            </remarks>
            <author>Drew Noakes</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jfxx.JfxxDirectory">
            <summary>Directory of tags and values for the SOF0 Jfif segment.</summary>
            <author>Drew Noakes</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jfxx.JfxxReader">
            <summary>Reads JFXX (JFIF Extensions) data.</summary>
            <remarks>
            JFXX is found in JPEG APP0 segments.
            <list type="bullet">
              <item>http://en.wikipedia.org/wiki/JPEG_File_Interchange_Format</item>
              <item>http://www.w3.org/Graphics/JPEG/jfif3.pdf</item>
            </list>
            </remarks>
            <author>Drew Noakes</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jfxx.JfxxReader.Extract(MetadataExtractor.IO.IndexedReader)">
            <summary>Reads JFXX values and returns them in an <see cref="T:MetadataExtractor.Formats.Jfxx.JfxxDirectory"/>.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.HuffmanTablesDescriptor">
            <summary>Provides a human-readable string version of the tag stored in a HuffmanTablesDirectory.</summary>
            <remarks>
            Provides a human-readable string versions of the tag stored in a HuffmanTablesDirectory.
            <list type="bullet">
              <item>https://en.wikipedia.org/wiki/Huffman_coding</item>
              <item>http://stackoverflow.com/a/4954117</item>
            </list>
            </remarks>
            <author>Nadahar</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.HuffmanTablesDirectory">
            <summary>Directory of tables for the DHT (Define Huffman Table(s)) segment.</summary>
            <author>Nadahar</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.HuffmanTablesDirectory.GetTable(System.Int32)">
            <remarks>Use GetNumberOfTables for bounds-checking.</remarks>
            <param name="tableNumber">The zero-based index of the table. This number is normally between 0 and 3.</param>
            <returns>The HuffmanTable having the specified number.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.HuffmanTablesDirectory.GetNumberOfTables">
            <returns>The number of Huffman tables held by this HuffmanTablesDirectory instance.</returns>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.HuffmanTablesDirectory._tables">
            <returns>The List of HuffmanTables in this Directory.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.HuffmanTablesDirectory.IsTypical">
            <summary>Evaluates whether all the tables in this HuffmanTablesDirectory are "typical" Huffman tables.</summary>
            <remarks>
            "Typical" has a special meaning in this context as the JPEG standard
            (ISO/IEC 10918 or ITU-T T.81) defines 4 Huffman tables that has been
            developed from the average statistics of a large set of images with 8-bit
            precision. Using these instead of calculating the optimal Huffman tables
            for a given image is faster, and is preferred by many hardware encoders
            and some hardware decoders.
            <para/>
            Even though the JPEG standard doesn't define these as "standard tables"
            and requires a decoder to be able to read any valid Huffman tables, some
            are in reality limited decoding images using these "typical" tables.
            Standards like DCF(Design rule for Camera File system) and DLNA(Digital
            Living Network Alliance) actually requires any compliant JPEG to use only
            the "typical" Huffman tables.
            <para/>
            This is also related to the term "optimized" JPEG. An "optimized" JPEG is
            a JPEG that doesn't use the "typical" Huffman tables.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.HuffmanTablesDirectory.IsOptimized">
            <remarks>The opposite of IsTypical().</remarks>
            <returns>
            Whether or not the tables in this HuffmanTablesDirectory
            are "optimized" - which means that at least one of them aren't
            one of the "typical" Huffman tables.
            </returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.HuffmanTable">
            <summary>A JPEG Huffman table.</summary>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.HuffmanTable.TableLength">
            <returns>The table length in bytes.</returns>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.HuffmanTable.TableClass">
            <returns>The HuffmanTableClass of this table.</returns>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.HuffmanTable.TableDestinationId">
            <returns>The destination identifier for this table.</returns>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.HuffmanTable.LengthBytes">
            <returns>A byte array with the L values for this table.</returns>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.HuffmanTable.ValueBytes">
            <returns>A byte array with the V values for this table.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.HuffmanTable.IsTypical">
            <summary>Evaluates whether this table is a "typical" Huffman table.</summary>
            <remarks>
            "Typical" has a special meaning in this context as the JPEG standard
            (ISO/IEC 10918 or ITU-T T.81) defines 4 Huffman tables that has been
            developed from the average statistics of a large set of images with 8-bit
            precision. Using these instead of calculating the optimal Huffman tables
            for a given image is faster, and is preferred by many hardware encoders
            and some hardware decoders.
            <para/>
            Even though the JPEG standard doesn't define these as "standard tables"
            and requires a decoder to be able to read any valid Huffman tables, some
            are in reality limited decoding images using these "typical" tables.
            Standards like DCF(Design rule for Camera File system) and DLNA(Digital
            Living Network Alliance) actually requires any compliant JPEG to use only
            the "typical" Huffman tables.
            <para/>
            This is also related to the term "optimized" JPEG. An "optimized" JPEG is
            a JPEG that doesn't use the "typical" Huffman tables.
            </remarks>
            <returns>Whether or not this table is one of the predefined "typical" Huffman tables.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.HuffmanTable.IsOptimized">
            <remarks>The opposite of IsTypical().</remarks>
            <returns>
            Whether or not this table is "optimized" - which means that
            it isn't one of the "typical" Huffman tables.
            </returns>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.IJpegSegmentMetadataReader">
            <summary>Defines an object that extracts metadata from in JPEG segments.</summary>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.IJpegSegmentMetadataReader.SegmentTypes">
            <summary>Gets the set of JPEG segment types that this reader is interested in.</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.IJpegSegmentMetadataReader.ReadJpegSegments(System.Collections.Generic.IEnumerable{MetadataExtractor.Formats.Jpeg.JpegSegment})">
            <summary>Extracts metadata from all instances of a particular JPEG segment type.</summary>
            <param name="segments">
            A sequence of JPEG segments from which the metadata should be extracted. These are in the order encountered in the original file.
            </param>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegCommentDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Jpeg.JpegCommentDirectory"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegCommentDirectory">
            <summary>Describes tags used by a JPEG file comment.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegCommentDirectory.TagComment">
            <summary>This value does not apply to a particular standard.</summary>
            <remarks>
            This value does not apply to a particular standard. Rather, this value has been fabricated to maintain
            consistency with other directory types.
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegCommentReader">
            <summary>Reads JPEG comments.</summary>
            <remarks>JPEG files can store zero or more comments in COM segments.</remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegCommentReader.ReadJpegSegments(System.Collections.Generic.IEnumerable{MetadataExtractor.Formats.Jpeg.JpegSegment})">
            <summary>Reads JPEG comments, returning each in a <see cref="T:MetadataExtractor.Formats.Jpeg.JpegCommentDirectory"/>.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegComponent">
            <summary>
            Stores information about a JPEG image component such as the component id, horiz/vert sampling factor and
            quantization table number.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.JpegComponent.Name">
            <summary>Returns the component name (one of: Y, Cb, Cr, I, or Q)</summary>
            <value>the component name</value>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegDescriptor">
            <summary>Provides human-readable string versions of the tags stored in a JpegDirectory.</summary>
            <remarks>
            Provides human-readable string versions of the tags stored in a JpegDirectory.
            Thanks to Darrell Silver (www.darrellsilver.com) for the initial version of this class.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegDhtReader">
            <summary>Reader for JPEG Huffman tables, found in the DHT JPEG segment.</summary>
            <seealso cref="T:MetadataExtractor.Formats.Jpeg.JpegSegment"/>
            <author>Nadahar</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegDirectory">
            <summary>Directory of tags and values for the SOF0 JPEG segment.</summary>
            <remarks>This segment holds basic metadata about the image.</remarks>
            <author>Darrell Silver http://www.darrellsilver.com and Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagDataPrecision">
            <summary>This is in bits/sample, usually 8 (12 and 16 not supported by most software).</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagImageHeight">
            <summary>The image's height.</summary>
            <remarks>Necessary for decoding the image, so it should always be there.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagImageWidth">
            <summary>The image's width.</summary>
            <remarks>Necessary for decoding the image, so it should always be there.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagNumberOfComponents">
            <summary>
            Usually 1 = grey scaled, 3 = color YcbCr or YIQ, 4 = color CMYK
            Each component TAG_COMPONENT_DATA_[1-4], has the following meaning:
            component Id(1byte)(1 = Y, 2 = Cb, 3 = Cr, 4 = I, 5 = Q),
            sampling factors (1byte) (bit 0-3 vertical., 4-7 horizontal.),
            quantization table number (1 byte).
            </summary>
            <remarks>
            This info is from http://www.funducode.com/freec/Fileformats/format3/format3b.htm
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagComponentData1">
            <summary>The first of a possible 4 color components.</summary>
            <remarks>The number of components specified in <see cref="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagNumberOfComponents"/>.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagComponentData2">
            <summary>The second of a possible 4 color components.</summary>
            <remarks>The number of components specified in <see cref="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagNumberOfComponents"/>.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagComponentData3">
            <summary>The third of a possible 4 color components.</summary>
            <remarks>The number of components specified in <see cref="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagNumberOfComponents"/>.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagComponentData4">
            <summary>The fourth of a possible 4 color components.</summary>
            <remarks>The number of components specified in <see cref="F:MetadataExtractor.Formats.Jpeg.JpegDirectory.TagNumberOfComponents"/>.</remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegDirectory.GetComponent(System.Int32)">
            <param name="componentNumber">
            The zero-based index of the component.  This number is normally between 0 and 3.
            Use <see cref="M:MetadataExtractor.Formats.Jpeg.JpegDirectory.GetNumberOfComponents"/> for bounds-checking.
            </param>
            <returns>the JpegComponent having the specified number, or <c>null</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegDirectory.GetImageWidth">
            <exception cref="T:MetadataExtractor.MetadataException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegDirectory.GetImageHeight">
            <exception cref="T:MetadataExtractor.MetadataException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegDirectory.GetNumberOfComponents">
            <exception cref="T:MetadataExtractor.MetadataException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegDnlDescriptor">
            <summary>
            Provides human-readable string representations of tag values stored in a <see cref="T:MetadataExtractor.Formats.Jpeg.JpegDnlDirectory"/>.
            </summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegDnlDirectory">
            <summary>Describes tags parsed from JPEG DNL data, holding the image height with information missing from the JPEG SOFx segment</summary>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegDnlDirectory.TagImageHeight">
            <summary>The image's height, gleaned from DNL data instead of an SOFx segment</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegDnlReader">
            <summary>Decodes JPEG DNL image height data.</summary>
            <remarks>The current implementation only calls this reader if image height is missing from the JPEG SOFx segment.</remarks>
            <seealso cref="T:MetadataExtractor.Formats.Jpeg.JpegSegment"/>
            <author>Nadahar</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegMetadataReader">
            <summary>Obtains all available metadata from JPEG formatted files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegMetadataReader.ReadMetadata(System.IO.Stream,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Jpeg.IJpegSegmentMetadataReader})">
            <exception cref="T:MetadataExtractor.Formats.Jpeg.JpegProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegMetadataReader.ReadMetadata(System.String,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Jpeg.IJpegSegmentMetadataReader})">
            <exception cref="T:MetadataExtractor.Formats.Jpeg.JpegProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegMetadataReader.Process(System.IO.Stream,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Jpeg.IJpegSegmentMetadataReader})">
            <exception cref="T:MetadataExtractor.Formats.Jpeg.JpegProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegProcessingException">
            <summary>An exception class thrown upon unexpected and fatal conditions while processing a JPEG file.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegReader">
            <summary>Reads SOF (Start of Frame) segment data.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Darrell Silver http://www.darrellsilver.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegReader.Extract(MetadataExtractor.Formats.Jpeg.JpegSegment)">
            <summary>Reads JPEG SOF values and returns them in a <see cref="T:MetadataExtractor.Formats.Jpeg.JpegDirectory"/>.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegSegment">
            <summary>
            Holds information about a JPEG segment.
            </summary>
            <seealso cref="T:MetadataExtractor.Formats.Jpeg.JpegSegmentReader"/>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegSegmentReader">
            <summary>Parses the structure of JPEG data, returning contained segments.</summary>
            <remarks>
            JPEG files are composed of a sequence of consecutive JPEG segments. Each segment has a type <see cref="T:MetadataExtractor.Formats.Jpeg.JpegSegmentType"/>.
            A JPEG file can contain multiple segments having the same type.
            <para />
            Segments are returned in the order they appear in the file, however that order may vary from file to file.
            <para />
            Use <see cref="M:MetadataExtractor.Formats.Jpeg.JpegSegmentReader.ReadSegments(MetadataExtractor.IO.SequentialReader,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Jpeg.JpegSegmentType})"/> to specific segment types,
            or pass <c>null</c> to read all segments.
            <para />
            Note that SOS (start of scan) or EOI (end of image) segments are not returned by this class's methods.
            </remarks>
            <seealso cref="T:MetadataExtractor.Formats.Jpeg.JpegSegment"/>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegSegmentReader.ReadSegments(System.String,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Jpeg.JpegSegmentType})">
            <summary>
            Walks the provided JPEG data, returning <see cref="T:MetadataExtractor.Formats.Jpeg.JpegSegment"/> objects.
            </summary>
            <remarks>
            Will not return SOS (start of scan) or EOI (end of image) segments.
            </remarks>
            <param name="filePath">a file from which the JPEG data will be read.</param>
            <param name="segmentTypes">the set of JPEG segments types that are to be returned. If this argument is <c>null</c> then all found segment types are returned.</param>
            <exception cref="T:MetadataExtractor.Formats.Jpeg.JpegProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegSegmentReader.ReadSegments(MetadataExtractor.IO.SequentialReader,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Jpeg.JpegSegmentType})">
            <summary>
            Processes the provided JPEG data, and extracts the specified JPEG segments into a <see cref="T:MetadataExtractor.Formats.Jpeg.JpegSegment"/> object.
            </summary>
            <remarks>
            Will not return SOS (start of scan) or EOI (end of image) segments.
            </remarks>
            <param name="reader">a <see cref="T:MetadataExtractor.IO.SequentialReader"/> from which the JPEG data will be read. It must be positioned at the beginning of the JPEG data stream.</param>
            <param name="segmentTypes">the set of JPEG segments types that are to be returned. If this argument is <c>null</c> then all found segment types are returned.</param>
            <exception cref="T:MetadataExtractor.Formats.Jpeg.JpegProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegSegmentType">
            <summary>An enumeration of the known segment types found in JPEG files.</summary>
            <remarks>
            <list type="bullet">
              <item>http://www.ozhiker.com/electronics/pjmt/jpeg_info/app_segments.html</item>
              <item>http://www.sno.phy.queensu.ca/~phil/exiftool/TagNames/JPEG.html</item>
              <item>http://lad.dsc.ufcg.edu.br/multimidia/jpegmarker.pdf</item>
              <item>http://dev.exiv2.org/projects/exiv2/wiki/The_Metadata_in_JPEG_files</item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Tem">
            <summary>For temporary use in arithmetic coding.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof0">
            <summary>Start-of-Frame, non-differential Huffman coding frame, baseline DCT.</summary>
            <remarks>
            Indicates that this is a baseline DCT-based JPEG, and specifies the width,
            height, number of components, and component subsampling (e.g., 4:2:0).
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof1">
            <summary>Start-of-Frame, non-differential Huffman coding frame, extended sequential DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof2">
            <summary>Start-of-Frame, non-differential Huffman coding frame, progressive DCT.</summary>
            <remarks>
            Indicates that this is a progressive DCT-based JPEG, and specifies the width,
            height, number of components, and component subsampling (e.g., 4:2:0).
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof3">
            <summary>Start-of-Frame, non-differential Huffman coding frame, lossless sequential.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Dht">
            <summary>Define Huffman Table(s).</summary>
            <remarks>Specifies one or more Huffman tables.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof5">
            <summary>Start-of-Frame, differential Huffman coding frame, differential sequential DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof6">
            <summary>Start-of-Frame, differential Huffman coding frame, differential progressive DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof7">
            <summary>Start-of-Frame, differential Huffman coding frame, differential lossless.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof9">
            <summary>Start-of-Frame, non-differential arithmetic coding frame, extended sequential DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof10">
            <summary>Start-of-Frame, non-differential arithmetic coding frame, progressive DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof11">
            <summary>Start-of-Frame, non-differential arithmetic coding frame, lossless sequential.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Dac">
            <summary>Define Arithmetic Coding table(s).</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof13">
            <summary>Start-of-Frame, differential arithmetic coding frame, differential sequential DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof14">
            <summary>Start-of-Frame, differential arithmetic coding frame, differential progressive DCT.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sof15">
            <summary>Start-of-Frame, differential arithmetic coding frame, differential lossless.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst0">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst1">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst2">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst3">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst4">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst5">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst6">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Rst7">
            <summary>Restart.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Soi">
            <summary>Start Of Image segment. Begins the compressed JPEG data stream.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Eoi">
            <summary>End-of-Image. Terminates the JPEG compressed data stream that started at <see cref="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Soi"/>.</summary>
            <remarks>No length or parameter sequence follows this marker.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Sos">
            <summary>Start-of-Scan.</summary>
            <remarks>
            Begins a top-to-bottom scan of the image.
            In baseline DCT JPEG images, there is generally a single scan.
            Progressive DCT JPEG images usually contain multiple scans.
            This marker specifies which slice of data it will contain, and is
            immediately followed by entropy-coded data.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Dqt">
            <summary>Define Quantization Table.</summary>
            <remarks>Specifies one or more quantization tables.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Dnl">
            <summary>Define Number of Lines.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Dri">
            <summary>Define Restart Interval.</summary>
            <remarks>
            Specifies the interval between RSTn markers, in macroblocks.
            This marker is followed by two bytes indicating the fixed size so
            it can be treated like any other variable size segment.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Dhp">
            <summary>Define Hierarchical Progression.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Exp">
            <summary>Expand reference components.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App0">
            <summary>Application specific, type 0. Commonly contains JFIF, JFXX.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App1">
            <summary>Application specific, type 1. Commonly contains Exif. XMP data is also kept in here, though usually in a second instance.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App2">
            <summary>Application specific, type 2. Commonly contains ICC.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App3">
            <summary>Application specific, type 3.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App4">
            <summary>Application specific, type 4.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App5">
            <summary>Application specific, type 5.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App6">
            <summary>Application specific, type 6.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App7">
            <summary>Application specific, type 7.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App8">
            <summary>Application specific, type 8.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.App9">
            <summary>Application specific, type 9.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.AppA">
            <summary>Application specific, type A. Can contain Unicode comments, though <see cref="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Com"/> is more commonly used for comments.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.AppB">
            <summary>Application specific, type B.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.AppC">
            <summary>Application specific, type C.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.AppD">
            <summary>Application specific, type D. Commonly contains IPTC, Photoshop data.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.AppE">
            <summary>Application specific, type E. Commonly contains Adobe data.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.AppF">
            <summary>Application specific, type F.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Jpeg.JpegSegmentType.Com">
            <summary>JPEG comment (text).</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Jpeg.JpegSegmentTypeExtensions">
            <summary>
            Extension methods for <see cref="T:MetadataExtractor.Formats.Jpeg.JpegSegmentType"/> enum.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegSegmentTypeExtensions.CanContainMetadata(MetadataExtractor.Formats.Jpeg.JpegSegmentType)">
            <summary>Gets whether this JPEG segment type might contain metadata.</summary>
            <remarks>Used to exclude large image-data-only segment from certain types of processing.</remarks>
        </member>
        <member name="P:MetadataExtractor.Formats.Jpeg.JpegSegmentTypeExtensions.CanContainMetadataTypes">
            <summary>Gets JPEG segment types that might contain metadata.</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegSegmentTypeExtensions.ContainsPayload(MetadataExtractor.Formats.Jpeg.JpegSegmentType)">
            <summary>Gets whether this JPEG segment type's marker is followed by a length indicator.</summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Jpeg.JpegSegmentTypeExtensions.IsApplicationSpecific(MetadataExtractor.Formats.Jpeg.JpegSegmentType)">
            <summary>Gets whether this JPEG segment is intended to hold application specific data.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Netpbm.NetpbmHeaderDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Netpbm.NetpbmHeaderDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Netpbm.NetpbmMetadataReader">
            <summary>Obtains metadata from BMP files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Netpbm.NetpbmMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Netpbm.NetpbmReader">
            <summary>
            Reads metadata from Netpbm files.
            </summary>
            <remarks>
            Resources:
            <list type="bullet">
                <item>https://en.wikipedia.org/wiki/Netpbm_format</item>
                <item>http://netpbm.sourceforge.net/doc/ppm.html</item>
                <item>http://paulbourke.net/dataformats/ppm/</item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Pcx.PcxDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Pcx.PcxDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Pcx.PcxMetadataReader">
            <summary>Obtains metadata from PCX image files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Pcx.PcxMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Pcx.PcxReader">
            <summary>Reads PCX image file metadata.</summary>
            <remarks>
            <list type="bullet">
            <item>https://courses.engr.illinois.edu/ece390/books/labmanual/graphics-pcx.html</item>
            <item>http://www.fileformat.info/format/pcx/egff.htm</item>
            <item>http://fileformats.archiveteam.org/wiki/PCX</item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.DuckyDirectory">
            <summary>Holds the data found in Photoshop "ducky" segments, created during Save-for-Web.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.DuckyReader">
            <summary>Reads Photoshop "ducky" segments, created during Save-for-Web.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.Knot">
            <summary>
            Represents a knot created by Photoshop:
            <list type="bullet">
            <item>Linked knot</item>
            <item>Unlinked knot</item>
            </list>
            </summary>
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="P:MetadataExtractor.Formats.Photoshop.Knot.Item(System.Int32)">
            <summary>
            Add/Get an individual coordinate value (x or y) to/from
            points array (6 points per knot)
            </summary>
            <param name="index"></param>
            <returns>an individual coordinate value</returns>
            <remarks>Define the indexer to allow client code to use [] notation</remarks>
        </member>
        <member name="P:MetadataExtractor.Formats.Photoshop.Knot.Type">
            <summary>
            Get the type of knot (linked or unlinked)
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PhotoshopDescriptor">
            <author>Yuri Binev</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PhotoshopDirectory">
            <summary>Holds the metadata found in the APPD segment of a JPEG file saved by Photoshop.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Yuri Binev</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PhotoshopReader">
            <summary>Reads metadata created by Photoshop and stored in the APPD segment of JPEG files.</summary>
            <remarks>
            Reads metadata created by Photoshop and stored in the APPD segment of JPEG files.
            Note that IPTC data may be stored within this segment, in which case this reader will
            create both a <see cref="T:MetadataExtractor.Formats.Photoshop.PhotoshopDirectory"/> and a <see cref="T:MetadataExtractor.Formats.Iptc.IptcDirectory"/>.
            </remarks>
            <author>Yuri Binev</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PhotoshopTiffHandler">
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PsdHeaderDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PsdHeaderDirectory">
            <summary>Holds the basic metadata found in the header of a Photoshop PSD file.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Photoshop.PsdHeaderDirectory.TagChannelCount">
            <summary>The number of channels in the image, including any alpha channels.</summary>
            <remarks>Supported range is 1 to 56.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Photoshop.PsdHeaderDirectory.TagImageHeight">
            <summary>The height of the image in pixels.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Photoshop.PsdHeaderDirectory.TagImageWidth">
            <summary>The width of the image in pixels.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Photoshop.PsdHeaderDirectory.TagBitsPerChannel">
            <summary>The number of bits per channel.</summary>
            <remarks>Supported values are 1, 8, 16 and 32.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Photoshop.PsdHeaderDirectory.TagColorMode">
            <summary>The color mode of the file.</summary>
            <remarks>
            Supported values are:
            Bitmap = 0; Grayscale = 1; Indexed = 2; RGB = 3; CMYK = 4; Multichannel = 7; Duotone = 8; Lab = 9.
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PsdMetadataReader">
            <summary>Obtains metadata from Photoshop's PSD files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Photoshop.PsdMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.PsdReader">
            <summary>Reads metadata stored within PSD file format data.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Photoshop.Subpath">
            <summary>
            Represents a subpath created by Photoshop:
            <list type="bullet">
            <item>Closed Bezier knot, linked</item>
            <item>Closed Bezier knot, unlinked</item>
            <item>Open Bezier knot, linked</item>
            <item>Open Bezier knot, unlinked</item>
            </list>
            </summary>
            <author>Payton Garland</author>
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Photoshop.Subpath.Add(MetadataExtractor.Formats.Photoshop.Knot)">
            <summary>
            Appends a knot (set of 3 points) into the list
            </summary>
            <param name="knot"></param>
        </member>
        <member name="P:MetadataExtractor.Formats.Photoshop.Subpath.KnotCount">
            <summary>
            Gets size of knots list
            </summary>
            <returns>size of knots List</returns>
        </member>
        <member name="P:MetadataExtractor.Formats.Photoshop.Subpath.Knots">
            <summary>
            Return a read-only list of Knots
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngChromaticities">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Png.PngChromaticities.#ctor(System.Byte[])">
            <exception cref="T:MetadataExtractor.Formats.Png.PngProcessingException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngChromaticitiesDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngChunk">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngChunkReader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Png.PngChunkReader.Extract(MetadataExtractor.IO.SequentialReader,System.Collections.Generic.ICollection{MetadataExtractor.Formats.Png.PngChunkType})">
            <exception cref="T:MetadataExtractor.Formats.Png.PngProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngChunkType">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngChunkType.IHDR">
            <summary>
            Denotes a critical <see cref="T:MetadataExtractor.Formats.Png.PngChunk"/> that contains basic information about the PNG image.
            </summary>
            <remarks>
            This must be the first chunk in the data sequence, and may only occur once.
            <para />
            The format is:
            <list type="bullet">
              <item><b>pixel width</b> 4 bytes, unsigned and greater than zero</item>
              <item><b>pixel height</b> 4 bytes, unsigned and greater than zero</item>
              <item><b>bit depth</b> 1 byte, number of bits per sample or per palette index (not per pixel)</item>
              <item><b>color type</b> 1 byte, maps to <see cref="T:MetadataExtractor.Formats.Png.PngColorType"/> enum</item>
              <item><b>compression method</b> 1 byte, currently only a value of zero (deflate/inflate) is in the standard</item>
              <item><b>filter method</b> 1 byte, currently only a value of zero (adaptive filtering with five basic filter types) is in the standard</item>
              <item><b>interlace method</b> 1 byte, indicates the transmission order of image data, currently only 0 (no interlace) and 1 (Adam7 interlace) are in the standard</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngChunkType.PLTE">
            <summary>
            Denotes a critical <see cref="T:MetadataExtractor.Formats.Png.PngChunk"/> that contains palette entries.
            </summary>
            <remarks>
            This chunk should only appear for a <see cref="T:MetadataExtractor.Formats.Png.PngColorType"/> of <see cref="F:MetadataExtractor.Formats.Png.PngColorType.IndexedColor"/>,
            and may only occur once in the PNG data sequence.
            <para />
            The chunk contains between one and 256 entries, each of three bytes:
            <list type="bullet">
              <item><b>red</b> 1 byte</item>
              <item><b>green</b> 1 byte</item>
              <item><b>blue</b> 1 byte</item>
            </list>
            The number of entries is determined by the chunk length. A chunk length indivisible by three is an error.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngChunkType.tEXt">
            <summary>
            Denotes an ancillary <see cref="T:MetadataExtractor.Formats.Png.PngChunk"/> that contains textual data, having first a keyword and then a value.
            </summary>
            <remarks>
            If multiple text data keywords are needed, then multiple chunks are included in the PNG data stream.
            <para />
            The format is:
            <list type="bullet">
            <item><b>keyword</b> 1-79 bytes</item>
            <item><b>null separator</b> 1 byte (\0)</item>
            <item><b>text string</b> 0 or more bytes</item>
            </list>
            Text is interpreted according to the Latin-1 character set [ISO-8859-1].
            Newlines should be represented by a single linefeed character (0x9).
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngColorType">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngColorType.Greyscale">
            <summary>Each pixel is a greyscale sample.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngColorType.TrueColor">
            <summary>Each pixel is an R,G,B triple.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngColorType.IndexedColor">
            <summary>Each pixel is a palette index.</summary>
            <remarks>Each pixel is a palette index. Seeing this value indicates that a <c>PLTE</c> chunk shall appear.</remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngColorType.GreyscaleWithAlpha">
            <summary>Each pixel is a greyscale sample followed by an alpha sample.</summary>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngColorType.TrueColorWithAlpha">
            <summary>Each pixel is an R,G,B triple followed by an alpha sample.</summary>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngHeader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Png.PngHeader.#ctor(System.Byte[])">
            <exception cref="T:MetadataExtractor.Formats.Png.PngProcessingException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngMetadataReader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Png.PngMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:MetadataExtractor.Formats.Png.PngProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Png.PngMetadataReader.ReadMetadata(System.IO.Stream)">
            <exception cref="T:MetadataExtractor.Formats.Png.PngProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="F:MetadataExtractor.Formats.Png.PngMetadataReader._latin1Encoding">
            <summary>
            The PNG spec states that ISO_8859_1 (Latin-1) encoding should be used for:
            <list type="bullet">
              <item>"tEXt" and "zTXt" chunks, both for keys and values (https://www.w3.org/TR/PNG/#11tEXt)</item>
              <item>"iCCP" chunks, for the profile name (https://www.w3.org/TR/PNG/#11iCCP)</item>
              <item>"sPLT" chunks, for the palette name (https://www.w3.org/TR/PNG/#11sPLT)</item>
            </list>
            Note that "iTXt" chunks use UTF-8 encoding (https://www.w3.org/TR/PNG/#11iTXt).
            <para/>
            For more guidance: http://www.w3.org/TR/PNG-Decoders.html#D.Text-chunk-processing
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.Png.PngMetadataReader.ProcessChunk(MetadataExtractor.Formats.Png.PngChunk)">
            <exception cref="T:MetadataExtractor.Formats.Png.PngProcessingException"/>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Png.PngProcessingException">
            <summary>An exception class thrown upon unexpected and fatal conditions while processing a JPEG file.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs">
            <summary>
            Models data provided to callbacks invoked when reading QuickTime atoms via <see cref="M:MetadataExtractor.Formats.QuickTime.QuickTimeReader.ProcessAtoms(System.IO.Stream,System.Action{MetadataExtractor.Formats.QuickTime.AtomCallbackArgs},System.Int64)"/>.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Type">
            <summary>
            Gets the 32-bit unsigned integer that identifies the atom's type.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Size">
            <summary>
            The length of the atom data, in bytes. If the atom extends to the end of the file, this value is zero.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Stream">
            <summary>
            Gets the stream from which atoms are being read.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.StartPosition">
            <summary>
            Gets the position within <see cref="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Stream"/> at which this atom's data started.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Reader">
            <summary>
            Gets a sequential reader from which this atom's contents may be read.
            </summary>
            <remarks>
            It is backed by <see cref="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Stream"/>, so manipulating the stream's position will influence this reader.
            </remarks>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Cancel">
            <summary>
            Gets and sets whether the callback wishes processing to terminate.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.TypeString">
            <summary>
            Gets the string representation of this atom's type.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.BytesLeft">
            <summary>
            Computes the number of bytes remaining in the atom, given the <see cref="P:MetadataExtractor.Formats.QuickTime.AtomCallbackArgs.Stream"/> position.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Formats.QuickTime.QuickTimeReader">
            <summary>
            Static class for processing atoms the QuickTime container format.
            </summary>
            <remarks>
            QuickTime file format specification: https://developer.apple.com/library/mac/documentation/QuickTime/QTFF/qtff.pdf
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.QuickTime.QuickTimeReader.ProcessAtoms(System.IO.Stream,System.Action{MetadataExtractor.Formats.QuickTime.AtomCallbackArgs},System.Int64)">
            <summary>
            Reads atom data from <paramref name="stream"/>, invoking <paramref name="handler"/> for each atom encountered.
            </summary>
            <param name="stream">The stream to read atoms from.</param>
            <param name="handler">A callback function to handle each atom.</param>
            <param name="stopByBytes">The maximum number of bytes to process before discontinuing.</param>
        </member>
        <member name="T:MetadataExtractor.Formats.QuickTime.QuickTimeReaderExtensions">
            <summary>
            Extension methods for reading QuickTime specific encodings from a <see cref="T:MetadataExtractor.IO.SequentialReader"/>.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Formats.QuickTime.QuickTimeReaderExtensions.GetMatrix(MetadataExtractor.IO.SequentialReader)">
            <summary>
            Returns a matrix as float[9].
            See <a href="https://developer.apple.com/library/archive/documentation/QuickTime/QTFF/QTFFChap4/qtff4.html#//apple_ref/doc/uid/**********-CH206-18737">QuickTime File Format Specification</a>.
            </summary>
            <param name="reader">The reader.</param>
            <returns>System.Single[].</returns>
        </member>
        <member name="T:MetadataExtractor.Formats.QuickTime.QuickTimeTypeChecker">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Raf.RafMetadataReader">
            <summary>Obtains metadata from RAF (Fujifilm camera raw) image files.</summary>
            <author>TSGames https://github.com/TSGames</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.IRiffChunkHandler">
            <summary>
            Interface of a class capable of handling an individual RIFF chunk.
            </summary>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.IRiffHandler">
            <summary>
            Interface of an class capable of handling events raised during the reading of a RIFF file
            via <see cref="T:MetadataExtractor.Formats.Riff.RiffReader"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Riff.IRiffHandler.ShouldAcceptRiffIdentifier(System.String)">
            <summary>Gets whether the specified RIFF identifier is of interest to this handler.</summary>
            <remarks>Returning <c>false</c> causes processing to stop after reading only the first twelve bytes of data.</remarks>
            <param name="identifier">The four character code identifying the type of RIFF data</param>
            <returns>true if processing should continue, otherwise false</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Riff.IRiffHandler.ShouldAcceptChunk(System.String)">
            <summary>Gets whether this handler is interested in the specific chunk type.</summary>
            <remarks>
            Returns <c>true</c> if the data should be copied into an array and passed
            to <see cref="M:MetadataExtractor.Formats.Riff.IRiffHandler.ProcessChunk(System.String,System.Byte[])"/>, or <c>false</c> to avoid
            the copy and skip to the next chunk in the file, if any.
            </remarks>
            <param name="fourCc">the four character code of this chunk</param>
            <returns><c>true</c> if <see cref="M:MetadataExtractor.Formats.Riff.IRiffHandler.ProcessChunk(System.String,System.Byte[])"/> should be called, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Riff.IRiffHandler.ShouldAcceptList(System.String)">
            <summary>Gets whether this handler is interested in the specific list type.</summary>
            <remarks>
            Returns <c>true</c> if the data should be copied into an array and passed
            to <see cref="M:MetadataExtractor.Formats.Riff.IRiffHandler.ProcessChunk(System.String,System.Byte[])"/>, or <c>false</c> to avoid
            the copy and skip to the next chunk in the file, if any.
            </remarks>
            <param name="fourCc">the four character code of this chunk</param>
            <returns><c>true</c> if <see cref="M:MetadataExtractor.Formats.Riff.IRiffHandler.ProcessChunk(System.String,System.Byte[])"/> should be called, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:MetadataExtractor.Formats.Riff.IRiffHandler.ProcessChunk(System.String,System.Byte[])">
            <summary>Perform whatever processing is necessary for the type of chunk with its payload.</summary>
            <remarks>This is only called if a previous call to <see cref="M:MetadataExtractor.Formats.Riff.IRiffHandler.ShouldAcceptChunk(System.String)"/> with the same <c>fourCC</c> returned <c>true</c>.</remarks>
            <param name="fourCc">the four character code of the chunk</param>
            <param name="payload">they payload of the chunk as a byte array</param>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.RiffChunkHandler`1">
            <summary>
            Base class for <see cref="T:MetadataExtractor.Formats.Riff.IRiffChunkHandler"/> implementations.
            </summary>
            <typeparam name="T">Directory type.</typeparam>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.RiffHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Riff.IRiffHandler"/> using a dictionary of
            <see cref="T:MetadataExtractor.Formats.Riff.IRiffChunkHandler"/> factory delegates.
            </summary>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.RiffProcessingException">
            <summary>An exception class thrown upon unexpected and fatal conditions while processing a RIFF file.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.RiffReader">
            <summary>
            Processes RIFF-formatted data, calling into client code via that <see cref="T:MetadataExtractor.Formats.Riff.IRiffHandler"/> interface.
            </summary>
            <remarks>
            For information on this file format, see:
            <list type="bullet">
              <item>http://en.wikipedia.org/wiki/Resource_Interchange_File_Format</item>
              <item>https://developers.google.com/speed/webp/docs/riff_container</item>
              <item>https://www.daubnet.com/en/file-format-riff</item>
            </list>
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Riff.RiffReader.ProcessRiff(MetadataExtractor.IO.SequentialReader,MetadataExtractor.Formats.Riff.IRiffHandler)">
            <summary>Processes a RIFF data sequence.</summary>
            <param name="reader">The <see cref="T:MetadataExtractor.IO.SequentialReader"/> from which the data should be read.</param>
            <param name="handler">The <see cref="T:MetadataExtractor.Formats.Riff.IRiffHandler"/> that will coordinate processing and accept read values.</param>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException">An error occurred during the processing of RIFF data that could not be ignored or recovered from.</exception>
            <exception cref="T:System.IO.IOException">an error occurred while accessing the required data</exception>
        </member>
        <member name="T:MetadataExtractor.Formats.Riff.RiffTypeChecker">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaDeveloperDirectory">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaDeveloperReader">
            <summary>Reads TGA image file developer area.</summary>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaExtensionDescriptor">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaExtensionDirectory">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaExtensionReader">
            <summary>Reads TGA image file extension area.</summary>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaFooterReader">
            <summary>Reads TGA image file footer.</summary>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaHeaderDescriptor">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaHeaderDirectory">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaHeaderReader">
            <summary>Reads TGA image file header.</summary>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaMetadataReader">
            <summary>Obtains metadata from TGA (Truevision) files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Tga.TgaMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:System.ArgumentException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Tga.TgaMetadataReader.ReadMetadata(System.IO.Stream)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:System.ArgumentException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Tga.TgaTypeChecker">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tiff.DirectoryTiffHandler">
            <summary>
            An implementation of <see cref="T:MetadataExtractor.Formats.Tiff.ITiffHandler"/> that stores tag values in <see cref="T:MetadataExtractor.Directory"/> objects.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tiff.ITiffHandler">
            <summary>
            Interface of an class capable of handling events raised during the reading of a TIFF file
            via <see cref="T:MetadataExtractor.Formats.Tiff.TiffReader"/>.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.ITiffHandler.SetTiffMarker(System.Int32)">
            <summary>Receives the 2-byte marker found in the TIFF header.</summary>
            <remarks>
            Receives the 2-byte marker found in the TIFF header.
            <para />
            Implementations are not obligated to use this information for any purpose, though it may be useful for
            validation or perhaps differentiating the type of mapping to use for observed tags and IFDs.
            </remarks>
            <param name="marker">the 2-byte value found at position 2 of the TIFF header</param>
            <exception cref="T:MetadataExtractor.Formats.Tiff.TiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.ITiffHandler.CustomProcessTag(System.Int32,System.Collections.Generic.ICollection{System.Int32},MetadataExtractor.IO.IndexedReader,System.Int32,System.Int32)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Tiff.TiffDataFormat">
            <summary>An enumeration of data formats used by the TIFF specification.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tiff.TiffMetadataReader">
            <summary>Obtains all available metadata from TIFF formatted files.</summary>
            <remarks>
            Obtains all available metadata from TIFF formatted files.  Note that TIFF files include many digital camera RAW
            formats, including Canon (CRW, CR2), Nikon (NEF), Olympus (ORF) and Panasonic (RW2).
            </remarks>
            <author>Darren Salomons</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.TiffMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Tiff.TiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.TiffMetadataReader.ReadMetadata(System.IO.Stream)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Tiff.TiffProcessingException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Tiff.TiffProcessingException">
            <summary>An exception class thrown upon unexpected and fatal conditions while processing a TIFF file.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
            <author>Darren Salomons</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Tiff.TiffReader">
            <summary>
            Processes TIFF-formatted data, calling into client code via that <see cref="T:MetadataExtractor.Formats.Tiff.ITiffHandler"/> interface.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.TiffReader.ProcessTiff(MetadataExtractor.IO.IndexedReader,MetadataExtractor.Formats.Tiff.ITiffHandler)">
            <summary>Processes a TIFF data sequence.</summary>
            <param name="reader">the <see cref="T:MetadataExtractor.IO.IndexedReader"/> from which the data should be read</param>
            <param name="handler">the <see cref="T:MetadataExtractor.Formats.Tiff.ITiffHandler"/> that will coordinate processing and accept read values</param>
            <exception cref="T:MetadataExtractor.Formats.Tiff.TiffProcessingException">if an error occurred during the processing of TIFF data that could not be ignored or recovered from</exception>
            <exception cref="T:System.IO.IOException">an error occurred while accessing the required data</exception>
            <exception cref="T:MetadataExtractor.Formats.Tiff.TiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.TiffReader.ProcessIfd(MetadataExtractor.Formats.Tiff.ITiffHandler,MetadataExtractor.IO.IndexedReader,System.Collections.Generic.ICollection{System.Int32},System.Int32)">
            <summary>Processes a TIFF IFD.</summary>
            <remarks>
            IFD Header:
            <list type="bullet">
              <item><b>2 bytes</b> number of tags</item>
            </list>
            Tag structure:
            <list type="bullet">
              <item><b>2 bytes</b> tag type</item>
              <item><b>2 bytes</b> format code (values 1 to 12, inclusive)</item>
              <item><b>4 bytes</b> component count</item>
              <item><b>4 bytes</b> inline value, or offset pointer if too large to fit in four bytes</item>
            </list>
            </remarks>
            <param name="handler">the <see cref="T:MetadataExtractor.Formats.Tiff.ITiffHandler"/> that will coordinate processing and accept read values</param>
            <param name="reader">the <see cref="T:MetadataExtractor.IO.IndexedReader"/> from which the data should be read</param>
            <param name="processedGlobalIfdOffsets">the set of visited IFD offsets, to avoid revisiting the same IFD in an endless loop</param>
            <param name="ifdOffset">the offset within <c>reader</c> at which the IFD data starts</param>
            <exception cref="T:System.IO.IOException">an error occurred while accessing the required data</exception>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.TiffReader.ProcessTag(MetadataExtractor.Formats.Tiff.ITiffHandler,System.Int32,System.Int32,System.Int32,MetadataExtractor.Formats.Tiff.TiffDataFormatCode,MetadataExtractor.IO.IndexedReader)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Tiff.TiffReader.CalculateTagOffset(System.Int32,System.Int32)">
            <summary>Determine the offset of a given tag within the specified IFD.</summary>
            <remarks>
            Add 2 bytes for the tag count.
            Each entry is 12 bytes.
            </remarks>
            <param name="ifdStartOffset">the offset at which the IFD starts</param>
            <param name="entryNumber">the zero-based entry number</param>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavFactDirectory">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavFactHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Riff.IRiffChunkHandler"/> for WAV "fact" chunk.
            </summary>
            <remarks>
            Source:
            http://www-mmsp.ece.mcgill.ca/Documents/AudioFormats/WAVE/WAVE.html
            </remarks>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavFormatDescriptor">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavFormatDirectory">
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavFormatHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Riff.IRiffChunkHandler"/> for WAV "fmt " chunk.
            </summary>
            <remarks>
            Source:
            http://www-mmsp.ece.mcgill.ca/Documents/AudioFormats/WAVE/WAVE.html
            </remarks>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavMetadataReader">
            <summary>Obtains metadata from WAV files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.Wav.WavMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.Wav.WavMetadataReader.ReadMetadata(System.IO.Stream)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.Wav.WavRiffHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Riff.IRiffHandler"/> specialising in WAV support.
            </summary>
            <remarks>
            Extracts data from chunk/list types:
            <list type="bullet">
              <item><c>"fmt "</c>: base format data</item>
              <item><c>"fact"</c>: number of samples</item>
            </list>
            Source:
            http://www-mmsp.ece.mcgill.ca/Documents/AudioFormats/WAVE/WAVE.html
            </remarks>
            <author>Dmitry Shechtman</author>
        </member>
        <member name="T:MetadataExtractor.Formats.WebP.WebPDescriptor">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.WebP.WebPDirectory">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.WebP.WebPMetadataReader">
            <summary>Obtains metadata from WebP files.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Formats.WebP.WebPMetadataReader.ReadMetadata(System.String)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException"/>
        </member>
        <member name="M:MetadataExtractor.Formats.WebP.WebPMetadataReader.ReadMetadata(System.IO.Stream)">
            <exception cref="T:System.IO.IOException"/>
            <exception cref="T:MetadataExtractor.Formats.Riff.RiffProcessingException"/>
        </member>
        <member name="T:MetadataExtractor.Formats.WebP.WebPRiffHandler">
            <summary>
            Implementation of <see cref="T:MetadataExtractor.Formats.Riff.IRiffHandler"/> specialising in WebP support.
            </summary>
            <remarks>
            Extracts data from chunk types:
            <list type="bullet">
              <item><c>"VP8X"</c>: width, height, is animation, has alpha</item>
              <item><c>"VP8L"</c>: width, height</item>
              <item><c>"VP8 "</c>: width, height</item>
              <item><c>"EXIF"</c>: full Exif data</item>
              <item><c>"ICCP"</c>: full ICC profile</item>
              <item><c>"XMP "</c>: full XMP data</item>
            </list>
            </remarks>
        </member>
        <member name="F:MetadataExtractor.Formats.Xmp.Schema.XmpProperties">
            <summary>XMP tag namespace.</summary>
            <remarks>
            XMP tag namespace. TODO the older "xap", "xapBJ", "xapMM" or "xapRights" namespace prefixes should be translated to the newer "xmp", "xmpBJ",
            "xmpMM" and "xmpRights" prefixes for use in family 1 group names
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Xmp.XmpDescriptor">
            <summary>Contains logic for the presentation of data stored in an <see cref="T:MetadataExtractor.Formats.Xmp.XmpDirectory"/>.</summary>
            <author>Torsten Skadell, Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Formats.Xmp.XmpDirectory">
            <summary>
            Wraps an instance of Adobe's <see cref="T:XmpCore.IXmpMeta"/> object, which holds XMP data.
            </summary>
            <remarks>
            XMP uses a namespace and path format for identifying values, which does not map to metadata-extractor's
            integer based tag identifiers. Therefore, XMP data is extracted and exposed via <see cref="P:MetadataExtractor.Formats.Xmp.XmpDirectory.XmpMeta"/>
            which returns an instance of Adobe's <see cref="T:XmpCore.IXmpMeta"/> which exposes the full XMP data set.
            </remarks>
            <author>Torsten Skadell</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.Formats.Xmp.XmpDirectory.XmpMeta">
            <summary>Gets the <see cref="T:XmpCore.IXmpMeta"/> object within this directory.</summary>
            <remarks>This object provides a rich API for working with XMP data.</remarks>
        </member>
        <member name="M:MetadataExtractor.Formats.Xmp.XmpDirectory.GetXmpProperties">
            <summary>Gets a map of all XMP properties in this directory, not just the known ones.</summary>
            <remarks>
            This is required because XMP properties are represented as strings, whereas the rest of this library
            uses integers for keys.
            </remarks>
        </member>
        <member name="T:MetadataExtractor.Formats.Xmp.XmpReader">
            <summary>Extracts XMP data JPEG APP1 segments.</summary>
            <remarks>
            XMP uses a namespace and path format for identifying values, which does not map to metadata-extractor's
            integer based tag identifiers. Therefore, XMP data is extracted and exposed via <see cref="P:MetadataExtractor.Formats.Xmp.XmpDirectory.XmpMeta"/>
            which returns an instance of Adobe's <see cref="T:XmpCore.IXmpMeta"/> which exposes the full XMP data set.
            <para />
            The extraction is done with Adobe's XmpCore-Library (XMP-Toolkit)
            Copyright (c) 1999 - 2007, Adobe Systems Incorporated All rights reserved.
            </remarks>
            <author>Torsten Skadell</author>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.GeoLocation">
            <summary>Represents a latitude and longitude pair, giving a position on earth in spherical coordinates.</summary>
            <remarks>
            Values of latitude and longitude are given in degrees.
            <para />
            This type is immutable.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.GeoLocation.#ctor(System.Double,System.Double)">
            <summary>
            Initialises an instance of <see cref="T:MetadataExtractor.GeoLocation"/>.
            </summary>
            <param name="latitude">the latitude, in degrees</param>
            <param name="longitude">the longitude, in degrees</param>
        </member>
        <member name="P:MetadataExtractor.GeoLocation.Latitude">
            <value>the latitudinal angle of this location, in degrees.</value>
        </member>
        <member name="P:MetadataExtractor.GeoLocation.Longitude">
            <value>the longitudinal angle of this location, in degrees.</value>
        </member>
        <member name="P:MetadataExtractor.GeoLocation.IsZero">
            <value>true, if both latitude and longitude are equal to zero</value>
        </member>
        <member name="M:MetadataExtractor.GeoLocation.DecimalToDegreesMinutesSecondsString(System.Double)">
            <summary>
            Converts a decimal degree angle into its corresponding DMS (degrees-minutes-seconds) representation as a string,
            of format:
            <c>-1° 23' 4.56"</c>
            </summary>
        </member>
        <member name="M:MetadataExtractor.GeoLocation.DecimalToDegreesMinutesSeconds(System.Double)">
            <summary>
            Converts a decimal degree angle into its corresponding DMS (degrees-minutes-seconds) component values, as
            a double array.
            </summary>
        </member>
        <member name="M:MetadataExtractor.GeoLocation.DegreesMinutesSecondsToDecimal(MetadataExtractor.Rational,MetadataExtractor.Rational,MetadataExtractor.Rational,System.Boolean)">
            <summary>
            Converts DMS (degrees-minutes-seconds) rational values, as given in
            <see cref="T:MetadataExtractor.Formats.Exif.GpsDirectory"/>, into a single value in degrees,
            as a double.
            </summary>
        </member>
        <member name="M:MetadataExtractor.GeoLocation.ToString">
            <returns>
            Returns a string representation of this object, of format:
            <c>1.23, 4.56</c>
            </returns>
        </member>
        <member name="M:MetadataExtractor.GeoLocation.ToDmsString">
            <returns>
            a string representation of this location, of format:
            <c>-1° 23' 4.56", 54° 32' 1.92"</c>
            </returns>
        </member>
        <member name="T:MetadataExtractor.ImageMetadataReader">
             <summary>Reads metadata from any supported file format.</summary>
             <remarks>
             This class a lightweight wrapper around other, specific metadata processors.
             During extraction, the file type is determined from the first few bytes of the file.
             Parsing is then delegated to one of:
            
             <list type="bullet">
               <item><see cref="T:MetadataExtractor.Formats.Jpeg.JpegMetadataReader"/> for JPEG files</item>
               <item><see cref="T:MetadataExtractor.Formats.Tiff.TiffMetadataReader"/> for TIFF and (most) RAW files</item>
               <item><see cref="T:MetadataExtractor.Formats.Photoshop.PsdMetadataReader"/> for Photoshop files</item>
               <item><see cref="T:MetadataExtractor.Formats.Png.PngMetadataReader"/> for PNG files</item>
               <item><see cref="T:MetadataExtractor.Formats.Bmp.BmpMetadataReader"/> for BMP files</item>
               <item><see cref="T:MetadataExtractor.Formats.Gif.GifMetadataReader"/> for GIF files</item>
               <item><see cref="T:MetadataExtractor.Formats.Ico.IcoMetadataReader"/> for ICO files</item>
               <item><see cref="T:MetadataExtractor.Formats.Netpbm.NetpbmMetadataReader"/> for Netpbm files (PPM, PGM, PBM, PPM)</item>
               <item><see cref="T:MetadataExtractor.Formats.Pcx.PcxMetadataReader"/> for PCX files</item>
               <item><see cref="T:MetadataExtractor.Formats.WebP.WebPMetadataReader"/> for WebP files</item>
               <item><see cref="T:MetadataExtractor.Formats.Raf.RafMetadataReader"/> for RAF files</item>
               <item><see cref="T:MetadataExtractor.Formats.QuickTime.QuickTimeMetadataReader"/> for QuickTime files</item>
             </list>
            
             If you know the file type you're working with, you may use one of the above processors directly.
             For most scenarios it is simpler, more convenient and more robust to use this class.
             <para />
             <see cref="T:MetadataExtractor.Util.FileTypeDetector"/> is used to determine the provided image's file type, and therefore
             the appropriate metadata reader to use.
             </remarks>
             <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.ImageMetadataReader.ReadMetadata(System.IO.Stream)">
            <summary>Reads metadata from an <see cref="T:System.IO.Stream"/>.</summary>
            <param name="stream">A stream from which the file data may be read.  The stream must be positioned at the beginning of the file's data.</param>
            <returns>A list of <see cref="T:MetadataExtractor.Directory"/> instances containing the various types of metadata found within the file's data.</returns>
            <exception cref="T:MetadataExtractor.ImageProcessingException">The file type is unknown, or processing errors occurred.</exception>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.ImageMetadataReader.ReadMetadata(System.String)">
            <summary>Reads metadata from a file.</summary>
            <remarks>Unlike <see cref="M:MetadataExtractor.ImageMetadataReader.ReadMetadata(System.IO.Stream)"/>, this overload includes a <see cref="T:MetadataExtractor.Formats.FileSystem.FileMetadataDirectory"/> in the output.</remarks>
            <param name="filePath">Location of a file from which data should be read.</param>
            <returns>A list of <see cref="T:MetadataExtractor.Directory"/> instances containing the various types of metadata found within the file's data.</returns>
            <exception cref="T:MetadataExtractor.ImageProcessingException">The file type is unknown, or processing errors occurred.</exception>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="T:MetadataExtractor.ImageProcessingException">
            <summary>An exception class thrown upon an unexpected condition that was fatal for the processing of an image.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.IO.BufferBoundsException">
            <summary>
            Thrown when the index provided to an <see cref="T:MetadataExtractor.IO.IndexedReader"/> is invalid.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.IO.ByteArrayReader">
            <summary>
            Reads values of various data types from a byte array, accessed by index.
            </summary>
            <remarks>
            By default, the reader operates with Motorola byte order (big endianness).  This can be changed by calling
            <see cref="P:MetadataExtractor.IO.IndexedReader.IsMotorolaByteOrder"/>.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.IO.IndexedCapturingReader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.IO.IndexedCapturingReader.Length">
            <summary>
            Returns the length of the data stream this reader is reading from.
            </summary>
            <remarks>
            If the underlying stream's <see cref="P:System.IO.Stream.Length"/> property does not throw <see cref="T:System.NotSupportedException"/> then it can be used directly.
            However if it does throw, then this class has no alternative but to reads to the end of the stream in order to determine the total number of bytes.
            <para />
            In general, this is not a good idea for this implementation of <see cref="T:MetadataExtractor.IO.IndexedReader"/>.
            </remarks>
            <value>The length of the data source, in bytes.</value>
            <exception cref="T:MetadataExtractor.IO.BufferBoundsException"/>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedCapturingReader.ValidateIndex(System.Int32,System.Int32)">
            <summary>Ensures that the buffered bytes extend to cover the specified index. If not, an attempt is made
            to read to that point.</summary>
            <remarks>If the stream ends before the point is reached, a <see cref="T:MetadataExtractor.IO.BufferBoundsException"/> is raised.</remarks>
            <param name="index">the index from which the required bytes start</param>
            <param name="bytesRequested">the number of bytes which are required</param>
            <exception cref="T:MetadataExtractor.IO.BufferBoundsException">if the stream ends before the required number of bytes are acquired</exception>
        </member>
        <member name="T:MetadataExtractor.IO.IndexedReader">
            <summary>Base class for random access data reading operations of common data types.</summary>
            <remarks>
            Concrete implementations include:
            <list type="bullet">
              <item><see cref="T:MetadataExtractor.IO.ByteArrayReader"/></item>
              <item><see cref="T:MetadataExtractor.IO.IndexedSeekingReader"/></item>
              <item><see cref="T:MetadataExtractor.IO.IndexedCapturingReader"/></item>
            </list>
            By default, the reader operates with Motorola byte order (big endianness).  This can be changed by via
            <see cref="P:MetadataExtractor.IO.IndexedReader.IsMotorolaByteOrder"/>.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.IO.IndexedReader.IsMotorolaByteOrder">
            <summary>Get the byte order of this reader.</summary>
            <remarks>
            <list type="bullet">
              <item><c>true</c> for Motorola (or big) endianness (also known as network byte order), with MSB before LSB.</item>
              <item><c>false</c> for Intel (or little) endianness, with LSB before MSB.</item>
            </list>
            </remarks>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetByte(System.Int32)">
            <summary>Gets the byte value at the specified byte <c>index</c>.</summary>
            <remarks>
            Implementations must validate <paramref name="index"/> by calling <see cref="M:MetadataExtractor.IO.IndexedReader.ValidateIndex(System.Int32,System.Int32)"/>.
            </remarks>
            <param name="index">The index from which to read the byte</param>
            <returns>The read byte value</returns>
            <exception cref="T:System.ArgumentException"><c>index</c> is negative</exception>
            <exception cref="T:MetadataExtractor.IO.BufferBoundsException">if the requested byte is beyond the end of the underlying data source</exception>
            <exception cref="T:System.IO.IOException">if the byte is unable to be read</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetBytes(System.Int32,System.Int32)">
            <summary>Returns the required number of bytes from the specified index from the underlying source.</summary>
            <param name="index">The index from which the bytes begins in the underlying source</param>
            <param name="count">The number of bytes to be returned</param>
            <returns>The requested bytes</returns>
            <exception cref="T:System.ArgumentException"><c>index</c> or <c>count</c> are negative</exception>
            <exception cref="T:MetadataExtractor.IO.BufferBoundsException">if the requested bytes extend beyond the end of the underlying data source</exception>
            <exception cref="T:System.IO.IOException">if the byte is unable to be read</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.ValidateIndex(System.Int32,System.Int32)">
            <summary>
            Ensures that the buffered bytes extend to cover the specified index. If not, an attempt is made
            to read to that point.
            </summary>
            <remarks>
            If the stream ends before the point is reached, a <see cref="T:MetadataExtractor.IO.BufferBoundsException"/> is raised.
            </remarks>
            <param name="index">the index from which the required bytes start</param>
            <param name="bytesRequested">the number of bytes which are required</param>
            <exception cref="T:System.IO.IOException">if the stream ends before the required number of bytes are acquired</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.IsValidIndex(System.Int32,System.Int32)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="P:MetadataExtractor.IO.IndexedReader.Length">
            <summary>Returns the length of the data source in bytes.</summary>
            <remarks>
            This is a simple operation for implementations (such as <see cref="T:MetadataExtractor.IO.IndexedSeekingReader"/> and
            <see cref="T:MetadataExtractor.IO.ByteArrayReader"/>) that have the entire data source available.
            <para />
            Users of this method must be aware that sequentially accessed implementations such as
            <see cref="T:MetadataExtractor.IO.IndexedCapturingReader"/>
            will have to read and buffer the entire data source in order to determine the length.
            </remarks>
            <value>the length of the data source, in bytes.</value>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetBit(System.Int32)">
            <summary>Gets whether a bit at a specific index is set or not.</summary>
            <param name="index">the number of bits at which to test</param>
            <returns>true if the bit is set, otherwise false</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetSByte(System.Int32)">
            <summary>Returns a signed 8-bit int calculated from one byte of data at the specified index.</summary>
            <param name="index">position within the data buffer to read byte</param>
            <returns>the 8 bit signed byte value</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetUInt16(System.Int32)">
            <summary>Returns an unsigned 16-bit int calculated from two bytes of data at the specified index.</summary>
            <param name="index">position within the data buffer to read first byte</param>
            <returns>the 16 bit int value, between 0x0000 and 0xFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetInt16(System.Int32)">
            <summary>Returns a signed 16-bit int calculated from two bytes of data at the specified index (MSB, LSB).</summary>
            <param name="index">position within the data buffer to read first byte</param>
            <returns>the 16 bit int value, between 0x0000 and 0xFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetInt24(System.Int32)">
            <summary>Get a 24-bit unsigned integer from the buffer, returning it as an int.</summary>
            <param name="index">position within the data buffer to read first byte</param>
            <returns>the unsigned 24-bit int value as a long, between 0x00000000 and 0x00FFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetUInt32(System.Int32)">
            <summary>Get a 32-bit unsigned integer from the buffer, returning it as a long.</summary>
            <param name="index">position within the data buffer to read first byte</param>
            <returns>the unsigned 32-bit int value as a long, between 0x00000000 and 0xFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetInt32(System.Int32)">
            <summary>Returns a signed 32-bit integer from four bytes of data at the specified index the buffer.</summary>
            <param name="index">position within the data buffer to read first byte</param>
            <returns>the signed 32 bit int value, between 0x00000000 and 0xFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetInt64(System.Int32)">
            <summary>Get a signed 64-bit integer from the buffer.</summary>
            <param name="index">position within the data buffer to read first byte</param>
            <returns>the 64 bit int value, between 0x0000000000000000 and 0xFFFFFFFFFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetS15Fixed16(System.Int32)">
            <summary>Gets a s15.16 fixed point float from the buffer.</summary>
            <remarks>
            This particular fixed point encoding has one sign bit, 15 numerator bits and 16 denominator bits.
            </remarks>
            <returns>the floating point value</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request, or index is negative</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetFloat32(System.Int32)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetDouble64(System.Int32)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetString(System.Int32,System.Int32,System.Text.Encoding)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetNullTerminatedString(System.Int32,System.Int32,System.Text.Encoding)">
            <summary>
            Creates a string starting at the specified index, and ending where either <c>byte=='\0'</c> or
            <c>length==maxLength</c>.
            </summary>
            <param name="index">The index within the buffer at which to start reading the string.</param>
            <param name="maxLengthBytes">
            The maximum number of bytes to read.  If a zero-byte is not reached within this limit,
            reading will stop and the string will be truncated to this length.
            </param>
            <param name="encoding">An optional string encoding. If none is provided, <see cref="P:System.Text.Encoding.UTF8"/> is used.</param>
            <returns>The read <see cref="T:System.String"/></returns>
            <exception cref="T:System.IO.IOException">The buffer does not contain enough bytes to satisfy this request.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetNullTerminatedStringValue(System.Int32,System.Int32,System.Text.Encoding)">
            <summary>
            Creates a string starting at the specified index, and ending where either <c>byte=='\0'</c> or
            <c>length==maxLength</c>.
            </summary>
            <param name="index">The index within the buffer at which to start reading the string.</param>
            <param name="maxLengthBytes">
            The maximum number of bytes to read.  If a zero-byte is not reached within this limit,
            reading will stop and the string will be truncated to this length.
            </param>
            <param name="encoding">An optional string encoding to use when interpreting bytes.</param>
            <returns>The read <see cref="T:MetadataExtractor.StringValue"/></returns>
            <exception cref="T:System.IO.IOException">The buffer does not contain enough bytes to satisfy this request.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.IndexedReader.GetNullTerminatedBytes(System.Int32,System.Int32)">
            <summary>
            Returns the sequence of bytes punctuated by a <c>\0</c> value.
            </summary>
            <param name="index">The index to start reading from.</param>
            <param name="maxLengthBytes">
            The maximum number of bytes to read.  If a <c>\0</c> byte is not reached within this limit,
            the returned array will be <paramref name="maxLengthBytes"/> long.
            </param>
            <returns>The read byte array.</returns>
            <exception cref="T:System.IO.IOException">The buffer does not contain enough bytes to satisfy this request.</exception>
        </member>
        <member name="T:MetadataExtractor.IO.IndexedSeekingReader">
            <summary>
            Provides methods to read data types from a <see cref="T:System.IO.Stream"/> by indexing into the data.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.IO.SequentialByteArrayReader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.IO.SequentialReader">
            <summary>Base class for reading sequentially through a sequence of data encoded in a byte stream.</summary>
            <remarks>
            Concrete implementations include:
            <list type="bullet">
              <item><see cref="T:MetadataExtractor.IO.SequentialByteArrayReader"/></item>
              <item><see cref="T:MetadataExtractor.IO.SequentialStreamReader"/></item>
            </list>
            By default, the reader operates with Motorola byte order (big endianness).  This can be changed by via
            <see cref="P:MetadataExtractor.IO.SequentialReader.IsMotorolaByteOrder"/>.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.IO.SequentialReader.IsMotorolaByteOrder">
            <summary>Get and set the byte order of this reader. <c>true</c> by default.</summary>
            <remarks>
            <list type="bullet">
              <item><c>true</c> for Motorola (or big) endianness (also known as network byte order), with MSB before LSB.</item>
              <item><c>false</c> for Intel (or little) endianness, with LSB before MSB.</item>
            </list>
            </remarks>
            <value><c>true</c> for Motorola/big endian, <c>false</c> for Intel/little endian</value>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetBytes(System.Int32)">
            <summary>Returns the required number of bytes from the sequence.</summary>
            <param name="count">The number of bytes to be returned</param>
            <returns>The requested bytes</returns>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetBytes(System.Byte[],System.Int32,System.Int32)">
            <summary>Retrieves bytes, writing them into a caller-provided buffer.</summary>
            <param name="buffer">The array to write bytes to.</param>
            <param name="offset">The starting position within <paramref name="buffer"/> to write to.</param>
            <param name="count">The number of bytes to be written.</param>
            <returns>The requested bytes</returns>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.Skip(System.Int64)">
            <summary>Skips forward in the sequence.</summary>
            <remarks>
            Skips forward in the sequence. If the sequence ends, an <see cref="T:System.IO.IOException"/> is thrown.
            </remarks>
            <param name="n">the number of byte to skip. Must be zero or greater.</param>
            <exception cref="T:System.IO.IOException">the end of the sequence is reached.</exception>
            <exception cref="T:System.IO.IOException">an error occurred reading from the underlying source.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.TrySkip(System.Int64)">
            <summary>Skips forward in the sequence, returning a boolean indicating whether the skip succeeded, or whether the sequence ended.</summary>
            <param name="n">the number of byte to skip. Must be zero or greater.</param>
            <returns>a boolean indicating whether the skip succeeded, or whether the sequence ended.</returns>
            <exception cref="T:System.IO.IOException">an error occurred reading from the underlying source.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.Available">
            <summary>
            Returns an estimate of the number of bytes that can be read (or skipped over)
            from this SequentialReader without blocking by the next
            invocation of a method for this input stream.A single read or skip of
            this many bytes will not block, but may read or skip fewer bytes.
            </summary>
            <remarks>
            Note that while some implementations of SequentialReader like
            SequentialByteArrayReader will return the total remaining number
            of bytes in the stream, others will not. It is never correct to use the
            return value of this method to allocate a buffer intended to hold all
            data in this stream.
            </remarks>
            <returns>
            an estimate of the number of bytes that can be read (or skipped
            over) from this SequentialReader without blocking or
            0 when it reaches the end of the input stream.
            </returns>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetByte">
            <summary>Returns the next unsigned byte from the sequence.</summary>
            <returns>the 8 bit int value, between 0 and 255</returns>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetSByte">
            <summary>Returns a signed 8-bit int calculated from the next byte the sequence.</summary>
            <returns>the 8 bit int value, between 0x00 and 0xFF</returns>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetUInt16">
            <summary>Returns an unsigned 16-bit int calculated from the next two bytes of the sequence.</summary>
            <returns>the 16 bit int value, between 0x0000 and 0xFFFF</returns>
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetInt16">
            <summary>Returns a signed 16-bit int calculated from two bytes of data (MSB, LSB).</summary>
            <returns>the 16 bit int value, between 0x0000 and 0xFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetUInt32">
            <summary>Get a 32-bit unsigned integer from the buffer, returning it as a long.</summary>
            <returns>the unsigned 32-bit int value as a long, between 0x00000000 and 0xFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetInt32">
            <summary>Returns a signed 32-bit integer from four bytes of data.</summary>
            <returns>the signed 32 bit int value, between 0x00000000 and 0xFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetInt64">
            <summary>Get a signed 64-bit integer from the buffer.</summary>
            <returns>the 64 bit int value, between 0x0000000000000000 and 0xFFFFFFFFFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetUInt64">
            <summary>Get an usigned 64-bit integer from the buffer.</summary>
            <returns>the unsigned 64 bit int value, between 0x0000000000000000 and 0xFFFFFFFFFFFFFFFF</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetS15Fixed16">
            <summary>Gets a s15.16 fixed point float from the buffer.</summary>
            <remarks>
            Gets a s15.16 fixed point float from the buffer.
            <para />
            This particular fixed point encoding has one sign bit, 15 numerator bits and 16 denominator bits.
            </remarks>
            <returns>the floating point value</returns>
            <exception cref="T:System.IO.IOException">the buffer does not contain enough bytes to service the request</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetFloat32">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetDouble64">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetString(System.Int32,System.Text.Encoding)">
            <exception cref="T:System.IO.IOException"/>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetNullTerminatedString(System.Int32,System.Text.Encoding)">
            <summary>
            Creates a <see cref="T:System.String"/> from the stream, ending where <c>byte=='\0'</c> or where <c>length==maxLength</c>.
            </summary>
            <param name="maxLengthBytes">
            The maximum number of bytes to read.  If a <c>\0</c> byte is not reached within this limit,
            reading will stop and the string will be truncated to this length.
            </param>
            <param name="encoding">An optional string encoding. If none is provided, <see cref="P:System.Text.Encoding.UTF8"/> is used.</param>
            <returns>The read <see cref="T:System.String"/></returns>
            <exception cref="T:System.IO.IOException">The buffer does not contain enough bytes to satisfy this request.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetNullTerminatedStringValue(System.Int32,System.Text.Encoding)">
            <summary>
            Creates a <see cref="T:MetadataExtractor.StringValue"/> from the stream, ending where <c>byte=='\0'</c> or where <c>length==maxLength</c>.
            </summary>
            <param name="maxLengthBytes">
            The maximum number of bytes to read.  If a <c>\0</c> byte is not reached within this limit,
            reading will stop and the string will be truncated to this length.
            </param>
            <param name="encoding">An optional string encoding to use when interpreting bytes.</param>
            <returns>The read string as a <see cref="T:MetadataExtractor.StringValue"/>, excluding the null terminator.</returns>
            <exception cref="T:System.IO.IOException">The buffer does not contain enough bytes to satisfy this request.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.GetNullTerminatedBytes(System.Int32)">
            <summary>
            Returns the sequence of bytes punctuated by a <c>\0</c> value.
            </summary>
            <param name="maxLengthBytes">
            The maximum number of bytes to read.  If a <c>\0</c> byte is not reached within this limit,
            the returned array will be <paramref name="maxLengthBytes"/> long.
            </param>
            <returns>The read byte array, excluding the null terminator.</returns>
            <exception cref="T:System.IO.IOException">The buffer does not contain enough bytes to satisfy this request.</exception>
        </member>
        <member name="M:MetadataExtractor.IO.SequentialReader.IsCloserToEnd(System.Int64)">
            <summary>
            Returns true in case the stream supports length checking and distance to the end of the stream is less then number of bytes in parameter.
            Otherwise false.
            </summary>
            <param name="numberOfBytes"></param>
            <returns>True if we going to have an exception while reading next numberOfBytes bytes from the stream</returns>
        </member>
        <member name="T:MetadataExtractor.IO.SequentialStreamReader">
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.ITagDescriptor.GetDescription(System.Int32)">
            <summary>Decodes the raw value stored for <paramref name="tagType"/>.</summary>
            <remarks>
            Where possible, known values will be substituted here in place of the raw
            tokens actually kept in the metadata segment.  If no substitution is
            available, the value provided by <see cref="M:MetadataExtractor.DirectoryExtensions.GetString(MetadataExtractor.Directory,System.Int32)"/> will be returned.
            </remarks>
            <param name="tagType">The tag to find a description for.</param>
            <returns>
            A description of the image's value for the specified tag, or
            <c>null</c> if the tag hasn't been defined.
            </returns>
        </member>
        <member name="T:MetadataExtractor.KeyValuePair">
            <summary>
            Models a key/value pair, where both are non-null <see cref="T:System.String"/> objects.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.MetadataException">
            <summary>Base class for all metadata specific exceptions.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Rational">
            <summary>Immutable type for representing a rational number.</summary>
            <remarks>
            Underlying values are stored as a numerator and denominator, each of type <see cref="T:System.Int64"/>.
            Note that any <see cref="T:MetadataExtractor.Rational"/> with a numerator of zero will be treated as zero, even if the denominator is also zero.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.Rational.Denominator">
            <summary>Gets the denominator.</summary>
        </member>
        <member name="P:MetadataExtractor.Rational.Numerator">
            <summary>Gets the numerator.</summary>
        </member>
        <member name="M:MetadataExtractor.Rational.#ctor(System.Int64,System.Int64)">
            <summary>Initialises a new instance with the <paramref name="numerator"/> and <paramref name="denominator"/>.</summary>
        </member>
        <member name="M:MetadataExtractor.Rational.ToDouble">
            <summary>Returns the value of the specified number as a <see cref="T:System.Double"/>.</summary>
            <remarks>This may involve rounding.</remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToSingle">
            <summary>Returns the value of the specified number as a <see cref="T:System.Single"/>.</summary>
            <remarks>May incur rounding.</remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToByte">
            <summary>Returns the value of the specified number as a <see cref="T:System.Byte"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.Byte"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToSByte">
            <summary>Returns the value of the specified number as a <see cref="T:System.SByte"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.SByte"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToInt32">
            <summary>Returns the value of the specified number as an <see cref="T:System.Int32"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.Int32"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToUInt32">
            <summary>Returns the value of the specified number as an <see cref="T:System.UInt32"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.UInt32"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToInt64">
            <summary>Returns the value of the specified number as a <see cref="T:System.Int64"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.Int64"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToUInt64">
            <summary>Returns the value of the specified number as a <see cref="T:System.UInt64"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.UInt64"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToInt16">
            <summary>Returns the value of the specified number as a <see cref="T:System.Int16"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.Int16"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToUInt16">
            <summary>Returns the value of the specified number as a <see cref="T:System.UInt16"/>.</summary>
            <remarks>
            May incur rounding or truncation.  This implementation simply
            casts the result of <see cref="M:MetadataExtractor.Rational.ToDouble"/> to <see cref="T:System.UInt16"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToDecimal">
            <summary>Returns the value of the specified number as a <see cref="T:System.Decimal"/>.</summary>
            <remarks>May incur truncation.</remarks>
        </member>
        <member name="M:MetadataExtractor.Rational.ToBoolean">
            <summary>Returns <c>true</c> if the value is non-zero, otherwise <c>false</c>.</summary>
        </member>
        <member name="P:MetadataExtractor.Rational.Reciprocal">
            <summary>Gets the reciprocal value of this object as a new <see cref="T:MetadataExtractor.Rational"/>.</summary>
            <value>the reciprocal in a new object</value>
        </member>
        <member name="P:MetadataExtractor.Rational.Absolute">
            <summary>
            Gets the absolute value of this object as a new <see cref="T:MetadataExtractor.Rational"/>.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Rational.IsInteger">
            <summary>
            Checks if this <see cref="T:MetadataExtractor.Rational"/> number is expressible as an integer, either positive or negative.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Rational.IsZero">
            <summary>
            True if either <see cref="P:MetadataExtractor.Rational.Denominator"/> or <see cref="P:MetadataExtractor.Rational.Numerator"/> are zero.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Rational.IsPositive">
            <summary>
            True if <see cref="P:MetadataExtractor.Rational.IsZero"/> is false and <see cref="P:MetadataExtractor.Rational.Numerator"/> and <see cref="P:MetadataExtractor.Rational.Denominator"/> are
            either both positive or both negative.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Rational.ToString">
            <summary>Returns a string representation of the object of form <c>numerator/denominator</c>.</summary>
            <returns>a string representation of the object.</returns>
        </member>
        <member name="M:MetadataExtractor.Rational.ToSimpleString(System.Boolean,System.IFormatProvider)">
            <summary>
            Returns the simplest representation of this <see cref="T:MetadataExtractor.Rational"/>'s value possible.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Rational.Equals(MetadataExtractor.Rational)">
            <summary>
            Indicates whether this instance and <paramref name="other"/> are numerically equal,
            even if their representations differ.
            </summary>
            <remarks>
            For example, <c>1/2</c> is equal to <c>10/20</c> by this method.
            Similarly, <c>1/0</c> is equal to <c>100/0</c> by this method.
            To test equal representations, use <see cref="M:MetadataExtractor.Rational.EqualsExact(MetadataExtractor.Rational)"/>.
            </remarks>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Rational.EqualsExact(MetadataExtractor.Rational)">
            <summary>
            Indicates whether this instance and <paramref name="other"/> have identical
            <see cref="P:MetadataExtractor.Rational.Numerator"/> and <see cref="P:MetadataExtractor.Rational.Denominator"/>.
            </summary>
            <remarks>
            For example, <c>1/2</c> is not equal to <c>10/20</c> by this method.
            Similarly, <c>1/0</c> is not equal to <c>100/0</c> by this method.
            To test numerically equivalence, use <see cref="M:MetadataExtractor.Rational.Equals(MetadataExtractor.Rational)"/>.
            </remarks>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:MetadataExtractor.Rational.GetSimplifiedInstance">
            <summary>
            Simplifies the representation of this <see cref="T:MetadataExtractor.Rational"/> number.
            </summary>
            <remarks>
            For example, <c>5/10</c> simplifies to <c>1/2</c> because both <see cref="P:MetadataExtractor.Rational.Numerator"/>
            and <see cref="P:MetadataExtractor.Rational.Denominator"/> share a common factor of 5.
            <para />
            Uses the Euclidean Algorithm to find the greatest common divisor.
            </remarks>
            <returns>
            A simplified instance if one exists, otherwise a copy of the original value.
            </returns>
        </member>
        <member name="T:MetadataExtractor.StringBuilderExtensions">
            <author>Kevin Mott https://github.com/kwhopper</author>
        </member>
        <member name="M:MetadataExtractor.StringBuilderExtensions.IndexOf(System.Text.StringBuilder,System.Char)">
            <summary>
            Returns the first index of character <paramref name="c"/> in <paramref name="sb"/>,
            or <c>-1</c> if it is not found.
            </summary>
            <param name="sb">The <see cref="T:System.Text.StringBuilder"/> to search within.</param>
            <param name="c">The character to find.</param>
        </member>
        <member name="T:MetadataExtractor.StringValue">
            <summary>
            Wraps a byte array with an <see cref="P:MetadataExtractor.StringValue.Encoding"/>. Allows consumers to override the encoding if required.
            </summary>
            <remarks>
            String data is often in the incorrect format, and many issues have been raised in the past related to string
            encoding. Metadata Extractor used to decode string bytes at read-time, after which it was not possible to
            override the encoding at a later time by the user.
            <para />
            The introduction of this type allows full transparency and control over the use of string data extracted
            by the library during the read phase.
            </remarks>
        </member>
        <member name="F:MetadataExtractor.StringValue.DefaultEncoding">
            <summary>
            The encoding used when decoding a <see cref="T:MetadataExtractor.StringValue"/> that does not specify its encoding.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Tag">
            <summary>
            Models metadata of a tag within a <see cref="T:MetadataExtractor.Directory"/> and provides methods
            for obtaining its value.
            </summary>
            <remarks>Immutable.</remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="P:MetadataExtractor.Tag.Type">
            <summary>Gets the tag type as an int</summary>
            <value>the tag type as an int</value>
        </member>
        <member name="P:MetadataExtractor.Tag.Description">
            <summary>
            Get a description of the tag's value, considering enumerated values
            and units.
            </summary>
            <value>a description of the tag's value</value>
        </member>
        <member name="P:MetadataExtractor.Tag.HasName">
            <summary>Get whether this tag has a name.</summary>
            <remarks>
            If <c>true</c>, it may be accessed via <see cref="P:MetadataExtractor.Tag.Name"/>.
            If <c>false</c>, <see cref="P:MetadataExtractor.Tag.Name"/> will return a string resembling <c>"Unknown tag (0x1234)"</c>.
            </remarks>
        </member>
        <member name="P:MetadataExtractor.Tag.Name">
            <summary>
            Get the name of the tag, such as <c>Aperture</c>, or <c>InteropVersion</c>.
            </summary>
        </member>
        <member name="P:MetadataExtractor.Tag.DirectoryName">
            <summary>
            Get the name of the <see cref="T:MetadataExtractor.Directory"/> in which the tag exists, such as <c>Exif</c>, <c>GPS</c> or <c>Interoperability</c>.
            </summary>
        </member>
        <member name="M:MetadataExtractor.Tag.ToString">
            <summary>A basic representation of the tag's type and value.</summary>
            <remarks>EG: <c>[ExifIfd0] F Number - f/2.8</c>.</remarks>
            <returns>The tag's type and value.</returns>
        </member>
        <member name="T:MetadataExtractor.TagDescriptor`1">
            <summary>Base class for all tag descriptor classes.</summary>
            <remarks>
            Implementations are responsible for providing the human-readable string representation of tag values stored in a directory.
            The directory is provided to the tag descriptor via its constructor.
            </remarks>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.TagDescriptor`1.GetDescription(System.Int32)">
            <summary>Returns a descriptive value of the specified tag for this image.</summary>
            <remarks>
            Where possible, known values will be substituted here in place of the raw
            tokens actually kept in the metadata segment.  If no substitution is
            available, the value provided by <c>getString(tagType)</c> will be returned.
            </remarks>
            <param name="tagType">the tag to find a description for</param>
            <returns>
            a description of the image's value for the specified tag, or
            <c>null</c> if the tag hasn't been defined.
            </returns>
        </member>
        <member name="M:MetadataExtractor.TagDescriptor`1.ConvertBytesToVersionString(System.Int32[],System.Int32)">
            <summary>
            Takes a series of 4 bytes from the specified offset, and converts these to a
            well-known version number, where possible.
            </summary>
            <remarks>
            Two different formats are processed:
            <list type="bullet">
            <item>[0x30 0x32 0x31 0x30] ⇒ 2.10</item>
            <item>[0 1 0 0] ⇒ 1.00</item>
            </list>
            </remarks>
            <param name="components">the four version values</param>
            <param name="majorDigits">the number of components to be</param>
            <returns>the version as a string of form "2.10" or null if the argument cannot be converted</returns>
        </member>
        <member name="M:MetadataExtractor.TagDescriptor`1.GetBitFlagDescription(System.Int32,System.Object[])">
            <remarks>LSB first. Labels may be null, a String, or a String[2] with (low label,high label) values.</remarks>
        </member>
        <member name="T:MetadataExtractor.Util.ByteTrie`1">
            <summary>Stores values using a prefix tree (aka 'trie', i.e. reTRIEval data structure).</summary>
        </member>
        <member name="T:MetadataExtractor.Util.ByteTrie`1.ByteTrieNode">
            <summary>A node in the trie.</summary>
            <remarks>Has children and may have an associated value.</remarks>
        </member>
        <member name="P:MetadataExtractor.Util.ByteTrie`1.MaxDepth">
            <summary>Gets the maximum depth stored in this trie.</summary>
        </member>
        <member name="M:MetadataExtractor.Util.ByteTrie`1.Find(System.Byte[])">
            <summary>Return the most specific value stored for this byte sequence.</summary>
            <remarks>
            If not found, returns <c>null</c> or a default values as specified by
            calling <see cref="M:MetadataExtractor.Util.ByteTrie`1.SetDefaultValue(`0)"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Util.ByteTrie`1.Find(System.Byte[],System.Int32,System.Int32)">
            <summary>Return the most specific value stored for this byte sequence.</summary>
            <remarks>
            If not found, returns <c>null</c> or a default values as specified by
            calling <see cref="M:MetadataExtractor.Util.ByteTrie`1.SetDefaultValue(`0)"/>.
            </remarks>
        </member>
        <member name="M:MetadataExtractor.Util.ByteTrie`1.Add(`0,System.Byte[][])">
            <summary>Store the given value at the specified path.</summary>
        </member>
        <member name="M:MetadataExtractor.Util.ByteTrie`1.SetDefaultValue(`0)">
            <summary>
            Sets the default value to use in <see cref="M:MetadataExtractor.Util.ByteTrie`1.Find(System.Byte[])"/> when no path matches.
            </summary>
        </member>
        <member name="T:MetadataExtractor.Util.DateUtil">
            <summary>
            Utility methods for date and time values.
            </summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="T:MetadataExtractor.Util.FileType">
            <summary>Enumeration of supported image file formats.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Unknown">
            <summary>File type is not known.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Jpeg">
            <summary>Joint Photographic Experts Group (JPEG).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Tiff">
            <summary>Tagged Image File Format (TIFF).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Psd">
            <summary>Photoshop Document.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Png">
            <summary>Portable Network Graphic (PNG).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Bmp">
            <summary>Bitmap (BMP).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Gif">
            <summary>Graphics Interchange Format (GIF).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Ico">
            <summary>Windows Icon.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Pcx">
            <summary>PiCture eXchange.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Riff">
            <summary>Resource Interchange File Format.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Wav">
            <summary>Waveform Audio File Format.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Avi">
            <summary>Audio Video Interleaved.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.WebP">
            <summary>WebP.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Arw">
            <summary>Sony camera raw.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Crw">
            <summary>Canon camera raw (version 1).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Cr2">
            <summary>Canon camera raw (version 2).</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Nef">
            <summary>Nikon camera raw.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Orf">
            <summary>Olympus camera raw.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Raf">
            <summary>Fujifilm camera raw.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Rw2">
            <summary>Panasonic camera raw.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.QuickTime">
            <summary>QuickTime (mov) format video.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Netpbm">
            <summary>Netpbm family of image formats.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Crx">
            <summary>Canon camera raw (version 3).</summary>
            <remarks>Shared by CR3 (image) and CRM (video).</remarks>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Eps">
            <summary>Encapsulated PostScript.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Tga">
            <summary>Truevision graphics.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Mp3">
            <summary>MPEG-1 / MPEG-2 Audio Layer III.</summary>
        </member>
        <member name="F:MetadataExtractor.Util.FileType.Mp4">
            <summary>MPEG-4 Part 14.</summary>
        </member>
        <member name="T:MetadataExtractor.Util.FileTypeDetector">
            <summary>Examines the a file's first bytes and estimates the file's type.</summary>
        </member>
        <member name="M:MetadataExtractor.Util.FileTypeDetector.DetectFileType(System.IO.Stream)">
            <summary>Examines the file's first bytes and estimates the file's type.</summary>
            <exception cref="T:System.ArgumentException">Stream does not support seeking.</exception>
            <exception cref="T:System.IO.IOException">An IO error occurred, or the input stream ended unexpectedly.</exception>
        </member>
        <member name="T:MetadataExtractor.Util.PhotographicConversions">
            <summary>Contains helper methods that perform photographic conversions.</summary>
            <author>Drew Noakes https://drewnoakes.com</author>
        </member>
        <member name="M:MetadataExtractor.Util.PhotographicConversions.ApertureToFStop(System.Double)">
            <summary>Converts an aperture value to its corresponding F-stop number.</summary>
            <param name="aperture">the aperture value to convert</param>
            <returns>the F-stop number of the specified aperture</returns>
        </member>
        <member name="M:MetadataExtractor.Util.PhotographicConversions.ShutterSpeedToExposureTime(System.Double)">
            <summary>Converts a shutter speed to an exposure time.</summary>
            <param name="shutterSpeed">the shutter speed to convert</param>
            <returns>the exposure time of the specified shutter speed</returns>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
    </members>
</doc>
