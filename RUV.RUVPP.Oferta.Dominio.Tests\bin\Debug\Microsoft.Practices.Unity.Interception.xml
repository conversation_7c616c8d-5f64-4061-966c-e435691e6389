<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.Unity.Interception</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface">
            <summary>
            Stores information about a single <see cref="T:System.Type"/> to be an additional interface for an intercepted object and
            configures a container accordingly.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionMember">
            <summary>
            Stores information about a an intercepted object and configures a container accordingly.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface"/> with a 
            <see cref="T:System.Type"/>.
            </summary>
            <param name="additionalInterface">A descriptor representing the interception behavior to use.</param>
            <exception cref="T:System.ArgumentNullException">when <paramref name="additionalInterface"/> is
            <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="additionalInterface"/> is not an interface.
            </exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface.AddPolicies(System.Type,System.Type,System.String,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Add policies to the <paramref name="policies"/> to configure the container to use the represented 
            <see cref="T:System.Type"/> as an additional interface for the supplied parameters.
            </summary>
            <param name="serviceType">Interface being registered.</param>
            <param name="implementationType">Type to register.</param>
            <param name="name">Name used to resolve the type object.</param>
            <param name="policies">Policy list to add policies to.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface`1">
            <summary>
            Stores information about a single <see cref="T:System.Type"/> to be an additional interface for an intercepted object and
            configures a container accordingly.
            </summary>
            <typeparam name="T">The interface.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterface`1"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior">
            <summary>
            An injection member that lets you specify behaviors that should
            apply to all instances of a type in the container regardless
            of what name it's resolved under.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorBase">
            <summary>
            Base class for injection members that allow you to add
            interception behaviors.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorBase.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>.
            </summary>
            <param name="interceptionBehavior">The interception behavior to use.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorBase.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            given type/name pair.
            </summary>
            <param name="behaviorType">Type of behavior to </param>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorBase.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            given behavior type.
            </summary>
            <param name="behaviorType">Type of behavior to </param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorBase.AddPolicies(System.Type,System.Type,System.String,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Add policies to the <paramref name="policies"/> to configure the container to use the represented 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> for the supplied parameters.
            </summary>
            <param name="serviceType">Interface being registered.</param>
            <param name="implementationType">Type to register.</param>
            <param name="name">Name used to resolve the type object.</param>
            <param name="policies">Policy list to add policies to.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorBase.GetBehaviorsPolicy(Microsoft.Practices.ObjectBuilder2.IPolicyList,System.Type,System.String)">
            <summary>
            Get the list of behaviors for the current type so that it can be added to.
            </summary>
            <param name="policies">Policy list.</param>
            <param name="implementationType">Implementation type to set behaviors for.</param>
            <param name="name">Name type is registered under.</param>
            <returns>An instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorsPolicy"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior"/> that will
            supply the given interception behavior to the container.
            </summary>
            <param name="interceptionBehavior">Behavior to apply to this type.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior.#ctor(System.Type,System.String)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior"/> that will
            resolve the given type/name pair to get the behavior.
            </summary>
            <param name="behaviorType">Type of behavior.</param>
            <param name="name">Name for behavior registration.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior.#ctor(System.Type)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior"/> that will
            resolve the given type to get the behavior.
            </summary>
            <param name="behaviorType">Type of behavior.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior.GetBehaviorsPolicy(Microsoft.Practices.ObjectBuilder2.IPolicyList,System.Type,System.String)">
            <summary>
            Get the list of behaviors for the current type so that it can be added to.
            </summary>
            <param name="policies">Policy list.</param>
            <param name="implementationType">Implementation type to set behaviors for.</param>
            <param name="name">Name type is registered under.</param>
            <returns>An instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorsPolicy"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior`1">
            <summary>
            A generic version of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior"/> so you
            can give the behavior type using generic syntax.
            </summary>
            <typeparam name="TBehavior">Type of the behavior object to apply.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior`1.#ctor(System.String)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior`1"/> instance
            that use the given type and name to resolve the behavior object.
            </summary>
            <param name="name">Name of the registration.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior`1.#ctor">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptionBehavior`1"/> instance
            that uses the given type to resolve the behavior object.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor">
            <summary>
            A <see cref="T:Microsoft.Practices.Unity.InjectionMember"/> that can be passed to the
            <see cref="M:Microsoft.Practices.Unity.IUnityContainer.RegisterType(System.Type,System.Type,System.String,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])"/> method to specify
            which interceptor to use. This member sets up the default
            interceptor for a type - this will be used regardless of which 
            name is used to resolve the type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInterceptor)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor"/> instance that,
            when applied to a container, will register the given
            interceptor as the default one.
            </summary>
            <param name="interceptor">Interceptor to use.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor.#ctor(System.Type,System.String)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor"/> that, when
            applied to a container, will register the given type as
            the default interceptor. 
            </summary>
            <param name="interceptorType">Type of interceptor.</param>
            <param name="name">Name to use to resolve the interceptor.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor.#ctor(System.Type)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor"/> that, when
            applied to a container, will register the given type as
            the default interceptor. 
            </summary>
            <param name="interceptorType">Type of interceptor.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor.AddPolicies(System.Type,System.Type,System.String,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Add policies to the <paramref name="policies"/> to configure the
            container to call this constructor with the appropriate parameter values.
            </summary>
            <param name="serviceType">Type of interface being registered. If no interface,
            this will be null.</param>
            <param name="implementationType">Type of concrete type being registered.</param>
            <param name="name">Name used to resolve the type object.</param>
            <param name="policies">Policy list to add policies to.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor`1">
            <summary>
            A generic version of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor"/> so that
            you can specify the interceptor type using generics.
            </summary>
            <typeparam name="TInterceptor"></typeparam>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor`1.#ctor(System.String)">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor`1"/>.
            </summary>
            <param name="name">Name to use when resolving interceptor.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor`1.#ctor">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.DefaultInterceptor`1"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterfacesPolicy">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IAdditionalInterfacesPolicy"/> that accumulates a sequence of 
            <see cref="T:System.Type"/> instances representing the additional interfaces for an intercepted object.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IAdditionalInterfacesPolicy">
            <summary>
            An <see cref="T:Microsoft.Practices.ObjectBuilder2.IBuilderPolicy"/> that returns a sequence of <see cref="T:System.Type"/> 
            instances representing the additional interfaces for an intercepted object.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IAdditionalInterfacesPolicy.AdditionalInterfaces">
            <summary>
            Gets the <see cref="T:System.Type"/> instances accumulated by this policy.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterfacesPolicy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterfacesPolicy"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.AdditionalInterfacesPolicy.AdditionalInterfaces">
            <summary>
            Gets the <see cref="T:System.Type"/> instances accumulated by this policy.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ResolvedInstanceInterceptionPolicy">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy"/> that will
            resolve the interceptor through the container.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy">
            <summary>
            An interface that determines when to intercept instances
            and which interceptor to use.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy.GetInterceptor(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Interceptor to use.
            </summary>
            <param name="context">Context for current build operation.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ResolvedInstanceInterceptionPolicy.#ctor(Microsoft.Practices.ObjectBuilder2.NamedTypeBuildKey)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ResolvedInstanceInterceptionPolicy"/> that
            will resolve the interceptor using the given build key.
            </summary>
            <param name="buildKey">build key to resolve.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ResolvedInstanceInterceptionPolicy.GetInterceptor(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Interceptor to use.
            </summary>
            <param name="context">Context for current build operation.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ResolvedTypeInterceptionPolicy">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptionPolicy"/> that will
            resolve the interceptor through the container.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptionPolicy">
            <summary>
            Interface that controls when and how types get intercepted.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptionPolicy.GetInterceptor(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Interceptor to use to create type proxy
            </summary>
            <param name="context">Context for current build operation.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptionPolicy.ProxyType">
            <summary>
            Cache for proxied type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ResolvedTypeInterceptionPolicy.#ctor(Microsoft.Practices.ObjectBuilder2.NamedTypeBuildKey)">
            <summary>
            construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ResolvedTypeInterceptionPolicy"/> that
            will resolve the interceptor with the given <paramref name="buildKey"/>.
            </summary>
            <param name="buildKey">The build key to use to resolve.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ResolvedTypeInterceptionPolicy.GetInterceptor(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Interceptor to use to create type proxy
            </summary>
            <param name="context">Context for current build operation.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ResolvedTypeInterceptionPolicy.ProxyType">
            <summary>
            Cache for proxied type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Intercept">
            <summary>
            High-level API for performing interception on existing and new objects.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.ThroughProxyWithAdditionalInterfaces``1(``0,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy"/> for type <typeparamref name="T"/> which wraps 
            the supplied <paramref name="target"/>.
            </summary>
            <typeparam name="T">The type to intercept.</typeparam>
            <param name="target">The instance to intercept.</param>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="additionalInterfaces">Any additional interfaces the proxy must implement.</param>
            <returns>A proxy for <paramref name="target"/> compatible with <typeparamref name="T"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="target"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="additionalInterfaces"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <typeparamref name="T"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.ThroughProxy``1(``0,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior})">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy"/> for type <typeparamref name="T"/> which wraps
            the supplied <paramref name="target"/>.
            </summary>
            <typeparam name="T">Type to intercept.</typeparam>
            <param name="target">The instance to intercept.</param>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <returns>A proxy for <paramref name="target"/> compatible with <typeparamref name="T"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="target"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <typeparamref name="T"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.ThroughProxyWithAdditionalInterfaces(System.Type,System.Object,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy"/> for type <paramref name="interceptedType"/> which wraps 
            the supplied <paramref name="target"/>.
            </summary>
            <param name="interceptedType">The type to intercept.</param>
            <param name="target">The instance to intercept.</param>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="additionalInterfaces">Any additional interfaces the proxy must implement.</param>
            <returns>A proxy for <paramref name="target"/> compatible with <paramref name="interceptedType"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptedType"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="target"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="additionalInterfaces"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <paramref name="interceptedType"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.ThroughProxy(System.Type,System.Object,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior})">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy"/> for type <paramref name="interceptedType"/> which wraps 
            the supplied <paramref name="target"/>.
            </summary>
            <param name="interceptedType">The type to intercept.</param>
            <param name="target">The instance to intercept.</param>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <returns>A proxy for <paramref name="target"/> compatible with <paramref name="interceptedType"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptedType"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="target"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <paramref name="interceptedType"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.NewInstanceWithAdditionalInterfaces``1(Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Collections.Generic.IEnumerable{System.Type},System.Object[])">
            <summary>
            Creates a new instance of type <typeparamref name="T"/> that is intercepted with the behaviors in 
            <paramref name="interceptionBehaviors"/>.
            </summary>
            <typeparam name="T">The type of the object to create.</typeparam>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="additionalInterfaces">Any additional interfaces the proxy must implement.</param>
            <param name="constructorParameters">The arguments for the creation of the new instance.</param>
            <returns>An instance of a class compatible with <typeparamref name="T"/> that includes execution of the
            given <paramref name="interceptionBehaviors"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">When <paramref name="additionalInterfaces"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <typeparamref name="T"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.NewInstance``1(Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Object[])">
            <summary>
            Creates a new instance of type <typeparamref name="T"/> that is intercepted with the behaviors in 
            <paramref name="interceptionBehaviors"/>.
            </summary>
            <typeparam name="T">The type of the object to create.</typeparam>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="constructorParameters">The arguments for the creation of the new instance.</param>
            <returns>An instance of a class compatible with <typeparamref name="T"/> that includes execution of the
            given <paramref name="interceptionBehaviors"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <typeparamref name="T"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.NewInstanceWithAdditionalInterfaces(System.Type,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Collections.Generic.IEnumerable{System.Type},System.Object[])">
            <summary>
            Creates a new instance of type <paramref name="type"/> that is intercepted with the behaviors in 
            <paramref name="interceptionBehaviors"/>.
            </summary>
            <param name="type">The type of the object to create.</param>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="additionalInterfaces">Any additional interfaces the instance must implement.</param>
            <param name="constructorParameters">The arguments for the creation of the new instance.</param>
            <returns>An instance of a class compatible with <paramref name="type"/> that includes execution of the
            given <paramref name="interceptionBehaviors"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="type"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="additionalInterfaces"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <paramref name="type"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.NewInstance(System.Type,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Object[])">
            <summary>
            Creates a new instance of type <paramref name="type"/> that is intercepted with the behaviors in 
            <paramref name="interceptionBehaviors"/>.
            </summary>
            <param name="type">The type of the object to create.</param>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor"/> to use when creating the proxy.</param>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="constructorParameters">The arguments for the creation of the new instance.</param>
            <returns>An instance of a class compatible with <paramref name="type"/> that includes execution of the
            given <paramref name="interceptionBehaviors"/>.</returns>
            <exception cref="T:System.ArgumentNullException">when <paramref name="type"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptionBehaviors"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentException">when <paramref name="interceptor"/> cannot intercept 
            <paramref name="type"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Intercept.GetAllAdditionalInterfaces(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior},System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Computes the array with all the additional interfaces for the interception of an object.
            </summary>
            <param name="interceptionBehaviors">The interception behaviors for the new proxy.</param>
            <param name="additionalInterfaces">Any additional interfaces the instance must implement.</param>
            <returns>An array with the required interfaces for </returns>
            <exception cref="T:System.ArgumentException">when the interfaces are not valid.</exception>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor">
            <summary>
            Stores information about the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor"/> to be used to intercept an object and
            configures a container accordingly.
            </summary>
            <seealso cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interceptor.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInterceptor)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor"/> class with an interceptor instance.
            </summary>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor"/> to use.</param>
            <exception cref="T:System.ArgumentNullException">when <paramref name="interceptor"/> is
            <see langword="null"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interceptor.#ctor(System.Type,System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor"/> class with a given
            name and type that will be resolved to provide interception.
            </summary>
            <param name="interceptorType">Type of the interceptor</param>
            <param name="name">name to use to resolve.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interceptor.#ctor(System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor"/> class with
            a given type that will be resolved to provide interception.
            </summary>
            <param name="interceptorType">Type of the interceptor.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interceptor.AddPolicies(System.Type,System.Type,System.String,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Add policies to the <paramref name="policies"/> to configure the container to use the represented 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor"/> for the supplied parameters.
            </summary>
            <param name="serviceType">Interface being registered.</param>
            <param name="implementationType">Type to register.</param>
            <param name="name">Name used to resolve the type object.</param>
            <param name="policies">Policy list to add policies to.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor`1">
            <summary>
            Generic version of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor"/> that lets you specify an interceptor
            type using generic syntax.
            </summary>
            <typeparam name="TInterceptor">Type of interceptor</typeparam>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interceptor`1.#ctor">
            <summary>
            Initialize an instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor`1"/> that will
            resolve the given interceptor type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interceptor`1.#ctor(System.String)">
            <summary>
            Initialize an instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Interceptor`1"/> that will
            resolve the given interceptor type and name.
            </summary>
            <param name="name">Name that will be used to resolve the interceptor.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest">
            <summary>
            A simple data holder class used to store information about the current
            interception operation that's being set up. Useful for creating behaviors
            that need to know this stuff (especially the PIAB behavior).
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInterceptor,System.Type,System.Type)">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest"/> that
            stores the given <paramref name="interceptor"/>,
            <paramref name="typeToIntercept"/>, and <paramref name="implementationType"/>.
            </summary>
            <param name="interceptor"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor"/> that will be used to
            create the intercepting type or proxy.</param>
            <param name="typeToIntercept">Type that interception was requested on.</param>
            <param name="implementationType">Type of the object that will actually be intercepted.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest.Interceptor">
            <summary>
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor"/> that will be used to
            create the intercepting type or proxy.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest.TypeToIntercept">
            <summary>
            Type that interception was requested on.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest.ImplementationType">
            <summary>
            Type of the object that will actually be intercepted.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper">
            <summary>
            Maps types involving generic parameter types from reflected types into equivalent versions involving generated types.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.#ctor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper"/> class.
            </summary>
            <param name="type">A constructed generic type, open or closed.</param>
            <param name="parent">The parent mapper, or <see langword="null"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.#ctor(System.Type[],System.Type[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper"/> class.
            </summary>
            <param name="reflectedParameters">The reflected generic parameters.</param>
            <param name="generatedParameters">The generated generic parameters.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.#ctor(System.Type[],System.Type[],Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper"/> class.
            </summary>
            <param name="reflectedParameters">The reflected generic parameters.</param>
            <param name="generatedParameters">The generated generic parameters.</param>
            <param name="parent">The parent mapper, or <see langword="null"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.Map(System.Type)">
            <summary>
            Maps a type to an equivalent type involving generated types.
            </summary>
            <param name="typeToMap">The type to map.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.GetReflectedParameters">
            <summary>
            Gets the formal parameters.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.GetGeneratedParameters">
            <summary>
            Gets the actual parameters.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.GenericParameterMapper.DefaultMapper">
            <summary>
            Gets the default mapper.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy">
            <summary>
            This interface is implemented by all proxy objects, type or instance based.
            It allows for adding interception behaviors.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy.AddInterceptionBehavior(Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior)">
            <summary>
            Adds a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> to the proxy.
            </summary>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> to add.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor">
            <summary>
            Base interface for type and instance based interceptor classes.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor.CanIntercept(System.Type)">
            <summary>
            Can this interceptor generate a proxy for the given type?
            </summary>
            <param name="t">Type to check.</param>
            <returns>True if interception is possible, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInterceptor.GetInterceptableMethods(System.Type,System.Type)">
            <summary>
            Returns a sequence of methods on the given type that can be
            intercepted.
            </summary>
            <param name="interceptedType">Type that was specified when this interceptor
            was created (typically an interface).</param>
            <param name="implementationType">The concrete type of the implementing object.</param>
            <returns>Sequence of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo"/> objects.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor">
            <summary>
            Interface for interceptors that generate separate proxy
            objects to implement interception on instances.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor.CreateProxy(System.Type,System.Object,System.Type[])">
            <summary>
            Create a proxy object that provides interception for <paramref name="target"/>.
            </summary>
            <param name="t">Type to generate the proxy of.</param>
            <param name="target">Object to create the proxy for.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
            <returns>The proxy object.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.FixedInstanceInterceptionPolicy">
            <summary>
            Implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptionPolicy"/> that returns a
            pre-created interceptor.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.FixedInstanceInterceptionPolicy.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.FixedInstanceInterceptionPolicy"/>.
            </summary>
            <param name="interceptor">Interceptor to store.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.FixedInstanceInterceptionPolicy.GetInterceptor(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Interceptor to use.
            </summary>
            <param name="context">Context for current build operation.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InstanceInterceptionStrategy">
            <summary>
            A <see cref="T:Microsoft.Practices.ObjectBuilder2.IBuilderStrategy"/> that intercepts objects
            in the build chain by creating a proxy object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InstanceInterceptionStrategy.PostBuildUp(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Called during the chain of responsibility for a build operation. The
            PostBuildUp method is called when the chain has finished the PreBuildUp
            phase and executes in reverse order from the PreBuildUp calls.
            </summary>
            <param name="context">Context of the build operation.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptor">
            <summary>
            An instance interceptor that works by generating a
            proxy class on the fly for a single interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptor.CanIntercept(System.Type)">
            <summary>
            Can this interceptor generate a proxy for the given type?
            </summary>
            <param name="t">Type to check.</param>
            <returns>True if interception is possible, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptor.GetInterceptableMethods(System.Type,System.Type)">
            <summary>
            Returns a sequence of methods on the given type that can be
            intercepted.
            </summary>
            <param name="interceptedType">Type that was specified when this interceptor
            was created (typically an interface).</param>
            <param name="implementationType">The concrete type of the implementing object.</param>
            <returns>Sequence of <see cref="T:System.Reflection.MethodInfo"/> objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptor.CreateProxy(System.Type,System.Object,System.Type[])">
            <summary>
            Create a proxy object that provides interception for <paramref name="target"/>.
            </summary>
            <param name="t">Type to generate the proxy of.</param>
            <param name="target">Object to create the proxy for.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
            <returns>The proxy object.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptorClassGenerator">
            <summary>
            A class used to generate proxy classes for doing interception on
            interfaces.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptorClassGenerator.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Create an instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptorClassGenerator"/> that
            can construct an intercepting proxy for the given interface.
            </summary>
            <param name="typeToIntercept">Type of the interface to intercept.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterfaceInterceptorClassGenerator.CreateProxyType">
            <summary>
            Create the type to proxy the requested interface
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterfaceMethodOverride">
            <summary>
            Represents the implementation of an interface method.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterfaceMethodOverride.BuildAdditionalInterfaceNonImplementedException">
            <summary>
            Used to throw an <see cref="T:System.NotImplementedException"/> for non-implemented methods on the
            additional interfaces.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy">
            <summary>
            This class provides the remoting based interception mechanism. It is
            invoked by a call on the corresponding TransparentProxy
            object. It routes calls through the handlers as appropriate.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy.#ctor(System.Object,System.Type,System.Type[])">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy"/> instance that applies
            the given policies to the given target object.
            </summary>
            <param name="target">Target object to intercept calls to.</param>
            <param name="classToProxy">Type to return as the type being proxied.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy.AddInterceptionBehavior(Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior)">
            <summary>
            Adds a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> to the proxy.
            </summary>
            <param name="interceptor">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> to add.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy.CanCastTo(System.Type,System.Object)">
            <summary>
            Checks whether the proxy that represents the specified object type can be cast to the type represented by the <see cref="T:System.Runtime.Remoting.IRemotingTypeInfo"></see> interface.
            </summary>
            <returns>
            true if cast will succeed; otherwise, false.
            </returns>
            <param name="fromType">The type to cast to. </param>
            <param name="o">The object for which to check casting. </param>
            <exception cref="T:System.Security.SecurityException">The immediate caller makes the call through a reference to the interface and does not have infrastructure permission. </exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy.Invoke(System.Runtime.Remoting.Messaging.IMessage)">
            <summary>
            Executes a method call represented by the <paramref name="msg"/>
            parameter. The CLR will call this method when a method is called
            on the TransparentProxy. This method runs the invocation through
            the call handler pipeline and finally sends it down to the
            target object, and then back through the pipeline. 
            </summary>
            <param name="msg">An <see cref="T:System.Runtime.Remoting.Messaging.IMessage"/> object that contains the information
            about the method call.</param>
            <returns>An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn"/> object contains the
            information about the target method's return value.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy.Target">
            <summary>
            Returns the target of this intercepted call.
            </summary>
            <value>The target object.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.InterceptingRealProxy.TypeName">
            <summary>
            Gets or sets the fully qualified type name of the server object in a <see cref="T:System.Runtime.Remoting.ObjRef"></see>.
            </summary>
            <value>
            The fully qualified type name of the server object in a <see cref="T:System.Runtime.Remoting.ObjRef"></see>.
            </value>
            <exception cref="T:System.Security.SecurityException">The immediate caller makes the call through a reference to the interface and does not have infrastructure permission. </exception><PermissionSet><IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Infrastructure" /></PermissionSet>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInterceptor">
            <summary>
            An instance interceptor that uses remoting proxies to do the
            interception.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInterceptor.CanIntercept(System.Type)">
            <summary>
            Can this interceptor generate a proxy for the given type?
            </summary>
            <param name="t">Type to check.</param>
            <returns>True if interception is possible, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInterceptor.GetInterceptableMethods(System.Type,System.Type)">
            <summary>
            Returns a sequence of methods on the given type that can be
            intercepted.
            </summary>
            <param name="interceptedType">The intercepted type.</param>
            <param name="implementationType">The concrete type of the implementing object.</param>
            <returns>Sequence of <see cref="T:System.Reflection.MethodInfo"/> objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInterceptor.CreateProxy(System.Type,System.Object,System.Type[])">
            <summary>
            Create a proxy object that provides interception for <paramref name="target"/>.
            </summary>
            <param name="t">Type to generate the proxy of.</param>
            <param name="target">Object to create the proxy for.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
            <returns>The proxy object.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo">
            <summary>
            A dumb data holder that returns the MethodInfo for both an
            interface method and the method that implements that interface
            method.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.#ctor(System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo"/> which holds
            the given <see cref="T:System.Reflection.MethodInfo"/> objects.
            </summary>
            <param name="interfaceMethodInfo">MethodInfo for the interface method (may be null if no interface).</param>
            <param name="implementationMethodInfo">MethodInfo for implementing method.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />.
            </summary>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the current <see cref="T:System.Object" />; otherwise, false.
            </returns>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current <see cref="T:System.Object" />. </param>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj" /> parameter is null.</exception>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.GetHashCode">
            <summary>
                                Serves as a hash function for a particular type. 
            </summary>
            <returns>
                                A hash code for the current <see cref="T:System.Object" />.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.op_Equality(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Standard equals operator
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.op_Inequality(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            standard not equal operator.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.ToString">
            <summary>
                                Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </summary>
            <returns>
                                A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.InterfaceMethodInfo">
            <summary>
            The interface method MethodInfo.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo.ImplementationMethodInfo">
            <summary>
            The implementing method MethodInfo.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor">
            <summary>
            Interface for interceptor objects that generate
            proxy types.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor.CreateProxyType(System.Type,System.Type[])">
            <summary>
            Create a type to proxy for the given type <paramref name="t"/>.
            </summary>
            <param name="t">Type to proxy.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
            <returns>New type that can be instantiated instead of the
            original type t, and supports interception.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.FixedTypeInterceptionPolicy">
            <summary>
            Implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptionPolicy"/> that returns a pre-created
            interceptor object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.FixedTypeInterceptionPolicy.#ctor(Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.FixedTypeInterceptionPolicy"/> that
            uses the given <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor"/>.
            </summary>
            <param name="interceptor">Interceptor to use.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.FixedTypeInterceptionPolicy.GetInterceptor(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Interceptor to use to create type proxy
            </summary>
            <param name="context">Context for current build operation.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.FixedTypeInterceptionPolicy.ProxyType">
            <summary>
            Cache for proxied type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TypeInterceptionStrategy">
            <summary>
            A <see cref="T:Microsoft.Practices.ObjectBuilder2.IBuilderStrategy"/> that hooks up type interception. It looks for
            a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptionPolicy"/> for the current build key, or the current
            build type. If present, it substitutes types so that that proxy class gets
            built up instead. On the way back, it hooks up the appropriate handlers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeInterceptionStrategy.PreBuildUp(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Called during the chain of responsibility for a build operation. The
            PreBuildUp method is called when the chain is being executed in the
            forward direction.
            </summary>
            <remarks>In this class, PreBuildUp is responsible for figuring out if the
            class is proxyable, and if so, replacing it with a proxy class.</remarks>
            <param name="context">Context of the build operation.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeInterceptionStrategy.PostBuildUp(Microsoft.Practices.ObjectBuilder2.IBuilderContext)">
            <summary>
            Called during the chain of responsibility for a build operation. The
            PostBuildUp method is called when the chain has finished the PreBuildUp
            phase and executes in reverse order from the PreBuildUp calls.
            </summary>
            <remarks>In this class, PostBuildUp checks to see if the object was proxyable,
            and if it was, wires up the handlers.</remarks>
            <param name="context">Context of the build operation.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptingClassGenerator">
            <summary>
            Class that handles generating the dynamic types used for interception.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptingClassGenerator.#ctor(System.Type,System.Type[])">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptingClassGenerator"/> that will generate a
            wrapper class for the requested <paramref name="typeToIntercept"/>.
            </summary>
            <param name="typeToIntercept">Type to generate the wrapper for.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptingClassGenerator.GenerateType">
            <summary>
            Create the wrapper class for the given type.
            </summary>
            <returns>Wrapper type.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptingProxyImplementor">
            <summary>
            This class provides the code needed to implement the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptingProxy"/>
            interface on a class.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MethodOverrideParameterMapper">
            <summary>
            This class handles parameter type mapping. When we generate
            a generic method, we need to make sure our parameter type
            objects line up with the generic parameters on the generated
            method, not on the one we're overriding. 
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter">
            <summary>
            A utility class that takes a set of <see cref="T:System.Reflection.MethodInfo"/>s
            and pulls out shadowed methods, only returning the ones that
            are actually accessible to be overridden.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.GroupMethodsByName(System.Collections.Generic.IEnumerable{System.Reflection.MethodInfo})">
            <summary>
            Take the list of methods and put them together into lists index by method name.
            </summary>
            <param name="methodsToSort">Methods to sort through.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.RemoveHiddenOverloads(System.Collections.Generic.IEnumerable{System.Reflection.MethodInfo})">
            <summary>
            Given a list of overloads for a method, return only those methods
            that are actually visible. In other words, if there's a "new SomeType" method
            somewhere, return only the new one, not the one from the base class
            that's now hidden.
            </summary>
            <param name="methods">Sequence of methods to process.</param>
            <returns>Sequence of returned methods.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.GroupOverloadedMethods(System.Collections.Generic.IList{System.Reflection.MethodInfo})">
            <summary>
            Take a semi-randomly ordered set of methods on a type and
            sort them into groups by name and by parameter list.
            </summary>
            <param name="sortedMethods">The list of methods.</param>
            <returns>Sequence of lists of methods, grouped by method name.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.SelectMostDerivedOverload(System.Collections.Generic.IList{System.Reflection.MethodInfo})">
            <summary>
            Given a set of hiding overloads, return only the currently visible one.
            </summary>
            <param name="overloads">The set of overloads.</param>
            <returns>The most visible one.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.DeclarationDepth(System.Reflection.MethodInfo)">
            <summary>
            Given a method, return a value indicating how deeply in the
            inheritance hierarchy the method is declared. Current type = 0,
            parent = 1, grandparent = 2, etc.
            </summary>
            <param name="method">Method to check.</param>
            <returns>Declaration depth</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.CompareMethodInfosByParameterLists(System.Reflection.MethodInfo,System.Reflection.MethodInfo)">
            <summary>
            A <see cref="T:System.Comparison`1"/> implementation that can compare two <see cref="T:System.Reflection.MethodInfo"/>
            based on their parameter lists.
            </summary>
            <param name="left">First <see cref="T:System.Reflection.MethodInfo"/> to compare.</param>
            <param name="right">Second <see cref="T:System.Reflection.MethodInfo"/> to compare.</param>
            <returns>&lt; 0, 0, or &gt; 0 based on which one is "greater" than the other.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.CompareParameterLists(System.Reflection.ParameterInfo[],System.Reflection.ParameterInfo[])">
            <summary>
            Compare two parameter lists.
            </summary>
            <param name="left">First parameter list.</param>
            <param name="right">Second parameter list.</param>
            <returns>&lt; 0, 0, or &gt; 0.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSorter.CompareParameterInfo(System.Reflection.ParameterInfo,System.Reflection.ParameterInfo)">
            <summary>
            Compare two <see cref="T:System.Reflection.ParameterInfo"/> objects by type.
            </summary>
            <param name="left">First <see cref="T:System.Reflection.ParameterInfo"/></param>
            <param name="right">First <see cref="T:System.Reflection.ParameterInfo"/></param>
            <returns>&lt; 0, 0, or &gt; 0</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInterceptor">
            <summary>
            A type based interceptor that works by generated a new class
            on the fly that derives from the target class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInterceptor.CanIntercept(System.Type)">
            <summary>
            Can this interceptor generate a proxy for the given type?
            </summary>
            <param name="t">Type to check.</param>
            <returns>True if interception is possible, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInterceptor.GetInterceptableMethods(System.Type,System.Type)">
            <summary>
            Returns a sequence of methods on the given type that can be
            intercepted.
            </summary>
            <param name="interceptedType">The intercepted type.</param>
            <param name="implementationType">The concrete type of the implementing object.</param>
            <returns>Sequence of <see cref="T:System.Reflection.MethodInfo"/> objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInterceptor.CreateProxyType(System.Type,System.Type[])">
            <summary>
            Create a type to proxy for the given type <paramref name="t"/>.
            </summary>
            <param name="t">Type to proxy.</param>
            <param name="additionalInterfaces">Additional interfaces the proxy must implement.</param>
            <returns>New type that can be instantiated instead of the
            original type t, and supports interception.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehaviorsPolicy">
            <summary>
            An <see cref="T:Microsoft.Practices.ObjectBuilder2.IBuilderPolicy"/> that returns a sequence of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> 
            instances for an intercepted object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehaviorsPolicy.GetEffectiveBehaviors(Microsoft.Practices.ObjectBuilder2.IBuilderContext,Microsoft.Practices.Unity.InterceptionExtension.IInterceptor,System.Type,System.Type)">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> object to be used for the given type and
            interceptor.
            </summary>
            <remarks>
            This method will return a sequence of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>s. These behaviors will
            only be included if their <see cref="P:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior.WillExecute"/> properties are true.
            </remarks>
            <param name="context">Context for the current build operation.</param>
            <param name="interceptor">Interceptor that will be used to invoke the behavior.</param>
            <param name="typeToIntercept">Type that interception was requested on.</param>
            <param name="implementationType">Type that implements the interception.</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehaviorsPolicy.BehaviorKeys">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.ObjectBuilder2.NamedTypeBuildKey"/> that can be used to resolve the
            behaviors.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey">
            <summary>
            Key for handler pipelines.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey.ForMethod(System.Reflection.MethodBase)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey"/> for the supplied method.
            </summary>
            <param name="methodBase">The method for the key.</param>
            <returns>The new key.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey.Equals(System.Object)">
            <summary>
            Compare two <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey"/> instances.
            </summary>
            <param name="obj">Object to compare to.</param>
            <returns>True if the two keys are equal, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey.GetHashCode">
            <summary>
            Calculate a hash code for this instance.
            </summary>
            <returns>A hash code.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey.op_Equality(Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey,Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey)">
            <summary>
            Compare two <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey"/> instances for equality.
            </summary>
            <param name="left">First of the two keys to compare.</param>
            <param name="right">Second of the two keys to compare.</param>
            <returns>True if the values of the keys are the same, else false.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey.op_Inequality(Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey,Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey)">
            <summary>
            Compare two <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey"/> instances for inequality.
            </summary>
            <param name="left">First of the two keys to compare.</param>
            <param name="right">Second of the two keys to compare.</param>
            <returns>false if the values of the keys are the same, else true.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey.Equals(Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey)">
            <summary>
            Compare two <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipelineKey"/> instances.
            </summary>
            <param name="other">Object to compare to.</param>
            <returns>True if the two keys are equal, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.UtilityExtensions">
            <summary>
            Some utility extension methods to make things portable to Silverlight.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PipelineManager">
            <summary>
            A collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline"/> objects, indexed
            by <see cref="T:System.Reflection.MethodBase"/>. Returns an empty pipeline if a
            MethodBase is requested that isn't in the dictionary.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PipelineManager.GetPipeline(System.Reflection.MethodBase)">
            <summary>
            Retrieve the pipeline associated with the requested <paramref name="method"/>.
            </summary>
            <param name="method">The method for which the pipeline is being requested.</param>
            <returns>The handler pipeline for the given method. If no pipeline has
            been set, returns a new empty pipeline.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PipelineManager.SetPipeline(System.Reflection.MethodBase,Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline)">
            <summary>
            Set a new pipeline for a method.
            </summary>
            <param name="method">The method on which the pipeline should be set.</param>
            <param name="pipeline">The new pipeline.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PipelineManager.InitializePipeline(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.ICallHandler})">
            <summary>
            Get the pipeline for the given method, creating it if necessary.
            </summary>
            <param name="method">Method to retrieve the pipeline for.</param>
            <param name="handlers">Handlers to initialize the pipeline with</param>
            <returns>True if the pipeline has any handlers in it, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ApplyNoPoliciesAttribute">
            <summary>
            Attribute used to indicate that no interception should be applied to
            the attribute target.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicy">
            <summary>
            A <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy"/> class that reads and constructs handlers
            based on <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerAttribute"/> on the target.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy">
            <summary>
            Base class for Policies that specifies which handlers apply to which methods of an object.
            </summary>
            <remarks>
            <para>This base class always enforces the 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ApplyNoPoliciesMatchingRule"/> before
            passing the checks onto derived classes. This way, derived classes do not need to
            worry about implementing this check.</para>
            <para>It also means that derived classes cannot override this rule. This is considered a feature.</para></remarks>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.#ctor">
            <summary>
            Creates a new empty Policy.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.#ctor(System.String)">
            <summary>
            Creates a new empty policy with the given name.
            </summary>
            <param name="name">Name of the policy.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.Matches(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Checks if the rules in this policy match the given member info.
            </summary>
            <param name="member">MemberInfo to check against.</param>
            <returns>true if ruleset matches, false if it does not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.GetHandlersFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Returns ordered collection of handlers in order that apply to the given member.
            </summary>
            <param name="member">Member that may or may not be assigned handlers by this policy.</param>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to use when creating handlers,
            if necessary.</param>
            <returns>Collection of handlers (possibly empty) that apply to this member.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.GetMethodSet(System.Reflection.MethodBase)">
            <summary>
            Given a method on an object, return the set of MethodBases for that method,
            plus any interface methods that the member implements.
            </summary>
            <param name="member">Member to get Method Set for.</param>
            <returns>The set of methods</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.DoesMatch(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Derived classes implement this method to calculate if the policy
            will provide any handler to the specified member.
            </summary>
            <param name="member">Member to check.</param>
            <returns>true if policy applies to this member, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.DoGetHandlersFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method to supply the list of handlers for
            this specific member.
            </summary>
            <param name="member">Member to get handlers for.</param>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to use when creating handlers,
            if necessary.</param>
            <returns>Enumerable collection of handlers for this method.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy.Name">
            <summary>
            Gets the name of this policy.
            </summary>
            <value>The name of the policy.</value>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicy.#ctor">
            <summary>
            Constructs a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicy"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicy.DoesMatch(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Derived classes implement this method to calculate if the policy
            will provide any handler to the specified member.
            </summary>
            <param name="member">Member to check.</param>
            <returns>true if policy applies to this member, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicy.DoGetHandlersFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method to supply the list of handlers for
            this specific member.
            </summary>
            <param name="member">Member to get handlers for.</param>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to use when creating handlers,
            if necessary.</param>
            <returns>Enumerable collection of handlers for this method.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerAttribute">
            <summary>
            Base class for handler attributes used in the attribute-driven
            interception policy.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerAttribute.CreateHandler(Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Derived classes implement this method. When called, it
            creates a new call handler as specified in the attribute
            configuration.
            </summary>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to use when creating handlers,
            if necessary.</param>
            <returns>A new call handler object.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.HandlerAttribute.Order">
            <summary>
            Gets or sets the order in which the handler will be executed.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline">
            <summary>
            The HandlerPipeline class encapsulates a list of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/>s
            and manages calling them in the proper order with the right inputs.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline"/> with an empty pipeline.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.ICallHandler})">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline"/> with the given collection
            of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/>s.
            </summary>
            <param name="handlers">Collection of handlers to add to the pipeline.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.InvokeHandlerDelegate)">
            <summary>
            Execute the pipeline with the given input.
            </summary>
            <param name="input">Input to the method call.</param>
            <param name="target">The ultimate target of the call.</param>
            <returns>Return value from the pipeline.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline.Count">
            <summary>
            Get the number of handlers in this pipeline.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler">
            <summary>
            Handlers implement this interface and are called for each
            invocation of the pipelines that they're included in.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextHandlerDelegate)">
            <summary>
            Implement this method to execute your handler processing.
            </summary>
            <param name="input">Inputs to the current call to the target.</param>
            <param name="getNext">Delegate to execute to get the next delegate in the handler
            chain.</param>
            <returns>Return value from the target.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler.Order">
            <summary>
            Order in which the handler will be executed
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InvokeHandlerDelegate">
            <summary>
            This delegate type is the type that points to the next
            method to execute in the current pipeline.
            </summary>
            <param name="input">Inputs to the current method call.</param>
            <param name="getNext">Delegate to get the next handler in the chain.</param>
            <returns>Return from the next method in the chain.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.GetNextHandlerDelegate">
            <summary>
            This delegate type is passed to each handler's Invoke method.
            Call the delegate to get the next delegate to call to continue
            the chain.
            </summary>
            <returns>Next delegate in the handler chain to call.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule">
            <summary>
            This interface is implemented by the matching rule classes.
            A Matching rule is used to see if a particular policy should
            be applied to a class member.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests to see if this rule applies to the given member.
            </summary>
            <param name="member">Member to test.</param>
            <returns>true if the rule applies, false if it doesn't.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation">
            <summary>
            This interface is used to represent the call to a method.
            An implementation of IMethodInvocation is passed to the
            call handlers so that they may manipulate the call
            (typically by changing the parameters) before the final target
            gets called.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.CreateMethodReturn(System.Object,System.Object[])">
            <summary>
            Factory method that creates the correct implementation of
            IMethodReturn.
            </summary>
            <param name="returnValue">Return value to be placed in the IMethodReturn object.</param>
            <param name="outputs">All arguments passed or returned as out/byref to the method. 
            Note that this is the entire argument list, including in parameters.</param>
            <returns>New IMethodReturn object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.CreateExceptionMethodReturn(System.Exception)">
            <summary>
            Factory method that creates the correct implementation of
            IMethodReturn in the presence of an exception.
            </summary>
            <param name="ex">Exception to be set into the returned object.</param>
            <returns>New IMethodReturn object</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.Inputs">
            <summary>
            Gets the inputs for this call.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.Arguments">
            <summary>
            Collection of all parameters to the call: in, out and byref.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.InvocationContext">
            <summary>
            Retrieves a dictionary that can be used to store arbitrary additional
            values. This allows the user to pass values between call handlers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.Target">
            <summary>
            The object that the call is made on.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation.MethodBase">
            <summary>
            The method on Target that we're aiming at.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn">
            <summary>
            This interface is used to represent the return value from a method.
            An implementation of IMethodReturn is returned by call handlers, and
            each handler can manipulate the parameters, return value, or add an
            exception on the way out.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn.Outputs">
            <summary>
            The collection of output parameters. If the method has no output
            parameters, this is a zero-length list (never null).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn.ReturnValue">
            <summary>
            Returns value from the method call.
            </summary>
            <remarks>This value is null if the method has no return value.</remarks>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn.Exception">
            <summary>
            If the method threw an exception, the exception object is here.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn.InvocationContext">
            <summary>
            Retrieves a dictionary that can be used to store arbitrary additional
            values. This allows the user to pass values between call handlers.
            </summary>
            <remarks>This is guaranteed to be the same dictionary that was used
            in the IMethodInvocation object, so handlers can set context
            properties in the pre-call phase and retrieve them in the after-call phase.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Interception">
            <summary>
            A Unity container extension that allows you to configure
            whether an object should be intercepted and which mechanism should
            be used to do it, and also provides a convenient set of methods for
            configuring injection for <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy"/> instances.
            </summary>
            <seealso cref="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetDefaultInterceptorFor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)"/>
            <seealso cref="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetDefaultInterceptorFor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)"/>
            <seealso cref="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor(System.Type,System.String,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)"/>
            <seealso cref="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor(System.Type,System.String,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)"/>
            <seealso cref="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.AddPolicy(System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.Initialize">
            <summary>
            Initial the container with this extension's functionality.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor(System.Type,System.String,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <param name="typeToIntercept">Type to intercept.</param>
            <param name="name">Name type is registered under.</param>
            <param name="interceptor">Interceptor to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <param name="typeToIntercept">Type to intercept.</param>
            <param name="interceptor">Interceptor to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor``1(System.String,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <typeparam name="T">Type to intercept</typeparam>
            <param name="name">Name type is registered under.</param>
            <param name="interceptor">Interceptor object to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor``1(Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <typeparam name="T">Type to intercept</typeparam>
            <param name="interceptor">Interceptor object to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor(System.Type,System.String,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <param name="typeToIntercept">Type to intercept.</param>
            <param name="name">Name type is registered under.</param>
            <param name="interceptor">Instance interceptor to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetDefaultInterceptorFor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            Set the interceptor for a type, regardless of what name is used to resolve the instances.
            </summary>
            <param name="typeToIntercept">Type to intercept</param>
            <param name="interceptor">Interceptor instance.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetDefaultInterceptorFor``1(Microsoft.Practices.Unity.InterceptionExtension.ITypeInterceptor)">
            <summary>
            Set the interceptor for a type, regardless of what name is used to resolve the instances.
            </summary>
            <typeparam name="TTypeToIntercept">Type to intercept</typeparam>
            <param name="interceptor">Interceptor instance.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <param name="typeToIntercept">Type to intercept.</param>
            <param name="interceptor">Instance interceptor to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor``1(System.String,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <typeparam name="T">Type to intercept.</typeparam>
            <param name="name">Name type is registered under.</param>
            <param name="interceptor">Instance interceptor to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetInterceptorFor``1(Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            API to configure interception for a type.
            </summary>
            <typeparam name="T">Type to intercept.</typeparam>
            <param name="interceptor">Instance interceptor to use.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetDefaultInterceptorFor(System.Type,Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            API to configure the default interception settings for a type.
            </summary>
            <param name="typeToIntercept">Type the interception is being configured for.</param>
            <param name="interceptor">The interceptor to use by default.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.SetDefaultInterceptorFor``1(Microsoft.Practices.Unity.InterceptionExtension.IInstanceInterceptor)">
            <summary>
            API to configure the default interception settings for a type.
            </summary>
            <typeparam name="TTypeToIntercept">Type the interception is being configured for.</typeparam>
            <param name="interceptor">The interceptor to use by default.</param>
            <returns>This extension object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Interception.AddPolicy(System.String)">
            <summary>
            Starts the definition of a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy"/>.
            </summary>
            <param name="policyName">The policy name.</param>
            <returns></returns>
            <remarks>This is a convenient way for defining a new policy and the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/>
            instances and <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> instances that are required by a policy.
            <para/>
            This mechanism is just a shortcut for what can be natively expressed by wiring up together objects
            with repeated calls to the <see cref="M:Microsoft.Practices.Unity.IUnityContainer.RegisterType(System.Type,System.Type,System.String,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])"/> method.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection">
            <summary>
            This interface represents a list of either input or output
            parameters. It implements a fixed size list, plus a couple
            of other utility methods.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection.ParameterName(System.Int32)">
            <summary>
            Gets the name of a parameter based on index.
            </summary>
            <param name="index">Index of parameter to get the name for.</param>
            <returns>Name of the requested parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection.GetParameterInfo(System.Int32)">
            <summary>
            Gets the ParameterInfo for a particular parameter by index.
            </summary>
            <param name="index">Index for this parameter.</param>
            <returns>ParameterInfo object describing the parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection.GetParameterInfo(System.String)">
            <summary>
            Gets the ParameterInfo for a particular parameter by name.
            </summary>
            <param name="parameterName">Name of the parameter.</param>
            <returns>ParameterInfo object for the named parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection.ContainsParameter(System.String)">
            <summary>
            Does this collection contain a parameter value with the given name?
            </summary>
            <param name="parameterName">Name of parameter to find.</param>
            <returns>True if the parameter name is in the collection, false if not.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection.Item(System.String)">
            <summary>
            Fetches a parameter's value by name.
            </summary>
            <param name="parameterName">parameter name.</param>
            <returns>value of the named parameter.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MatchingRuleSet">
            <summary>
            A <cref see="T:MatchingRuleSet"/> is a matching rule that
            is a collection of other matching rules. All the contained
            rules much match for the set to match.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MatchingRuleSet.Matches(System.Reflection.MethodBase)">
            <summary>
            Tests the given member against the ruleset. The member matches
            if all contained rules in the ruleset match against it.
            </summary>
            <remarks>If the ruleset is empty, then Matches passes since no rules failed.</remarks>
            <param name="member">MemberInfo to test.</param>
            <returns>true if all contained rules match, false if any fail.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ApplyNoPoliciesMatchingRule">
            <summary>
            A <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> implementation that fails to match
            if the method in question has the ApplyNoPolicies attribute on it.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ApplyNoPoliciesMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check if the <paramref name="member"/> matches this rule.
            </summary>
            <remarks>This rule returns true if the member does NOT have the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ApplyNoPoliciesAttribute"/>
            on it, or a containing type doesn't have the attribute.</remarks>
            <param name="member">Member to check.</param>
            <returns>True if the rule matches, false if it doesn't.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that matches the assembly name of the
            given member.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule"/> with the given
            assembly name (or partial name).
            </summary>
            <param name="assemblyName">Assembly name to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule.#ctor(System.Reflection.Assembly)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule"/> that matches
            against the given assembly.
            </summary>
            <param name="assembly">Assembly to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AssemblyMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Determines if the supplied <paramref name="member"/> matches the rule.
            </summary>
            <remarks>
            This rule matches if the assembly containing the given <paramref name="member"/>
            matches the name given. The rule used for matches lets you include the parts
            of the assembly name in order. You can specify assembly name only, assembly and version,
            assembly, version and culture, or the fully qualified assembly name.
            </remarks>
            <param name="member">Member to check.</param>
            <returns>true if <paramref name="member"/> is in a matching assembly, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicyMatchingRule">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that checks to see if the
            member (or type containing that member) have any <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerAttribute"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.AttributeDrivenPolicyMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Checks to see if <paramref name="member"/> matches the rule.
            </summary>
            <remarks>Returns true if any <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerAttribute"/>s are present on the method
            or the type containing that method.</remarks>
            <param name="member">Member to check.</param>
            <returns>true if member matches, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that checks to see if
            the member tested has an arbitrary attribute applied.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule.#ctor(System.Type,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule"/>.
            </summary>
            <param name="attributeType">Attribute to match.</param>
            <param name="inherited">If true, checks the base class for attributes as well.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.CustomAttributeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Checks to see if the given <paramref name="member"/> matches the rule.
            </summary>
            <param name="member">Member to check.</param>
            <returns>true if it matches, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo">
            <summary>
            Class used for storing information about a single name/ignoreCase
            pair. This class is also used as a base class for other classes that
            need this pair plus some other properties.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo.#ctor">
            <summary>
            Constructs an empty <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo"/> object with empty
            string and ignoreCase = false.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo.#ctor(System.String)">
            <summary>
            Constructs a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo"/> object that matches the given
            string. IgnoreCase is false.
            </summary>
            <param name="nameToMatch">The name to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo.#ctor(System.String,System.Boolean)">
            <summary>
            Constructs a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo"/> object that matches the
            given string, setting the ignoreCase flag to the given value.
            </summary>
            <param name="nameToMatch">The name to match.</param>
            <param name="ignoreCase">true to do case insensitive comparison, false to do case sensitive.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo.Match">
            <summary>
            Gets or sets the name to match.
            </summary>
            <value>The name to match.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo.IgnoreCase">
            <summary>
            Gets or sets whether to do case sensitive comparisons of Match.
            </summary>
            <value>If false, case sensitive comparison. If true, case insensitive comparisons.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule">
            <summary>
            A matching rule that matches when the given member name is
            the same as the one supplied in the constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule.#ctor(System.String)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/> that matches the
            given member name. Wildcards are allowed.
            </summary>
            <param name="nameToMatch">Name to match against. Comparison is case sensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule.#ctor(System.String,System.Boolean)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/> that matches the
            given member name. Wildcards are allowed.
            </summary>
            <param name="nameToMatch">Name to match against.</param>
            <param name="ignoreCase">If false, name comparisons are case sensitive. If true, name comparisons are case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/> that matches the
            given member names. Wildcards are allowed.
            </summary>
            <param name="namesToMatch">collections of names to match. If any of these patterns match, the rule matches. Comparisons are case sensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule.#ctor(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/> that matches the
            given member names. Wildcards are allowed.
            </summary>
            <param name="namesToMatch">Collections of names to match. If any of these patterns match, the rule matches. </param>
            <param name="ignoreCase">If false, name comparisons are case sensitive. If true, name comparisons are case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo})">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule"/> that matches
            one of the given member names. Wildcards are allowed.
            </summary>
            <param name="matches">List of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo"/> objects containing
            the pattern to match and case sensitivity flag.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MemberNameMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check if the given <paramref name="member"/> matches one of this
            object's matching patterns.
            </summary>
            <param name="member">Member to check.</param>
            <returns>True if matches, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule">
            <summary>
            Match methods with the given names and method signature.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/> that matches methods
            with the given name, with parameter types matching the given list.
            </summary>
            <param name="methodName">Method name to match. Wildcards are allowed.</param>
            <param name="parameterTypeNames">Parameter type names to match, in order. Wildcards are allowed.</param>
            <param name="ignoreCase">If false, name comparisons are case sensitive. If true, name comparisons are case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/> that matches methods
            with the given name, with parameter types matching the given list.
            </summary>
            <remarks>Name comparisons are case sensitive.</remarks>
            <param name="methodName">Method name to match. Wildcards are allowed.</param>
            <param name="parameterTypeNames">Parameter type names to match, in order. Wildcards are allowed.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/> that matches any method
            with parameter types matching the given list.
            </summary>
            <remarks>Name comparisons are case sensitive.</remarks>
            <param name="parameterTypeNames">Parameter type names to match, in order. Wildcards are allowed.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule.#ctor(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule"/> that matches any method
            with parameter types matching the given list.
            </summary>
            <param name="parameterTypeNames">Parameter type names to match, in order. Wildcards are allowed.</param>
            <param name="ignoreCase">If false, name comparisons are case sensitive. If true, name comparisons are case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodSignatureMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check to see if the given method matches the name and signature.
            </summary>
            <param name="member">Member to check.</param>
            <returns>True if match, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that matches members in a given namespace. You can
            specify either a single namespace (e.g. <c>System.Data</c>) or a namespace root
            (e.g. <c>System.Data.*</c> to match types in that namespace or below.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.#ctor(System.String)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule"/> that matches the given
            namespace.
            </summary>
            <param name="namespaceName">namespace name to match. Comparison is case sensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.#ctor(System.String,System.Boolean)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule"/> that matches the given
            namespace.
            </summary>
            <param name="namespaceName">namespace name to match.</param>
            <param name="ignoreCase">If false, comparison is case sensitive. If true, comparison is case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo})">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule"/> that matches any of
            the given namespace names.
            </summary>
            <param name="matches">Collection of namespace names to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check to see if the given <paramref name="member"/> is in a namespace
            matched by any of our given namespace names.
            </summary>
            <param name="member">member to check.</param>
            <returns>True if member is contained in a matching namespace, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.NamespaceMatchingInfo">
            <summary>
            A helper class that encapsulates the name to match, case sensitivity flag,
            and the wildcard rules for matching namespaces.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.NamespaceMatchingInfo.#ctor(System.String,System.Boolean)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.NamespaceMatchingInfo"/> that matches the
            given namespace name.
            </summary>
            <param name="match">Namespace name to match.</param>
            <param name="ignoreCase">If false, comparison is case sensitive. If true, comparison is case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.NamespaceMatchingRule.NamespaceMatchingInfo.Matches(System.Type)">
            <summary>
            Check if the given type <paramref name="t"/> is in a matching namespace.
            </summary>
            <param name="t">Type to check.</param>
            <returns>True if type is in a matching namespace, false if not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that matches methods that have any parameters
            of the given types.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo})">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule"/> that matches if any of
            the method parameters match ones in the given collection.
            </summary>
            <param name="matches">Collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo"/> that
            describes the types to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check the given member to see if it has any matching parameters.
            </summary>
            <param name="member">Member to match.</param>
            <returns>true if member matches, false if it doesn't.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingRule.ParameterMatches">
            <summary>
            The list of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo"/> describing the parameter types to match.
            </summary>
            <value>The collection of matches.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind">
            <summary>
            Describes the type of parameter to match.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind.Input">
            <summary>
            Input parameter
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind.Output">
            <summary>
            Output parameter
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind.InputOrOutput">
            <summary>
            Input or output parameter
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind.ReturnValue">
            <summary>
            Method return value
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo">
            <summary>
            A class that stores information about a single type to match.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo.#ctor">
            <summary>
            Creates a new uninitialized <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo.#ctor(Microsoft.Practices.Unity.InterceptionExtension.ParameterKind)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo"/> matching the given kind of parameter.
            </summary>
            <param name="kind"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind"/> of parameter to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.ParameterKind)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo"/> matching the given parameter
            type and kind.
            </summary>
            <param name="nameToMatch">Parameter <see cref="T:System.Type"/> name to match.</param>
            <param name="kind"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind"/> of parameter to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo.#ctor(System.String,System.Boolean,Microsoft.Practices.Unity.InterceptionExtension.ParameterKind)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo"/> matching the given parameter
            type and kind.
            </summary>
            <param name="nameToMatch">Parameter <see cref="T:System.Type"/> name to match.</param>
            <param name="ignoreCase">If false, compare type names using case-sensitive comparison.
            If true, compare type names using case-insensitive comparison.</param>
            <param name="kind"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind"/> of parameter to match.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterTypeMatchingInfo.Kind">
            <summary>
            What kind of parameter to match.
            </summary>
            <value><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterKind"/> indicating which kind of parameters to match.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> implementation that matches properties
            by name. You can match the getter, setter, or both.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule.#ctor(System.String)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/> that matches the
            getter or setter of the given property.
            </summary>
            <param name="propertyName">Name of the property. Name comparison is case sensitive. Wildcards are allowed.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/> that matches the
            given method of the given property.
            </summary>
            <param name="propertyName">Name of the property. Name comparison is case sensitive. Wildcards are allowed.</param>
            <param name="option">Match the getter, setter, or both.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/> that matches the
            given method of the given property.
            </summary>
            <param name="propertyName">Name of the property to match. Wildcards are allowed.</param>
            <param name="option">Match the getter, setter, or both.</param>
            <param name="ignoreCase">If false, name comparison is case sensitive. If true, name comparison is case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/> that matches any of the
            given properties.
            </summary>
            <param name="matches">Collection of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo"/> defining which
            properties to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Checks if the given member matches the rule.
            </summary>
            <param name="member">Member to check.</param>
            <returns>True if it matches, false if it does not.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption">
            <summary>
            Specifies which methods of a property should be matches by
            the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingRule"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption.Get">
            <summary>
            Match the property getter method.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption.Set">
            <summary>
            Match the property setter method.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption.GetOrSet">
            <summary>
            Match either the getter or setter method.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo">
            <summary>
            Information about a property match.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo.#ctor(System.String)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo"/> that matches the get or set methods
            of the given property name, and does a case-sensitive comparison.
            </summary>
            <param name="match">Property name to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo"/> that matches the given methods of
            the given property name, doing a case-sensitive comparison.
            </summary>
            <param name="match">Property name to match.</param>
            <param name="option"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption"/> specifying which methods of the property to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption,System.Boolean)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo"/> that matches the given methods of
            the given property name.
            </summary>
            <param name="match">Property name to match.</param>
            <param name="option"><see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption"/> specifying which methods of the property to match.</param>
            <param name="ignoreCase">If false, name comparison is case sensitive. If true, name comparison is case insensitive.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingInfo.Option">
            <summary>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PropertyMatchingOption"/> to use when doing name comparisons on this property.
            </summary>
            <value>Specifies which methods of the property to match.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that checks to see if a member has a specified
            type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule.#ctor(System.Type)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule"/> that matches
            members with the given return type.
            </summary>
            <param name="returnType">Type to look for.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule.#ctor(System.String)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule"/> that matches
            the given return type by name.
            </summary>
            <remarks>See the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> class for details on how
            type name matches are done.</remarks>
            <param name="returnTypeName">Type name to match. Name comparisons are case sensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule.#ctor(System.String,System.Boolean)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule"/> that matches
            the given return type by name.
            </summary>
            <remarks>See the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> class for details on how
            type name matches are done.</remarks>
            <param name="returnTypeName">Type name to match.</param>
            <param name="ignoreCase">If false, name comparison is case sensitive. If true, comparison
            is case insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReturnTypeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check to see if the given member has a matching return type.
            </summary>
            <param name="member">Member to check.</param>
            <returns>true if return types match, false if they don't.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule">
            <summary>
            A <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> that checks a member for the presence
            of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttribute"/> on the method, property, or class, and
            that the given string matches.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule"/>, looking for
            the given string. The comparison is case sensitive.
            </summary>
            <param name="tagToMatch">tag string to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule.#ctor(System.String,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule"/>, looking for
            the given string. The comparison is case sensitive if <paramref name="ignoreCase"/> is
            false, case insensitive if <paramref name="ignoreCase"/> is true.
            </summary>
            <param name="tagToMatch">tag string to match.</param>
            <param name="ignoreCase">if false, case-sensitive comparison. If true, case-insensitive comparison.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Check the given member for the presence of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttribute"/> and
            match the strings.
            </summary>
            <param name="member">Member to check.</param>
            <returns>True if tag strings match, false if they don't.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule">
            <summary>
            A matching rule that matches when the member is declared
            in the given type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule.#ctor(System.Type)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> that matches the
            given type.
            </summary>
            <param name="type">The type to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> that matches types
            with the given name.
            </summary>
            <remarks>Comparisons are case sensitive.</remarks>
            <param name="typeName">Type name to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule.#ctor(System.String,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> that matches types
            with the given name, using the given case sensitivity.
            </summary>
            <param name="typeName">Type name to match.</param>
            <param name="ignoreCase">if false, do case-sensitive comparison. If true, do case-insensitive.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.MatchingInfo})">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule"/> that will match
            any of the type names given in the collection of match information.
            </summary>
            <param name="matches">The match information to match.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule.Matches(System.Reflection.MethodBase)">
            <summary>
            Checks if the given member matches any of this object's matches.
            </summary>
            <param name="member">Member to match.</param>
            <returns>True if match, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TypeMatchingRule.Matches(System.Type)">
            <summary>
            Checks if the given type matches any of this object's matches.
            </summary>
            <remarks>Matches may be on the namespace-qualified type name or just the type name.</remarks>
            <param name="t">Type to check.</param>
            <returns>True if it matches, false if it doesn't.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection"/> that wraps a provided array
            containing the argument values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.#ctor(System.Object[],System.Reflection.ParameterInfo[],System.Predicate{System.Reflection.ParameterInfo})">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection"/> that wraps the
            given array of arguments.
            </summary>
            <param name="arguments">Complete collection of arguments.</param>
            <param name="argumentInfo">Type information about each parameter.</param>
            <param name="isArgumentPartOfCollection">A <see cref="T:System.Predicate`1"/> that indicates
            whether a particular parameter is part of the collection. Used to filter out only input
            parameters, for example.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.GetParameterInfo(System.Int32)">
            <summary>
            Gets the ParameterInfo for a particular parameter by index.
            </summary>
            <param name="index">Index for this parameter.</param>
            <returns>ParameterInfo object describing the parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.GetParameterInfo(System.String)">
            <summary>
            Gets the <see cref="T:System.Reflection.ParameterInfo"/> for the given named parameter.
            </summary>
            <param name="parameterName">Name of parameter.</param>
            <returns><see cref="T:System.Reflection.ParameterInfo"/> for the requested parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ParameterName(System.Int32)">
            <summary>
            Gets the name of a parameter based on index.
            </summary>
            <param name="index">Index of parameter to get the name for.</param>
            <returns>Name of the requested parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ContainsParameter(System.String)">
            <summary>
            Does this collection contain a parameter value with the given name?
            </summary>
            <param name="parameterName">Name of parameter to find.</param>
            <returns>True if the parameter name is in the collection, false if not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Add(System.Object)">
            <summary>
            Adds to the collection. This is a read only collection, so this method
            always throws <see cref="T:System.NotSupportedException"/>.
            </summary>
            <param name="value">Object to add.</param>
            <returns>Nothing, always throws.</returns>
            <exception cref="T:System.NotSupportedException">Always throws this.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Contains(System.Object)">
            <summary>
            Checks to see if the collection contains the given object.
            </summary>
            <remarks>Tests for the object using object.Equals.</remarks>
            <param name="value">Object to find.</param>
            <returns>true if object is in collection, false if it is not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Clear">
            <summary>
            Remove all items in the collection. This collection is fixed-size, so this
            method always throws <see cref="T:System.NotSupportedException"/>.
            </summary>
            <exception cref="T:System.NotSupportedException">This is always thrown.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.IndexOf(System.Object)">
            <summary>
            Returns the index of the given object, or -1 if not found.
            </summary>
            <param name="value">Object to find.</param>
            <returns>zero-based index of found object, or -1 if not found.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Insert(System.Int32,System.Object)">
            <summary>
            Inserts a new item. This is a fixed-size collection, so this method throws <see cref="T:System.NotSupportedException"/>.
            </summary>
            <param name="index">Index to insert at.</param>
            <param name="value">Always throws.</param>
            <exception cref="T:System.NotSupportedException">Always throws this.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Remove(System.Object)">
            <summary>
            Removes the given item. This is a fixed-size collection, so this method throws <see cref="T:System.NotSupportedException"/>.
            </summary>
            <param name="value">Always throws.</param>
            <exception cref="T:System.NotSupportedException">Always throws this.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the given item. This is a fixed-size collection, so this method throws <see cref="T:System.NotSupportedException"/>.
            </summary>
            <param name="index">Always throws.</param>
            <exception cref="T:System.NotSupportedException">Always throws this.</exception>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the contents of this collection to the given array.
            </summary>
            <param name="array">Destination array.</param>
            <param name="index">index to start copying from.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.GetEnumerator">
            <summary>
            Gets an enumerator object to support the foreach construct.
            </summary>
            <returns>Enumerator object.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Item(System.String)">
            <summary>
            Fetches a parameter's value by name.
            </summary>
            <param name="parameterName">parameter name.</param>
            <value>value of the named parameter.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Item(System.Int32)">
            <summary>
            Gets the value of a parameter based on index.
            </summary>
            <param name="index">Index of parameter to get the value for.</param>
            <value>Value of the requested parameter.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.IsReadOnly">
            <summary>
            Is this collection read only?
            </summary>
            <value>No, it is not read only, the contents can change.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.IsFixedSize">
            <summary>
            Is this collection fixed size?
            </summary>
            <value>Yes, it is.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.Count">
            <summary>
            Total number of items in the collection.
            </summary>
            <value>The count.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.SyncRoot">
            <summary>
            Gets a synchronized version of this collection. WARNING: Not implemented completely,
            DO NOT USE THIS METHOD.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.IsSynchronized">
            <summary>
            Is the object synchronized for thread safety?
            </summary>
            <value>No, it isn't.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ArgumentInfo">
            <summary>
            An internal struct that maps the index in the arguments collection to the
            corresponding <see cref="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ArgumentInfo.ParameterInfo"/> about that argument.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ArgumentInfo.#ctor(System.Int32,System.Reflection.ParameterInfo)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ArgumentInfo"/> object linking the
            given index and <see cref="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ArgumentInfo.ParameterInfo"/> object.
            </summary>
            <param name="index">Index into arguments array (zero-based).</param>
            <param name="parameterInfo"><see cref="F:Microsoft.Practices.Unity.InterceptionExtension.ParameterCollection.ArgumentInfo.ParameterInfo"/> for the argument at <paramref name="index"/>.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition">
            <summary>
            Transient class that supports convenience method for specifying interception policies.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule(System.String)">
            <summary>
            Adds a reference to matching rule by name.
            </summary>
            <param name="name">The name for the matching rule.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
            <remarks>
            The details of how the rule should be created by the container must be specified using a 
            standard injection specification mechanism.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule(Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule)">
            <summary>
            Makes <paramref name="instance"/> a matching rule in the current policy.
            </summary>
            <param name="instance">The new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> for the policy.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule(System.Type,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> and makes it available
            as a matching rule in the current policy.
            </summary>
            <param name="type">The type for the new matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule(System.Type,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> and makes it available
            as a matching rule in the current policy, using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <param name="type">The type for the new matching rule.</param>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule(System.Type,System.String,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> using the specified name
            and makes it available as a matching rule in the current policy.
            </summary>
            <param name="type">The type for the new matching rule.</param>
            <param name="name">The name for the injection configuration for the matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule(System.Type,System.String,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> and makes it available
            as a matching rule in the current policy, using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <param name="type">The type for the new matching rule.</param>
            <param name="name">The name for the injection configuration for the matching rule.</param>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule``1(Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> and makes it available
            as a matching rule in the current policy.
            </summary>
            <typeparam name="TMatchingRule">The type for the new matching rule.</typeparam>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule``1(Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> and makes it available
            as a matching rule in the current policy, using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <typeparam name="TMatchingRule">The type for the new matching rule.</typeparam>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule``1(System.String,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> using the specified name
            and makes it available as a matching rule in the current policy.
            </summary>
            <typeparam name="TMatchingRule">The type for the new matching rule.</typeparam>
            <param name="name">The name for the injection configuration for the matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddMatchingRule``1(System.String,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule"/> using the specified name
            and makes it available as a matching rule in the current policy, 
            using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <typeparam name="TMatchingRule">The type for the new matching rule.</typeparam>
            <param name="name">The name for the injection configuration for the matching rule.</param>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured matching rule.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler(System.String)">
            <summary>
            Adds a reference to call handler by name.
            </summary>
            <param name="name">The name for the call handler.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
            <remarks>
            The details of how the handler should be created by the container must be specified using a 
            standard injection specification mechanism.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler(Microsoft.Practices.Unity.InterceptionExtension.ICallHandler)">
            <summary>
            Makes <paramref name="instance"/> a call handler in the current policy.
            </summary>
            <param name="instance">The new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> for the policy.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler(System.Type,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> and makes it available
            as a call handler in the current policy.
            </summary>
            <param name="type">The type for the new call handler.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler(System.Type,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> and makes it available
            as a call handler in the current policy, using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <param name="type">The type for the new call handler.</param>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured call handler.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler(System.Type,System.String,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> using the specified name
            and makes it available as a call handler in the current policy.
            </summary>
            <param name="type">The type for the new call handler.</param>
            <param name="name">The name for the injection configuration for the call handler.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler(System.Type,System.String,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> using the specified name
            and makes it available as a call handler in the current policy, 
            using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <param name="type">The type for the new call handler.</param>
            <param name="name">The name for the injection configuration for the call handler.</param>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured call handler.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler``1(Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> and makes it available
            as a call handler in the current policy.
            </summary>
            <typeparam name="TCallHandler">The type for the new call handler.</typeparam>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler``1(Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> and makes it available
            as a call handler in the current policy, using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <typeparam name="TCallHandler">The type for the new call handler.</typeparam>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured call handler.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler``1(System.String,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> using the specified name
            and makes it available as a call handler in the current policy.
            </summary>
            <typeparam name="TCallHandler">The type for the new call handler.</typeparam>
            <param name="name">The name for the injection configuration for the call handler .</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.AddCallHandler``1(System.String,Microsoft.Practices.Unity.LifetimeManager,Microsoft.Practices.Unity.InjectionMember[])">
            <summary>
            Configures injection for a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/> using the specified name
            and makes it available as a call handler in the current policy, 
            using the given <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/>.
            </summary>
            <typeparam name="TCallHandler">The type for the new call handler.</typeparam>
            <param name="name">The name for the injection configuration for the call handler .</param>
            <param name="lifetimeManager">The <see cref="T:Microsoft.Practices.Unity.LifetimeManager"/> that controls the lifetime
            of the configured call handler.</param>
            <param name="injectionMembers">Objects containing the details on which members to inject and how.</param>
            <returns>
            The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition"/> than allows further configuration of the policy.
            </returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.Container">
            <summary>
            The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> that is currently being
            configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.Interception">
            <summary>
            The <see cref="P:Microsoft.Practices.Unity.InterceptionExtension.PolicyDefinition.Interception"/> extension to which the policy was added.
            </summary>
            <remarks>
            Use this property to start adding a new policy.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PolicySet">
            <summary>
            A collection of Policy objects. The policies within a PolicySet combine using
            an "or" operation.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicySet.#ctor(Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy[])">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicySet"/> containing the given policies.
            </summary>
            <param name="policies">Policies to put into the policy set.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicySet.GetPoliciesFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Gets the policies that apply to the given member.
            </summary>
            <param name="member">Member to get policies for.</param>
            <returns>Collection of policies that apply to this member.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicySet.GetPoliciesNotFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Gets the policies in the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicySet"/> that do not
            apply to the given member.
            </summary>
            <param name="member">Member to check.</param>
            <returns>Collection of policies that do not apply to <paramref name="member"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicySet.GetHandlersFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Gets the handlers that apply to the given member based on all policies in the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicySet"/>.
            </summary>
            <param name="member">Member to get handlers for.</param>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to use when creating handlers,
            if necessary.</param>
            <returns>Collection of call handlers for <paramref name="member"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior">
            <summary>
            Interceptor that performs policy injection.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior">
            <summary>
            Interception behaviors implement this interface and are called for each
            invocation of the pipelines that they're included in.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextInterceptionBehaviorDelegate)">
            <summary>
            Implement this method to execute your behavior processing.
            </summary>
            <param name="input">Inputs to the current call to the target.</param>
            <param name="getNext">Delegate to execute to get the next delegate in the behavior chain.</param>
            <returns>Return value from the target.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior.GetRequiredInterfaces">
            <summary>
            Returns the interfaces required by the behavior for the objects it intercepts.
            </summary>
            <returns>The required interfaces.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior.WillExecute">
            <summary>
            Returns a flag indicating if this behavior will actually do anything when invoked.
            </summary>
            <remarks>This is used to optimize interception. If the behaviors won't actually
            do anything (for example, PIAB where no policies match) then the interception
            mechanism can be skipped completely.</remarks>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior.#ctor(Microsoft.Practices.Unity.InterceptionExtension.PipelineManager)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior"/> with a pipeline manager.
            </summary>
            <param name="pipelineManager">The <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PipelineManager"/> for the new instance.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior.#ctor(Microsoft.Practices.Unity.InterceptionExtension.CurrentInterceptionRequest,Microsoft.Practices.Unity.InterceptionExtension.InjectionPolicy[],Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior"/> with the given information
            about what's being intercepted and the current set of injection policies.
            </summary>
            <param name="interceptionRequest">Information about what will be injected.</param>
            <param name="policies">Current injection policies.</param>
            <param name="container">Unity container that can be used to resolve call handlers.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextInterceptionBehaviorDelegate)">
            <summary>
            Applies the policy injection handlers configured for the invoked method.
            </summary>
            <param name="input">Inputs to the current call to the target.</param>
            <param name="getNext">Delegate to execute to get the next delegate in the handler
            chain.</param>
            <returns>Return value from the target.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior.GetRequiredInterfaces">
            <summary>
            Returns the interfaces required by the behavior for the objects it intercepts.
            </summary>
            <returns>An empty array of interfaces.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.PolicyInjectionBehavior.WillExecute">
            <summary>
            Returns a flag indicating if this behavior will actually do anything when invoked.
            </summary>
            <remarks>This is used to optimize interception. If the behaviors won't actually
            do anything (for example, PIAB where no policies match) then the interception
            mechanism can be skipped completely.</remarks>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionAbstractMethodNotImplemented">
            <summary>
              Looks up a localized string similar to Intercepted abstract method was invoked..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionAdditionalInterfaceNotImplemented">
            <summary>
              Looks up a localized string similar to Additional interfaces do not have an implementation..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionAdditionalInterfacesInvalid">
            <summary>
              Looks up a localized string similar to The additional interfaces supplied are invalid: {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionAttributeNoSubclassOfAttribute">
            <summary>
              Looks up a localized string similar to Type must be a subclass of System.Attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionCannotCreateInstance">
            <summary>
              Looks up a localized string similar to Could not create instance of type {0} with no constructor arguments..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionCannotMapGenericTypeDefinition">
            <summary>
              Looks up a localized string similar to Cannot map generic type parameters on a generic type definition (an unbound generic type)..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionContainsNullElement">
            <summary>
              Looks up a localized string similar to Collection contains a null element..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionMappedParametersDoNotMatch">
            <summary>
              Looks up a localized string similar to The lengths of the mapped generic parameters do not match..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionNullInterfacesCollection">
            <summary>
              Looks up a localized string similar to The collection of interfaces is null..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionRequiredInterfacesInvalid">
            <summary>
              Looks up a localized string similar to The required interfaces for behavior {1} are invalid: {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionTypeIsNotInterface">
            <summary>
              Looks up a localized string similar to The type {0} is not an interface..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionTypeIsNull">
            <summary>
              Looks up a localized string similar to Null type..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.ExceptionTypeIsOpenGeneric">
            <summary>
              Looks up a localized string similar to The type {0} is an open generic..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.InterceptionNotSupported">
            <summary>
              Looks up a localized string similar to The type {0} is not interceptable..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.InterfaceMethodNotImplemented">
            <summary>
              Looks up a localized string similar to Could not find the implementation of interface method {0}.{1} in type {2}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.Properties.Resources.NullBehavior">
            <summary>
              Looks up a localized string similar to Null is not permitted as an interception behavior..
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInputParameterCollection">
            <summary>
            A class that wraps the inputs of a <see cref="T:System.Runtime.Remoting.Messaging.IMethodCallMessage"/> into the
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection"/> interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInputParameterCollection.#ctor(System.Runtime.Remoting.Messaging.IMethodCallMessage,System.Object[])">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyInputParameterCollection"/> that wraps the
            given method call and arguments.
            </summary>
            <param name="callMessage">The call message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> that wraps the
            remoting based <see cref="T:System.Runtime.Remoting.Messaging.IMethodCallMessage"/> in the PIAB call
            interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.#ctor(System.Runtime.Remoting.Messaging.IMethodCallMessage,System.Object)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> implementation that wraps
            the given <paramref name="callMessage"/>, with the given ultimate
            target object.
            </summary>
            <param name="callMessage">Remoting call message object.</param>
            <param name="target">Ultimate target of the method call.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.CreateMethodReturn(System.Object,System.Object[])">
            <summary>
            Factory method that creates the correct implementation of
            IMethodReturn.
            </summary>
            <remarks>In this implementation we create an instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn"/>.</remarks>
            <param name="returnValue">Return value to be placed in the IMethodReturn object.</param>
            <param name="outputs">All arguments passed or returned as out/byref to the method. 
            Note that this is the entire argument list, including in parameters.</param>
            <returns>New IMethodReturn object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.CreateExceptionMethodReturn(System.Exception)">
            <summary>
            Factory method that creates the correct implementation of
            IMethodReturn in the presence of an exception.
            </summary>
            <param name="ex">Exception to be set into the returned object.</param>
            <returns>New IMethodReturn object</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.Inputs">
            <summary>
            Gets the inputs for this call.
            </summary>
            <value>The input collection.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.Microsoft#Practices#Unity#InterceptionExtension#IMethodInvocation#Arguments">
            <summary>
            Collection of all parameters to the call: in, out and byref.
            </summary>
            <value>The arguments collection.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.InvocationContext">
            <summary>
            Retrieves a dictionary that can be used to store arbitrary additional
            values. This allows the user to pass values between call handlers.
            </summary>
            <value>The invocation context dictionary.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.Target">
            <summary>
            The object that the call is made on.
            </summary>
            <value>The target object.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.MethodBase">
            <summary>
            The method on Target that we're aiming at.
            </summary>
            <value>The target method base.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodInvocation.Arguments">
            <summary>
            Gets the collection of arguments being passed to the target.
            </summary>
            <remarks>This method exists because the underlying remoting call message
            does not let handlers change the arguments.</remarks>
            <value>Array containing the arguments to the target.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn"/> that wraps the
            remoting call and return messages.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.#ctor(System.Runtime.Remoting.Messaging.IMethodCallMessage,System.Object,System.Object[],System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn"/> object that contains a
            return value.
            </summary>
            <param name="callMessage">The original call message that invoked the method.</param>
            <param name="returnValue">Return value from the method.</param>
            <param name="arguments">Collections of arguments passed to the method (including the new
            values of any out params).</param>
            <param name="invocationContext">Invocation context dictionary passed into the call.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.#ctor(System.Exception,System.Runtime.Remoting.Messaging.IMethodCallMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn"/> object that contains an
            exception thrown by the target.
            </summary>
            <param name="ex">Exception that was thrown.</param>
            <param name="callMessage">The original call message that invoked the method.</param>
            <param name="invocationContext">Invocation context dictionary passed into the call.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.ToMethodReturnMessage">
            <summary>
            Constructs a <see cref="T:System.Runtime.Remoting.Messaging.IMethodReturnMessage"/> for the remoting
            infrastructure based on the contents of this object.
            </summary>
            <returns>The <see cref="T:System.Runtime.Remoting.Messaging.IMethodReturnMessage"/> instance.</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.Outputs">
            <summary>
            The collection of output parameters. If the method has no output
            parameters, this is a zero-length list (never null).
            </summary>
            <value>The output parameter collection.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.ReturnValue">
            <summary>
            Return value from the method call.
            </summary>
            <remarks>This value is null if the method has no return value.</remarks>
            <value>The return value.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.Exception">
            <summary>
            If the method threw an exception, the exception object is here.
            </summary>
            <value>The exception, or null if no exception was thrown.</value>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyMethodReturn.InvocationContext">
            <summary>
            Retrieves a dictionary that can be used to store arbitrary additional
            values. This allows the user to pass values between call handlers.
            </summary>
            <remarks>This is guaranteed to be the same dictionary that was used
            in the IMethodInvocation object, so handlers can set context
            properties in the pre-call phase and retrieve them in the after-call phase.
            </remarks>
            <value>The invocation context dictionary.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyOutputParameterCollection">
            <summary>
            A class that wraps the outputs of a <see cref="T:System.Runtime.Remoting.Messaging.IMethodCallMessage"/> into the
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IParameterCollection"/> interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyOutputParameterCollection.#ctor(System.Runtime.Remoting.Messaging.IMethodCallMessage,System.Object[])">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TransparentProxyOutputParameterCollection"/> that wraps the
            given method call and arguments.
            </summary>
            <param name="callMessage">The call message.</param>
            <param name="arguments">The arguments.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy">
            <summary>
            A policy is a combination of a matching rule set and a set of handlers.
            If the policy applies to a member, then the handlers will be enabled for
            that member.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule[],System.String[])">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy"/> object with a set of matching rules
            and the names to use when resolving handlers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy.#ctor(System.String,Microsoft.Practices.Unity.InterceptionExtension.IMatchingRule[],System.String[])">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy"/> object with a name, a set of matching rules
            and the names to use when resolving handlers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy.DoesMatch(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo)">
            <summary>
            Checks if the rules in this policy match the given member info.
            </summary>
            <param name="member">MemberInfo to check against.</param>
            <returns>true if ruleset matches, false if it does not.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.RuleDrivenPolicy.DoGetHandlersFor(Microsoft.Practices.Unity.InterceptionExtension.MethodImplementationInfo,Microsoft.Practices.Unity.IUnityContainer)">
            <summary>
            Return ordered collection of handlers in order that apply to the given member.
            </summary>
            <param name="member">Member that may or may not be assigned handlers by this policy.</param>
            <param name="container">The <see cref="T:Microsoft.Practices.Unity.IUnityContainer"/> to use when creating handlers,
            if necessary.</param>
            <returns>Collection of handlers (possibly empty) that apply to this member.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttribute">
            <summary>
            A simple attribute used to "tag" classes, methods, or properties with a
            string that can later be matched via the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttributeMatchingRule"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.TagAttribute.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.TagAttribute"/> with the given string.
            </summary>
            <param name="tag">The tag string.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.TagAttribute.Tag">
            <summary>
            The string tag for this attribute.
            </summary>
            <value>the tag.</value>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior">
            <summary>
            Stores information about a single <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> to be used on an intercepted object and
            configures a container accordingly.
            </summary>
            <seealso cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>.
            </summary>
            <param name="interceptionBehavior">The interception behavior to use.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior.#ctor(System.Type,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            given type/name pair.
            </summary>
            <param name="behaviorType">Type of behavior to </param>
            <param name="name"></param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            given behavior type.
            </summary>
            <param name="behaviorType">Type of behavior to </param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior.GetBehaviorsPolicy(Microsoft.Practices.ObjectBuilder2.IPolicyList,System.Type,System.String)">
            <summary>
            Get the list of behaviors for the current type so that it can be added to.
            </summary>
            <param name="policies">Policy list.</param>
            <param name="implementationType">Implementation type to set behaviors for.</param>
            <param name="name">Name type is registered under.</param>
            <returns>An instance of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorsPolicy"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior`1">
            <summary>
            A generic version of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> that lets you
            specify behavior types using generic syntax.
            </summary>
            <typeparam name="TBehavior">Type of behavior to register.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            given behavior type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior`1.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehavior"/> with a 
            given type/name pair.
            </summary>
            <param name="name">Name to use to resolve the behavior.</param>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorsPolicy">
            <summary>
            An <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehaviorsPolicy"/> that accumulates a sequence of 
            <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> instances for an intercepted object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorsPolicy.GetEffectiveBehaviors(Microsoft.Practices.ObjectBuilder2.IBuilderContext,Microsoft.Practices.Unity.InterceptionExtension.IInterceptor,System.Type,System.Type)">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> object to be used for the given type and
            interceptor.
            </summary>
            <remarks>
            This method will return a sequence of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>s. These behaviors will
            only be included if their <see cref="P:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior.WillExecute"/> properties are true.
            </remarks>
            <param name="context">Context for the current build operation.</param>
            <param name="interceptor">Interceptor that will be used to invoke the behavior.</param>
            <param name="typeToIntercept">Type that interception was requested on.</param>
            <param name="implementationType">Type that implements the interception.</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorsPolicy.BehaviorKeys">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.ObjectBuilder2.NamedTypeBuildKey"/> that can be used to resolve the
            behaviors.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorPipeline">
            <summary>
            The InterceptionBehaviorPipeline class encapsulates a list of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/>s
            and manages calling them in the proper order with the right inputs.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorPipeline.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline"/> with an empty pipeline.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorPipeline.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior})">
            <summary>
            Creates a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.HandlerPipeline"/> with the given collection
            of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ICallHandler"/>s.
            </summary>
            <param name="interceptionBehaviors">Collection of interception behaviors to add to the pipeline.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorPipeline.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.InvokeInterceptionBehaviorDelegate)">
            <summary>
            Execute the pipeline with the given input.
            </summary>
            <param name="input">Input to the method call.</param>
            <param name="target">The ultimate target of the call.</param>
            <returns>Return value from the pipeline.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorPipeline.Add(Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior)">
            <summary>
            Adds a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IInterceptionBehavior"/> to the pipeline.
            </summary>
            <param name="interceptionBehavior">The interception behavior to add.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.InterceptionBehaviorPipeline.Count">
            <summary>
            Get the number of interceptors in this pipeline.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.InvokeInterceptionBehaviorDelegate">
            <summary>
            This delegate type is the type that points to the next
            method to execute in the current pipeline.
            </summary>
            <param name="input">Inputs to the current method call.</param>
            <param name="getNext">Delegate to get the next interceptor in the chain.</param>
            <returns>Return from the next method in the chain.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.GetNextInterceptionBehaviorDelegate">
            <summary>
            This delegate type is passed to each interceptor's Invoke method.
            Call the delegate to get the next delegate to call to continue
            the chain.
            </summary>
            <returns>Next delegate in the interceptor chain to call.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.Glob">
            <summary>
            A &quot;glob&quot; is a string matching pattern. It is similar to the
            matches available in the file system (*.cs, for example). The Glob
            class implements this string matching.
            </summary>
            <remarks>Glob supports the following meta-characters:
                * - match zero or more characters
                ? - match any one character
            [abc] - match one character if it's in the characters inside the brackets.
            All other characters in the glob are literals.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Glob.#ctor(System.String)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Glob"/> instance that matches the given pattern.
            </summary>
            <remarks>
            The pattern match is case sensitive by default.
            </remarks>
            <param name="pattern">Pattern to use. See <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Glob"/> summary for
            details of the pattern.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Glob.#ctor(System.String,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Glob"/> instance that matches the given pattern.
            </summary>
            <param name="pattern">The pattern to use. See <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.Glob"/> summary for
            details of the patterns supported.</param>
            <param name="caseSensitive">If true, perform a case sensitive match. 
            If false, perform a case insensitive comparison.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.Glob.IsMatch(System.String)">
            <summary>
            Checks to see if the given string matches the pattern.
            </summary>
            <param name="s">String to check.</param>
            <returns>True if it matches, false if it doesn't.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ReflectionHelper">
            <summary>
            A collection of utility functions to encapsulate details of
            reflection and finding attributes.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReflectionHelper.GetPropertyFromMethod(System.Reflection.MethodBase)">
            <summary>
            Given a MethodBase for a property's get or set method,
            return the corresponding property info.
            </summary>
            <param name="method">MethodBase for the property's get or set method.</param>
            <returns>PropertyInfo for the property, or null if method is not part of a property.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReflectionHelper.GetPropertyFromMethod(System.Reflection.MethodInfo)">
            <summary>
            Given a MethodInfo for a property's get or set method,
            return the corresponding property info.
            </summary>
            <param name="method">MethodBase for the property's get or set method.</param>
            <returns>PropertyInfo for the property, or null if method is not part of a property.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReflectionHelper.GetAttributes``1(System.Reflection.MemberInfo,System.Boolean)">
            <summary>
            Given a particular MemberInfo, return the custom attributes of the
            given type on that member.
            </summary>
            <typeparam name="TAttribute">Type of attribute to retrieve.</typeparam>
            <param name="member">The member to look at.</param>
            <param name="inherits">True to include attributes inherited from base classes.</param>
            <returns>Array of found attributes.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ReflectionHelper.GetAllAttributes``1(System.Reflection.MemberInfo,System.Boolean)">
            <summary>
            Given a particular MemberInfo, find all the attributes that apply to this
            member. Specifically, it returns the attributes on the type, then (if it's a
            property accessor) on the property, then on the member itself.
            </summary>
            <typeparam name="TAttribute">Type of attribute to retrieve.</typeparam>
            <param name="member">The member to look at.</param>
            <param name="inherits">true to include attributes inherited from base classes.</param>
            <returns>Array of found attributes.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.ConstructorWithResolverKeysSelectorPolicy">
            <summary>
            A small implementation of <see cref="T:Microsoft.Practices.ObjectBuilder2.IConstructorSelectorPolicy"/> that returns the
            given <see cref="T:Microsoft.Practices.ObjectBuilder2.SelectedConstructor"/> object.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ConstructorWithResolverKeysSelectorPolicy.#ctor(Microsoft.Practices.ObjectBuilder2.SelectedConstructor)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.ConstructorWithResolverKeysSelectorPolicy"/> instance.
            </summary>
            <param name="selectedConstructor">Information about which constructor to select.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.ConstructorWithResolverKeysSelectorPolicy.SelectConstructor(Microsoft.Practices.ObjectBuilder2.IBuilderContext,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Choose the constructor to call for the given type.
            </summary>
            <param name="context">Current build context</param>
            <param name="resolverPolicyDestination">The <see cref="T:Microsoft.Practices.ObjectBuilder2.IPolicyList"/> to add any
            generated resolver objects into.</param>
            <returns>The chosen constructor.</returns>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocationMethods">
            <summary>
            MethodInfo objects for the methods we need to generate
            calls to on IMethodInvocation.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.MethodOverride">
            <summary>
            Represents the implementation of a method override.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.MethodOverride.BuildAbstractMethodInvokedException">
            <summary>
            Used to throw an <see cref="T:System.NotImplementedException"/> for overrides on abstract methods.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation">
            <summary>
            Implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation"/> used
            by the virtual method interceptor.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.#ctor(System.Object,System.Reflection.MethodBase,System.Object[])">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation"/> instance for the
            given target object and method, passing the <paramref name="parameterValues"/>
            to the target method.
            </summary>
            <param name="target">Object that is target of this invocation.</param>
            <param name="targetMethod">Method on <paramref name="target"/> to call.</param>
            <param name="parameterValues">Values for the parameters.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.CreateMethodReturn(System.Object,System.Object[])">
            <summary>
            Factory method that creates the correct implementation of
            IMethodReturn.
            </summary>
            <param name="returnValue">Return value to be placed in the IMethodReturn object.</param>
            <param name="outputs">All arguments passed or returned as out/byref to the method. 
            Note that this is the entire argument list, including in parameters.</param>
            <returns>New IMethodReturn object.</returns>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.CreateExceptionMethodReturn(System.Exception)">
            <summary>
            Factory method that creates the correct implementation of
            IMethodReturn in the presence of an exception.
            </summary>
            <param name="ex">Exception to be set into the returned object.</param>
            <returns>New IMethodReturn object</returns>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.Inputs">
            <summary>
            Gets the inputs for this call.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.Arguments">
            <summary>
            Collection of all parameters to the call: in, out and byref.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.InvocationContext">
            <summary>
            Retrieves a dictionary that can be used to store arbitrary additional
            values. This allows the user to pass values between call handlers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.Target">
            <summary>
            The object that the call is made on.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodInvocation.MethodBase">
            <summary>
            The method on Target that we're aiming at.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.IMethodReturn"/> used by
            the virtual method interception mechanism.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,System.Object,System.Object[])">
            <summary>
            Construct a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn"/> instance that returns
            a value.
            </summary>
            <param name="originalInvocation">The method invocation.</param>
            <param name="returnValue">Return value (should be null if method returns void).</param>
            <param name="arguments">All arguments (including current values) passed to the method.</param>
        </member>
        <member name="M:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn.#ctor(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,System.Exception)">
            <summary>
            Construct a <see cref="T:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn"/> instance for when the target method throws an exception.
            </summary>
            <param name="originalInvocation">The method invocation.</param>
            <param name="exception">Exception that was thrown.</param>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn.Outputs">
            <summary>
            The collection of output parameters. If the method has no output
            parameters, this is a zero-length list (never null).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn.ReturnValue">
            <summary>
            Returns value from the method call.
            </summary>
            <remarks>This value is null if the method has no return value.</remarks>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn.Exception">
            <summary>
            If the method threw an exception, the exception object is here.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.Unity.InterceptionExtension.VirtualMethodReturn.InvocationContext">
            <summary>
            Retrieves a dictionary that can be used to store arbitrary additional
            values. This allows the user to pass values between call handlers.
            </summary>
            <remarks>This is guaranteed to be the same dictionary that was used
            in the IMethodInvocation object, so handlers can set context
            properties in the pre-call phase and retrieve them in the after-call phase.
            </remarks>
        </member>
    </members>
</doc>
