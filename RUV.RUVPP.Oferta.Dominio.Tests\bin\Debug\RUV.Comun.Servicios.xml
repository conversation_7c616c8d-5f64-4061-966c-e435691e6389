<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.Comun.Servicios</name>
    </assembly>
    <members>
        <member name="T:RUV.Comun.Servicios.Caching.AzureCaching">
            <summary>
            Clase que controla las conexion hacia los servicios de Redis en Azure
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Caching.AzureCaching.LazyConnection">
            <summary>
            Lista lazy que controla la inicializacion de la conexion de la base de datos de manera segura (thread-safe).
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Caching.AzureCaching.#cctor">
            <summary>
            Constructor estatico, inicializa la conexion a la base de datos.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Caching.AzureCaching.Connection">
            <summary>
            Representa la conexion al servicio de Redis
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Caching.StackExchangeRedisExtensions">
            <summary>
            @Autor <PERSON>
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.LogBook.aop.LogBookInterceptionBehavior">
            <summary>
            Interceptor { Bitacora } 
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.LogBook.aop.LogBookInterceptionBehavior.TransactionDetail">
            <summary>
            Lista detalle transacción
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.LogBook.aop.LogBookInterceptionBehavior.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextInterceptionBehaviorDelegate)">
            <summary>
            Implemente este método para ejecutar su procesamiento .
            </summary>
            <param name="input"></param>
            <param name="getNext"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.Comun.Servicios.LogBook.entity.Bitacora">
            <summary>
            Entidad en el TableStorage
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.LogBook.entity.ParametrosBitacora">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.LogBook.util.RecordLogBook">
            <summary>
            Permite generar un nuevo registro en { TableStorageContext } de { Bitácora  }
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.LogBook.util.RecordLogBook.registerLog(RUV.Comun.Servicios.LogBook.entity.Bitacora)">
            <summary>
            Genera un nuevo registro en { TableStorageContext } de { Bitácora  }
            </summary>
            <param name="entity"></param>
        </member>
        <member name="T:RUV.Comun.Servicios.Logger.DelegateRegisterLog">
            <summary>
            Mapea el delegado para el manejo de logs
            </summary>
            <param name="message"></param>
            <param name="level"></param>
        </member>
        <member name="T:RUV.Comun.Servicios.Logger.DelegateRegisterLogMVC">
            <summary>
            Mapea el delegado para el manejo de logs
            </summary>
            <param name="message"></param>
            <param name="level"></param>
            <param name="controller"></param>
            <param name="action"></param>
        </member>
        <member name="T:RUV.Comun.Servicios.Logger.ILoggerHelperpublic">
            <summary>
            Modela la logica de mantenimiento de logs
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.ILoggerHelperpublic.RegisterLog(System.String,RUV.Comun.Servicios.Logger.LogLevel)">
            <summary>
            Registra al log con el siguiente formato:
            yyyy-MM-dd HH:mm tt - mensaje
            </summary>
            <param name="message">Mensaje a mostrar en el registro al log</param>
            <param name="level">Nivel del evento</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.ILoggerHelperpublic.RegisterLog(System.String,System.Guid,RUV.Comun.Servicios.Logger.LogLevel)">
            <summary>
            Registra al log con el siguiente formato:
            GUID - yyyy-MM-dd HH:mm tt - mensaje
            </summary>
            <param name="message">Mensaje a mostrar en el registro al log</param>
            <param name="identifier">Guid a mostrar en el registro al log</param>
            <param name="level">Nivel del evento <see cref="T:RUV.Comun.Servicios.Logger.LogLevel"/></param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.ILoggerHelperpublic.RegisterException(System.Exception)">
            <summary>
            Registra al log la informacion de la excepcion porporcionada
            </summary>
            <param name="exception">excepcion donde se obtendran los datos</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.ILoggerHelperpublic.RegisterException(System.Exception,System.String,System.String)">
            <summary>
            Registra al log la informacion de la excepcion porporcionada
            </summary>
            <param name="exception">excepcion donde se obtendran los datos</param>
        </member>
        <member name="T:RUV.Comun.Servicios.Logger.LoggerFacility">
            <summary>
            Clase que facilita/Expone la implementacion del logger
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerFacility.#ctor(System.Int32,System.Int32,System.String,System.String,System.Int32,System.Int32)">
            <summary>
            constructor para setear las configuraciones de Diagnostics
            </summary>
            <param name="backOffSecs"></param>
            <param name="counterSampleRate"></param>
            <param name="diagnosticCounters"></param>
            <param name="nivelLog"></param>
            <param name="retryPolicyNumber"></param>
            <param name="scheduleTransferPeriod"></param>
        </member>
        <member name="P:RUV.Comun.Servicios.Logger.LoggerFacility._DiagnosticSettings">
            <summary>
            persiste las configuraciones para Diagnostics
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerFacility.InitializeDiagnostics">
            <summary>
            Inicializa Diagnostics de azure
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:RUV.Comun.Servicios.Logger.LoggerHelper" -->
        <member name="F:RUV.Comun.Servicios.Logger.LoggerHelper.DelegateRegisterLog">
            <summary>
            instancia del delegado que ejecutara el trace
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerHelper.#ctor(RUV.Comun.Servicios.Logger.DelegateRegisterLog)">
            <summary>
            Contructor para instanciar un objeto LoggerHelper
            </summary>
            <param name="action">Delegado a utilizar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerHelper.#ctor(RUV.Comun.Servicios.Logger.DelegateRegisterLogMVC)">
            <summary>
            Contructor para instanciar un objeto LoggerHelper
            </summary>
            <param name="action">Delegado a utilizar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerHelper.RegisterLog(System.String,RUV.Comun.Servicios.Logger.LogLevel)">
            <summary>
            Registra al log con el siguiente formato:
            yyyy-MM-dd HH:mm tt - mensaje
            </summary>
            <param name="message">Mensaje a mostrar en el registro al log</param>
            <param name="level">Nivel del evento</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerHelper.RegisterLog(System.String,System.Guid,RUV.Comun.Servicios.Logger.LogLevel)">
            <summary>
            Registra al log con el siguiente formato:
            GUID - yyyy-MM-dd HH:mm tt - mensaje
            </summary>
            <param name="message">Mensaje a mostrar en el registro al log</param>
            <param name="identifier">Guid a mostrar en el registro al log</param>
            <param name="level">Nivel del evento <see cref="T:RUV.Comun.Servicios.Logger.LogLevel"/></param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerHelper.RegisterException(System.Exception)">
            <summary>
            Registra al log la informacion de la excepcion porporcionada
            </summary>
            <param name="exception">excepcion donde se obtendran los datos</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Logger.LoggerHelper.RegisterException(System.Exception,System.String,System.String)">
            <summary>
            Registra al log la informacion de la excepcion porporcionada
            </summary>
            <param name="exception">excepcion donde se obtendran los datos</param>
        </member>
        <member name="T:RUV.Comun.Servicios.Mail.MailSenderUtil">
            <summary>
            Modela la logica para el envio de Coreos
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Mail.MailSenderUtil.SendMail``1(RUV.Comun.Servicios.Mail.SmtpClientConfiguration,``0,System.Boolean)">
            <summary>
            
            </summary>
            <param name="config">Configuración para el envío del correo</param>
            <param name="mensajeCorreo">Mensaje de correo que se enviará</param>
            <returns>Regresa el estatus del envío</returns>
        </member>
        <member name="T:RUV.Comun.Servicios.Mail.Message">
            <summary>
            Definicion de la entidad de correo
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.Message.Subject">
            <summary>
            Asunto con el que se enviara el correo
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.Message.To">
            <summary>
            Lista de correos a los que se enviara el correo
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.Message.Cc">
            <summary>
            Lista de correos a los que se envara copia del correo
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.Message.Body">
            <summary>
            Mensaje que contendra el coreo
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Mail.SmtpClientConfiguration">
            <summary>
            Modela las configuraciones que se utilizaran para enviar un correo
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.#ctor">
            <summary>
            Constructor por defecto
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.#ctor(System.String,System.String,System.Int32,System.String,System.String,System.Boolean)">
            <summary>
            Objeto para el envío de correos
            </summary>
            <param name="user">Usuario para autenticar en el servidor de correo</param>
            <param name="password">Password para autenticar en el servidor de correo</param>
            <param name="port">Puerto para el evío de correo</param>
            <param name="server">Servidor de envío de correo</param>
            <param name="from">Cuenta de envío de correo</param>
            <param name="ssl">Indica si esta habilitada la seguridad con ssl</param>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.user">
            <summary>
            Usuario para conectarse al servidor de envio de correos
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.password">
            <summary>
            Contraseña para conectarse al servidor de envio de correos
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.port">
            <summary>
            Puerto por el cual se enviara el correo
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.server">
            <summary>
            Direccion/Url del servidor de envio de correos
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.from">
            <summary>
            Correo  que se utilizara para enviar el correo
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Mail.SmtpClientConfiguration.sslEnabled">
            <summary>
            Especifica si se utilizara el protocolo SSL(Secure Sockets Layer) al enviar el coreo
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Parameters.aop.ParametersInterceptionBehavior">
            <summary>
            Interceptor { Configuracion.Parametros } 
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Parameters.aop.ParametersInterceptionBehavior.Invoke(Microsoft.Practices.Unity.InterceptionExtension.IMethodInvocation,Microsoft.Practices.Unity.InterceptionExtension.GetNextInterceptionBehaviorDelegate)">
            <summary>
            Implemente este método para ejecutar su procesamiento .
            </summary>
            <param name="input"></param>
            <param name="getNext"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.Comun.Servicios.Parameters.aop.ParametersInterceptionBehavior.Operacion">
            <summary>
            Tipo de operación
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Parameters.da.ParametersDA">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Parameters.da.ParametersDA.getParametro(System.String)">
            <summary>
            Permite recuperar un parámetro de Configuracion.Parametro
            </summary>
            <param name="clave"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Parameters.da.ParametersDA.getListParametros(System.String)">
            <summary>
            Permite recuperar una lista de parámetros de Configuracion.Parametro
            </summary>
            <param name="grupoParametro"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.Comun.Servicios.Parameters.entity.ParametersDTO">
            <summary>
            Entidad { [configuracion].[Parametro] }
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Parameters.service.ParametersService">
            <summary>
            Permite obtener parametros de la entidad { [configuracion].[Parametro] }
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Parameters.service.ParametersService.getParametro(System.String)">
            <summary>
            Permite obtener parametros de la entidad { [configuracion].[Parametro] } clave = ??
            </summary>
            <param name="clave"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Parameters.service.ParametersService.getListParametros(System.String)">
            <summary>
            Permite obtener parametros de la entidad { [configuracion].[Parametro] } grupoParametro = ??
            </summary>
            <param name="grupoParametro"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.DocumentoNombreContenedorInvalido">
            <summary>
              Looks up a localized string similar to nombreContenedor solo permite alfanumericos en minúsculas: {0}.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.DocumentoNombreContenedorMinMaxLength">
            <summary>
              Looks up a localized string similar to El nombre del contenedor debe tener entre 3 y 63 caracteres.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.DocumentoNombreInvalido">
            <summary>
              Looks up a localized string similar to El nombre del archivo solo puede contener alfanuméricos, paréntesis, espacios, guión alto y guión bajo: {0}.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.DocumentoNombreMinMaxLength">
            <summary>
              Looks up a localized string similar to El nombre del contenedor debe tener entre 1 y 1,024 caracteres.
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Recursos.RecursosDocumentosBase.DocumentoNombrePatron">
            <summary>
              Looks up a localized string similar to ^[\w()/\-_\s\.]*$.
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext">
            <summary>
            Modela la logica de acceso y mantenimiento al blob storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.BeginFile">
            <summary>
            Posicion que se tomara como inicial para el stream
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.Kb">
            <summary>
            Se ocupara para calcular el tamaño del blob
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext._ConnectionString">
            <summary>
            Cadena de conexion para crear el CloudStorageAccount
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext._numRetries">
            <summary>
            Numero de reintentos
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext._StorageCredentials">
            <summary>
            Credenciales para acceder al blob storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext._blobClient">
            <summary>
            Cliente para conectarse al Blob Storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext._timeToWait">
            <summary>
            Tiempo que que se esperara para ejecutar la siguiente operacion: exponencial
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.#ctor(System.String,System.Int32,System.TimeSpan)">
            <summary>
            Crea una instancia BlobStorageContext
            </summary>
            <param name="connectionString">Cadena de conexion para crear el CloudStorageAccount</param>
            <param name="numRetries">Numero de reintentos</param>
            <param name="timeToWait">Tiempo que que se esperara para ejecutar la siguiente operacion: exponencial</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.CreateBlobContainerIfNotExist(System.String,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,Microsoft.WindowsAzure.Storage.Blob.BlobRequestOptions)">
            <summary>
            Crea un contenedor de blobs si no existe
            </summary>
            <param name="containerName">Nombre del contenedor</param>
            <param name="accessType"></param>
            <returns>Contenedor de blobs</returns>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.BlobClient">
            <summary>
            Cliente para conectarse al Blob Storage
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.GetBlobContainer(System.String)">
            <summary>
            Obtiene un contenedor de blobs
            </summary>
            <param name="containerName">Nombre del contenedor</param>
            <returns>Contenedor de blobs</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.GetSizeBlobInKb(System.String,System.String)">
            <summary>
            Obtiene el tamaño del blob en Kb
            </summary>
            <param name="blobName">Nombre del blob</param>
            <param name="blobContainerName">Nombre del contenedor</param>
            <returns>tamaño del blob en Kb</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.CopyContainer(System.String,System.String)">
            <summary>
            Copia contenido de un contenedor a otro
            </summary>
            <param name="oldContainerName">Contenedor con los blob a migrar</param>
            <param name="newContainerName">Contenedor donde se migrara el contenido</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.UploadBlob(System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobBase,System.Boolean,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,System.Boolean)">
            <summary>
            Guarda/Carga al Blob Storage un stream
            </summary>
            <param name="blobContainerName">Nombre del contenedor de blobs</param>
            <param name="file">Objeto BlobBase del cual se obtendra el Stream</param>
            <param name="isOverwritable">Especifica si el archivo se sobre escribira o no</param>
            <param name="accessType"></param>
            <returns>Url de referencia al blob</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.UploadBlob(System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobBase,System.Int32,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType)">
            <summary>
            Carga/Guarda un stream al Blob Storage por partes
            </summary>
            <param name="blobContainerName">Nombre del contenedor de blobs</param>
            <param name="file">Objeto BlobBase del cual se obtendra el Stream a cargar</param>
            <param name="blockSizeInKb">entero que se utilizara para partir el stream</param>
            <param name="accessType"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.DownloadBlob(System.String,System.String,System.IO.Stream)">
            <summary>
            Descarga un blob
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="blobName">Nombre del blob</param>
            <param name="streamData">Stream para almacenar el contenido del blob</param>
            <returns>Contenido del blob</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.DownloadBlob(System.String,System.String)">
            <summary>
            Descarga un blob
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="blobName">Nombre del blob</param>
            <returns>BlobBase</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.GetBlobReference(System.String,System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobReferenceOptions,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,Microsoft.WindowsAzure.Storage.Blob.BlobRequestOptions)">
            <summary>
            Obtiene la referencia a un blob <see cref="T:Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob"/>
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="blobName">Nombre del blob</param>
            <param name="options">Opciones para obtener la referencia al blob</param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.GetBlobReferences(System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobReferenceOptions,System.Int32,System.Int32,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,Microsoft.WindowsAzure.Storage.Blob.BlobRequestOptions)">
            <summary>
            Obtiene una lista de referencias del blob
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="options">Opciones para obtener la referencia al blob</param>
            <param name="pagina">Número para la paginación de la consulta</param>
            <param name="bloque">Total de elementos regresos en una pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.DeleteBlobByName(System.String,System.String)">
            <summary>
            Borra un blob del Blob Storage
            </summary>
            <param name="blobName">Nombre del blob</param>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <returns>verdadero o falso, segun sea el caso</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.CopyBlobAsync(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Copia un blob dentro del Blob Storage.
            </summary>
            <param name="sourceBlobName">Nombre del blob origen.</param>
            <param name="sourceContainer">Nombre del contenedor origen.</param>
            <param name="targetBlobName">Nombre del blob destino.</param>
            <param name="targetContainer">Nombre del contenedor destino.</param>
            <param name="isOverwritable">Indica si se sutituye o no el blob en caso de existir.</param>
            <returns>Url de referencia al blob copiado.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.MoveBlobAsync(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Mueve un blob dentro del Blob Storage.
            </summary>
            <param name="sourceBlobName">Nombre del blob origen.</param>
            <param name="sourceContainer">Nombre del contenedor origen.</param>
            <param name="targetBlobName">Nombre del blob destino.</param>
            <param name="targetContainer">Nombre del contenedor destino.</param>
            <param name="isOverwritable">Indica si se sutituye o no el blob en caso de existir.</param>
            <returns>Url de referencia al blob movido.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.SetMatchProperties(Microsoft.WindowsAzure.Storage.Blob.BlobProperties,Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob)">
            <summary>
            Setea las propiedades proporcionadas a un blob
            </summary>
            <param name="properties">Propiedades a configurar</param>
            <param name="blob">Blob al que se le configuraran las propiedades</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.GetBlobSASUri(System.String,System.String)">
            <summary>
            Regresa el url con un token SAS para la visualizacion de un blob
            </summary>
            <param name="blobContainer">Nobre del contenedor</param>
            <param name="blobName">Nombre del blob</param>
            <param name="ExpiryTime">Tiempo en minutos en el que expira el token</param>
            <param name="startTime">Tiempo en minutos en que empezara a ser valido el token</param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.GetBlobSASUriContentDisposition(System.String,System.String)">
            <summary>
            Regresa el url con un token SAS para la visualizacion de un blob
            </summary>
            <param name="blobContainer">Nobre del contenedor</param>
            <param name="blobName">Nombre del blob</param>
            <param name="ExpiryTime">Tiempo en minutos en el que expira el token</param>
            <param name="startTime">Tiempo en minutos en que empezara a ser valido el token</param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.EnableCors">
            <summary>
            Habilita CORS en el cliente del Blob Storage.
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.DeleteContainer(System.String)">
            <summary>
            Elimina el Contenedor Blob
            </summary>
            <param name="blobContainer"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext.formatBlobName(System.String)" -->
        <member name="T:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper">
            <summary>
            clase helper para el acceso al blob storage
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.#ctor">
            <summary>
            Constructor sin parametros
            Inicializa valores por defecto
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.#ctor(System.Int32,System.TimeSpan)">
            <summary>
            Crea una instancia de <see cref="T:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper"/>
            </summary>
            <param name="numeroIntentos">Numero de intentos que se efectuaran cuando una transaccion sea fallida</param>
            <param name="tiempoEspera">Tiempo de espera entre cada intentos</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.#ctor(System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            
            </summary>
            <param name="numeroIntentos"></param>
            <param name="tiempoEspera"></param>
            <param name="cadenaConexion"></param>
            <param name="esSobreEscribible"></param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.#ctor(System.Int32,System.TimeSpan,System.Boolean)">
            <summary>
            Crea una instancia de <see cref="T:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper"/>
            </summary>
            <param name="numeroIntentos">Numero de intentos que se efectuaran cuando una transaccion sea fallida</param>
            <param name="tiempoEspera">Tiempo de espera entre cada intentos</param>
            <param name="esSobreEscribible">Especifica si los documentos a subir seran sobreescribibles o no</param>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.NumeroIntentos">
            <summary>
            Numero de intentos que se efectuaran cuando una transaccion sea fallida
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.TiempoEspera">
            <summary>
            Tiempo de espera entre cada intento
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BlobBase">
            <summary>
            propiedad <see cref="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BlobBase"/>
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.EsSobreEscribible">
            <summary>
            Especifica si al subir un archivo este se sobreescribe si ya existe o no
            en caso de que su valor sea false, se crea un consecutivo en cada subida
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.CadenaConexion">
            <summary>
            Cadena de conexion hacia el storage
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.Bloque">
            <summary>
            Número de items que se traeran en caso de paginado
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.StorageContext">
            <summary>
            propiedad <see cref="T:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext"/>
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.DescargarBlob">
            <summary>
            Obtiene un objeto del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BlobBase"/> que contendra los datos de un blob
            </summary>
            <returns>objeto del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BlobBase"/></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.ObtenerBlobReference">
            <summary>
            Obtiene un objeto del tipo <see cref="T:Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob"/> que contendra los datos de un blob
            </summary>
            <returns>objeto del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BlobBase"/></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.ObtenerBlobReferences(System.Int32)">
            <summary>
            Obtiene una lista del tipo <see cref="T:Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob"/> que contendra los datos de un blob
            </summary>
            <param name="pagina">Número para la paginación de la consulta</param>
            <returns>Lista del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BlobBase"/></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.CargarBlobFromMemoryStream(System.Boolean)">
            <summary>
            Carga un blob al storage utilizando el la propiedad BlobBase
            </summary>
            <returns>La url generada para el archivo cargado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BorrarBlob">
            <summary>
            Elimina fisicamente un archivo del blob storage
            </summary>
            <returns>True si la transaccion fue exitosa, de lo contrario regresa false</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.ObtenerSASBlob">
            <summary>
            Obtiene un SAS para un archivo en el blob storage
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.BorrarContenedor(System.String)">
            <summary>
            Eimina el un Contenedor y por tanto los blobs que contiene 
            </summary>
            <returns>true si se borro el Contenedor</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.BlobStorageHelper.CopiarContenedor(System.String,System.String)">
            <summary>
            Copiae el contenido de un contenedor a otro
            </summary>
            <param name="contenedorViejo">Viejo contenedor</param>
            <param name="contenedorNuevo">Nuevo contenedor</param>
        </member>
        <member name="T:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase">
            <summary>
            Modela las propiedades basicas de un Blob
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.isDisposed">
            <summary>
            Campo que almacena true cuando se ha ejecutado el metodo Dispose de la clase.
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.#ctor(System.IO.Stream)">
            <summary>
            Crea una instancia BlobBase
            </summary>
            <param name="streamData">Stream del contenido del blob</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.#ctor">
            <summary>
            Constructor por defecto
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.#ctor(System.IO.Stream,Microsoft.WindowsAzure.Storage.Blob.BlobProperties,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Crea una instancia BlobBase
            </summary>
            <param name="streamData">Stream del contenido del blob</param>
            <param name="properties">Propiedades del blob</param>
            <param name="name">Nombre del blob</param>
            <param name="metadata">Metadatos del blob</param>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.StreamData">
            <summary>
            Almacena en un objeto tipo Stream el contenido del blob
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.Properties">
            <summary>
            Almacena las propiedades del blob
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.Name">
            <summary>
            Nombre del blob
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.ContainerName">
            <summary>
            Nombre del contenedor del blob
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.Metadata">
            <summary>
            Metadatos del Blob
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.Data.BlobBase.Dispose">
            <summary>
            Destruye la instancia
            </summary>
        </member>
        <member name="T:RUV.Comun.Servicios.Storage.Blob.Data.BlobReferenceOptions">
            <summary>
            Modela las opciones de como se creara la referencia a un blob
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.BlobClient">
            <summary>
            Cliente para conectarse al Blob Storage
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.CopyBlobAsync(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Copia un blob dentro del Blob Storage.
            </summary>
            <param name="sourceBlobName">Nombre del blob origen.</param>
            <param name="sourceContainer">Nombre del contenedor origen.</param>
            <param name="targetBlobName">Nombre del blob destino.</param>
            <param name="targetContainer">Nombre del contenedor destino.</param>
            <param name="isOverwritable">Indica si se sutituye o no el blob en caso de existir.</param>
            <returns>Url de referencia al blob copiado.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.CopyContainer(System.String,System.String)">
            <summary>
            Copia contenido de un contenedor a otro
            </summary>
            <param name="oldContainerName">Contenedor con los blob a migrar</param>
            <param name="newContainerName">Contenedor donde se migrara el contenido</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.CreateBlobContainerIfNotExist(System.String,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,Microsoft.WindowsAzure.Storage.Blob.BlobRequestOptions)">
            <summary>
            Crea un contenedor de blobs si no existe
            </summary>
            <param name="containerName">Nombre del contenedor</param>
            <param name="accessType"></param>
            <returns>Contenedor de blobs</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.DeleteBlobByName(System.String,System.String)">
            <summary>
            Borra un blob del Blob Storage
            </summary>
            <param name="blobName">Nombre del blob</param>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <returns>verdadero o falso, segun sea el caso</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.DeleteContainer(System.String)">
            <summary>
            Elimina el Contenedor Blob
            </summary>
            <param name="blobContainer">Contenedor a borrar.</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.DownloadBlob(System.String,System.String)">
            <summary>
            Descarga un blob
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="blobName">Nombre del blob</param>
            <returns>BlobBase</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.DownloadBlob(System.String,System.String,System.IO.Stream)">
            <summary>
            Descarga un blob
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="blobName">Nombre del blob</param>
            <param name="streamData">Stream para almacenar el contenido del blob</param>
            <returns>Contenido del blob</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.EnableCors">
            <summary>
            Habilita CORS en el cliente del Blob Storage.
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.GetBlobContainer(System.String)">
            <summary>
            Obtiene un contenedor de blobs
            </summary>
            <param name="containerName">Nombre del contenedor</param>
            <returns>Contenedor de blobs</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.GetBlobReference(System.String,System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobReferenceOptions,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,Microsoft.WindowsAzure.Storage.Blob.BlobRequestOptions)">
            <summary>
            Obtiene la referencia a un blob <see cref="T:Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob"/>
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="blobName">Nombre del blob</param>
            <param name="options">Opciones para obtener la referencia al blob</param>
            <returns>Referencia al blob.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.GetBlobReferences(System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobReferenceOptions,System.Int32,System.Int32,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,Microsoft.WindowsAzure.Storage.Blob.BlobRequestOptions)">
            <summary>
            Obtiene una lista de referencias del blob
            </summary>
            <param name="blobContainer">Nombre del contenedor del blob</param>
            <param name="options">Opciones para obtener la referencia al blob</param>
            <param name="pagina">Número para la paginación de la consulta</param>
            <param name="bloque">Total de elementos regresos en una pagina</param>
            <returns>Lista de referencias al blob.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.GetBlobSASUri(System.String,System.String)">
            <summary>
            Regresa el url con un token SAS para la visualizacion de un blob
            </summary>
            <param name="blobContainer">Nobre del contenedor</param>
            <param name="blobName">Nombre del blob</param>
            <param name="ExpiryTime">Tiempo en minutos en el que expira el token</param>
            <param name="startTime">Tiempo en minutos en que empezara a ser valido el token</param>
            <returns>URI con el token SAS del blob.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.GetBlobSASUriContentDisposition(System.String,System.String)">
            <summary>
            Regresa el url con un token SAS para la visualizacion de un blob
            </summary>
            <param name="blobContainer">Nobre del contenedor</param>
            <param name="blobName">Nombre del blob</param>
            <param name="ExpiryTime">Tiempo en minutos en el que expira el token</param>
            <param name="startTime">Tiempo en minutos en que empezara a ser valido el token</param>
            <returns>URI con el token SAS del blob</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.MoveBlobAsync(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Mueve un blob dentro del Blob Storage.
            </summary>
            <param name="sourceBlobName">Nombre del blob origen.</param>
            <param name="sourceContainer">Nombre del contenedor origen.</param>
            <param name="targetBlobName">Nombre del blob destino.</param>
            <param name="targetContainer">Nombre del contenedor destino.</param>
            <param name="isOverwritable">Indica si se sutituye o no el blob en caso de existir.</param>
            <returns>Url de referencia al blob movido.</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.SetMatchProperties(Microsoft.WindowsAzure.Storage.Blob.BlobProperties,Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob)">
            <summary>
            Setea las propiedades proporcionadas a un blob
            </summary>
            <param name="properties">Propiedades a configurar</param>
            <param name="blob">Blob al que se le configuraran las propiedades</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.UploadBlob(System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobBase,System.Int32,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType)">
            <summary>
            Carga/Guarda un stream al Blob Storage por partes
            </summary>
            <param name="blobContainerName">Nombre del contenedor de blobs</param>
            <param name="file">Objeto BlobBase del cual se obtendra el Stream a cargar</param>
            <param name="blockSizeInKb">entero que se utilizara para partir el stream</param>
            <param name="accessType">Tipo de acceso al blob.</param>
            <returns>Url de referencia al blob</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageContext.UploadBlob(System.String,RUV.Comun.Servicios.Storage.Blob.Data.BlobBase,System.Boolean,Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType,System.Boolean)">
            <summary>
            Guarda/Carga al Blob Storage un stream
            </summary>
            <param name="blobContainerName">Nombre del contenedor de blobs</param>
            <param name="file">Objeto BlobBase del cual se obtendra el Stream</param>
            <param name="isOverwritable">Especifica si el archivo se sobre escribira o no</param>
            <param name="accessType">Tipo de acceso al blob.</param>
            <returns>Url de referencia al blob</returns>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BlobBase">
            <summary>
            propiedad <see cref="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BlobBase"/>
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.Bloque">
            <summary>
            Número de items que se traeran en caso de paginado
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.CadenaConexion">
            <summary>
            Cadena de conexion hacia el storage
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.EsSobreEscribible">
            <summary>
            Especifica si al subir un archivo este se sobreescribe si ya existe o no
            en caso de que su valor sea false, se crea un consecutivo en cada subida
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.NumeroIntentos">
            <summary>
            Numero de intentos que se efectuaran cuando una transaccion sea fallida
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.StorageContext">
            <summary>
            propiedad <see cref="T:RUV.Comun.Servicios.Storage.Blob.BlobStorageContext"/>
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.TiempoEspera">
            <summary>
            Tiempo de espera entre cada intento
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BorrarBlob">
            <summary>
            Elimina fisicamente un archivo del blob storage
            </summary>
            <returns>True si la transaccion fue exitosa, de lo contrario regresa false</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BorrarContenedor(System.String)">
            <summary>
            Eimina el un Contenedor y por tanto los blobs que contiene 
            </summary>
            <returns>true si se borro el Contenedor</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.CargarBlobFromMemoryStream(System.Boolean)">
            <summary>
            Carga un blob al storage utilizando el la propiedad BlobBase
            </summary>
            <returns>La url generada para el archivo cargado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.CopiarContenedor(System.String,System.String)">
            <summary>
            Copiae el contenido de un contenedor a otro
            </summary>
            <param name="contenedorViejo">Viejo contenedor</param>
            <param name="contenedorNuevo">Nuevo contenedor</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.DescargarBlob">
            <summary>
            Obtiene un objeto del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BlobBase"/> que contendra los datos de un blob
            </summary>
            <returns>objeto del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BlobBase"/></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.ObtenerBlobReference">
            <summary>
            Obtiene un objeto del tipo <see cref="T:Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob"/> que contendra los datos de un blob
            </summary>
            <returns>objeto del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BlobBase"/></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.ObtenerBlobReferences(System.Int32)">
            <summary>
            Obtiene una lista del tipo <see cref="T:Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob"/> que contendra los datos de un blob
            </summary>
            <param name="pagina">Número para la paginación de la consulta</param>
            <returns>Lista del tipo <see cref="P:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.BlobBase"/></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Blob.IBlobStorageHelper.ObtenerSASBlob">
            <summary>
            Obtiene un SAS para un archivo en el blob storage
            </summary>
            <returns></returns>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.QueueClient">
            <summary>
            Cliente para conectarse al queue storage
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.CleanQueue(System.String)">
            <summary>
            Elimina todos los mensajes de la Queue
            </summary>
            <param name="tipoQueue">Nombre del Queue a limpiar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.CreateQueue(System.String)">
            <summary>
            Crea una Queue si no existe
            </summary>
            <param name="queueName"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.DeleteMessage(System.String,Microsoft.WindowsAzure.Storage.Queue.CloudQueueMessage)">
            <summary>
            Borra un mensaje del Queue
            </summary>
            <param name="queueName">Nombre del Queue</param>
            <param name="message">Mensaje a borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.DeleteMessage(System.String,System.String,System.String)">
            <summary>
            Borra un mensaje de la Queue
            </summary>
            <param name="queueName">Nombre de la Queue</param>
            <param name="id">Id del mensaje a borrar</param>
            <param name="popReceipt">Pop Receipt del mensaje a borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.DeleteMessageFromObject``1(System.String,``0)">
            <summary>
            Borra un mensaje de la Queue
            </summary>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje para borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.DeleteQueue(System.String)">
            <summary>
            Borra un Queue del Queue Storage
            </summary>
            <param name="tipoQueue">Nombre de la cola a borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.EnqueueMessage(System.String,Microsoft.WindowsAzure.Storage.Queue.CloudQueueMessage,System.Nullable{System.Double})">
            <summary>
            Agrega un mensaje al Queue
            </summary>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje a agregar</param>
            <param name="invisibleTimeSeconds">Tiempo en que el mensaje permanecera invisible apartir de que se agrega, null: si se desea que sea visible inmediatamente</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.EnqueueMessage``1(System.String,``0)">
            <summary>
            Agrega un mensaje al Queue
            </summary>
            <typeparam name="T">tipo del Objeto a agregar</typeparam>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje para agregar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.EnqueueMessage``1(System.String,``0,System.Nullable{System.Double})">
            <summary>
            Agrega un mensaje al Queue
            </summary>
            <typeparam name="T">tipo del Objeto a agregar</typeparam>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje a agregar</param>
            <param name="invisibleTimeSeconds">Tiempo en que el mensaje permanecera invisible apartir de que se agrega, null: si se desea que sea visible inmediatamente</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.EnqueueMessageFromObject``1(System.String,``0,System.Nullable{System.Double})">
            <summary>
            Agrega una instacia de un objeto como mensaje al Queue
            </summary>
            <typeparam name="T">tipo del Objeto a agregar</typeparam>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje para agregar</param>
            <param name="invisibleTimeSeconds"></param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.GetAllEntitiesFromMessages``1(System.String)">
            <summary>
            Obtiene una lista con los mensajes casteados al tipo especificado
            </summary>
            <typeparam name="T">Tipo al que se convertiran los mensajes</typeparam>
            <param name="queueName">Nombre del Queue</param>
            <returns>Lista con los elemenos convertidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.GetEntityFromMessages``1(System.String,System.Int32,System.TimeSpan)">
            <summary>
            Obtiene una Coleccion de mensajes
            </summary>
            <typeparam name="T">Tipo al que se intentara convertir la queue</typeparam>
            <param name="queueName">Nombre del queue</param>
            <param name="size">Numero de mensajes que se obtendran</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion de enttidades casteadas al tipo generico especificado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.GetMessage(System.String,System.TimeSpan)">
            <summary>
            Obtiene un mensaje del queue
            </summary>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Mensaje obtenido</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.GetMessages(System.Int32,System.String,System.TimeSpan)">
            <summary>
            Obtiene una coleccion de mensajes del queue
            </summary>
            <param name="size">Numero de mensajes a obtendran</param>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion con los mensajes obtenidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.GetMessagesList(System.Int32,System.String,System.TimeSpan)">
            <summary>
            Obtiene una lista de mensajes del queue
            </summary>
            <param name="size">Numero de mensajes a obtendran</param>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion con los mensajes obtenidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.IQueueStorageContext.GetMessagesToObjectList``1(System.Int32,System.String,System.TimeSpan)">
            <summary>
            Obtiene una coleccion de mensajes del queue
            </summary>
            <param name="size">Numero de mensajes a obtendran</param>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion con los mensajes obtenidos</returns>
        </member>
        <member name="T:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext">
            <summary>
            Modela la logica de acceso y mantenimiento al queue storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext._disposed">
            <summary>
            especifica si se ha ejecutado dispose()
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext._queueClient">
            <summary>
            Cliente para conectarse al queue storage
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.#ctor(System.String,Microsoft.WindowsAzure.Storage.Auth.StorageCredentials,System.Int32,System.TimeSpan)">
            <summary>
            Crea una insntancia QueueStorageContext
            </summary>
            <param name="baseAddress">Endpoint al servicio de queue que se utilizara para crear el cliente</param>
            <param name="credentials">Credenciales a usar par aautenticarse al storage</param>
            <param name="noReintentos">Numero maximo de reintentos</param>
            <param name="tiempoEspera">Tiempo de espera entre reintentos</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.#ctor(System.String,System.Int32,System.TimeSpan)">
            <summary>
            Crea una insntancia QueueStorageContext
            </summary>
            <param name="connectionString">Cdena de coneccion para acceder al storage</param>
            <param name="noReintentos">Numero maximo de reintentos</param>
            <param name="tiempoEspera">Tiempo de espera entre reintentos</param>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.QueueClient">
            <summary>
            Cliente para conectarse al queue storage
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.Dispose">
            <summary>
            Marca la clase para que sus recursos sean reciclados por el Garbage Collector.
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.CreateQueue(System.String)">
            <summary>
            Crea una Queue si no existe
            </summary>
            <param name="queueName"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.GetEntityFromMessages``1(System.String,System.Int32,System.TimeSpan)">
            <summary>
            Obtiene una Coleccion de mensajes
            </summary>
            <typeparam name="T">Tipo al que se intentara convertir la queue</typeparam>
            <param name="queueName">Nombre del queue</param>
            <param name="size">Numero de mensajes que se obtendran</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion de enttidades casteadas al tipo generico especificado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.GetMessage(System.String,System.TimeSpan)">
            <summary>
            Obtiene un mensaje del queue
            </summary>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Mensaje obtenido</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.GetMessagesList(System.Int32,System.String,System.TimeSpan)">
            <summary>
            Obtiene una lista de mensajes del queue
            </summary>
            <param name="size">Numero de mensajes a obtendran</param>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion con los mensajes obtenidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.GetMessages(System.Int32,System.String,System.TimeSpan)">
            <summary>
            Obtiene una coleccion de mensajes del queue
            </summary>
            <param name="size">Numero de mensajes a obtendran</param>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion con los mensajes obtenidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.GetMessagesToObjectList``1(System.Int32,System.String,System.TimeSpan)">
            <summary>
            Obtiene una coleccion de mensajes del queue
            </summary>
            <param name="size">Numero de mensajes a obtendran</param>
            <param name="queueName">Nombre del queue</param>
            <param name="hiddenTime">Intervalo de tiempo que permanecera oculta la queue</param>
            <returns>Coleccion con los mensajes obtenidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.GetAllEntitiesFromMessages``1(System.String)">
            <summary>
            Obtiene una lista con los mensajes casteados al tipo especificado
            </summary>
            <typeparam name="T">Tipo al que se convertiran los mensajes</typeparam>
            <param name="queueName">Nombre del Queue</param>
            <returns>Lista con los elemenos convertidos</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.EnqueueMessage(System.String,Microsoft.WindowsAzure.Storage.Queue.CloudQueueMessage,System.Nullable{System.Double})">
            <summary>
            Agrega un mensaje al Queue
            </summary>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje a agregar</param>
            <param name="invisibleTimeSeconds">Tiempo en que el mensaje permanecera invisible apartir de que se agrega, null: si se desea que sea visible inmediatamente</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.EnqueueMessage``1(System.String,``0,System.Nullable{System.Double})">
            <summary>
            Agrega un mensaje al Queue
            </summary>
            <typeparam name="T">tipo del Objeto a agregar</typeparam>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje a agregar</param>
            <param name="invisibleTimeSeconds">Tiempo en que el mensaje permanecera invisible apartir de que se agrega, null: si se desea que sea visible inmediatamente</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.EnqueueMessage``1(System.String,``0)">
            <summary>
            Agrega un mensaje al Queue
            </summary>
            <typeparam name="T">tipo del Objeto a agregar</typeparam>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje para agregar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.EnqueueMessageFromObject``1(System.String,``0,System.Nullable{System.Double})">
            <summary>
            Agrega una instacia de un objeto como mensaje al Queue
            </summary>
            <typeparam name="T">tipo del Objeto a agregar</typeparam>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje para agregar</param>
            <param name="invisibleTimeSeconds"></param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.DeleteMessageFromObject``1(System.String,``0)">
            <summary>
            Borra un mensaje de la Queue
            </summary>
            <param name="queueName">Nombre de la Queue</param>
            <param name="message">Objeto que se tomara como mensaje para borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.DeleteMessage(System.String,System.String,System.String)">
            <summary>
            Borra un mensaje de la Queue
            </summary>
            <param name="queueName">Nombre de la Queue</param>
            <param name="id">Id del mensaje a borrar</param>
            <param name="popReceipt">Pop Receipt del mensaje a borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.DeleteMessage(System.String,Microsoft.WindowsAzure.Storage.Queue.CloudQueueMessage)">
            <summary>
            Borra un mensaje del Queue
            </summary>
            <param name="queueName">Nombre del Queue</param>
            <param name="message">Mensaje a borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.DeleteQueue(System.String)">
            <summary>
            Borra un Queue del Queue Storage
            </summary>
            <param name="tipoQueue">Nombre de la cola a borrar</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Queue.QueueStorageContext.CleanQueue(System.String)">
            <summary>
            Elimina todos los mensajes de la Queue
            </summary>
            <param name="tipoQueue">Nombre del Queue a limpiar</param>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.CloudTableClient">
            <summary>
            Cliente para conectarse al Table Storage
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.Entity">
            <summary>
            Obtiene un IQueryable de la instancia
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.AddOrReplace(`0,Microsoft.WindowsAzure.Storage.Table.CloudTable)">
            <summary>
            Agrega o remplaza un obj en la tabla
            </summary>
            <param name="entity">Objeto a agregar en la tabla del Table Storage</param>
            <param name="ct">Contexto sobre el que se agregará la entidad</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.CreateTableIfNotExist">
            <summary>
            Crea una tabla en el Table Storage si no existe
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.DeleteEntities(System.Collections.Generic.List{`0},System.Int32)">
            <summary>
            Borra las lista de entidades de la tabla
            </summary>
            <param name="entities">Lista de entidades a borrar</param>
            <param name="parallelism">Grado maximo de paralelismo</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.DeleteEntities(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
            Borra las entidades proporcionadas de la tabla de acuerdo a lo encontrado con el predicado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <param name="parallelism">Numero de grados de paralelismo</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.DeleteEntity(`0)">
            <summary>
            Borra un registro(entidad) de la tabla del Table Storage
            </summary>
            <param name="entity">Entidad a borrar del Table Storage</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.DeleteTable">
            <summary>
            Borra la tabla en el Table Storage si existe
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.GetEntities(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <returns>Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.GetEntities(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
            Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <param name="top">numero de elementos a obtener</param>
            <returns>Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.GetEntity(`0)">
            <summary>
            Obtiene una entidad del Table Storage del tipo especificado
            </summary>
            <param name="entity">Objeto que hereda de TableEntity</param>
            <returns>Obtiene un objeto casteados al tipo proporcionado que hereda de TableEntity</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.GetEntity(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Obtiene una entidad del tipo especificado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <returns>el registro encontrado casteado al tipo especificado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.InsertEntities(System.Collections.Generic.IEnumerable{`0},System.Action)">
            <summary>
            Inserta entidades a la tabla del Table Storage
            </summary>
            <param name="entities">lista con las entidades a insertar</param>
            <param name="callbackAfterSave">Callback a ejecutarse despues de la insercion</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.InsertEntity(`0)">
            <summary>
            Inserta una entidad a la tabla del Table Storage
            </summary>
            <param name="entity">Entidad que se insertara en la tabla</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.UpdateEntities(System.Collections.Generic.IEnumerable{`0},System.Action)">
            <summary>
            Actualiza registros en la tabla del Table Storage 
            </summary>
            <param name="entities">Entidades que se actualizaran</param>
            <param name="callbackAfterUpdate">Callback que se ejecutara despues de la actualizacion</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.ITableStorageContext`1.UpdateEntity(`0)">
            <summary>
            Actualiza un registro (entidad) en la tabla del Table Storage
            </summary>
            <param name="entity">Entidad a actualizar del Table Storage</param>
        </member>
        <member name="T:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1">
            <summary>
            Modela la logica de acceso y mantenimiento al table storage
            </summary>
            <typeparam name="T">Especifica el tipo de comportamiento del table storage</typeparam>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1._cloudTableClient">
            <summary>
            Cliente para conectarse al Table Storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1._connectionString">
            <summary>
            Cadena de conexión para acceder a la tabla
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1._retries">
            <summary>
            Numeros de reintentos maximos para cada operacion
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1._storageCredentials">
            <summary>
            Credenciales para acceder al blob storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1._tableName">
            <summary>
            Nombre de la tabla en el Table Storage
            </summary>
        </member>
        <member name="F:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1._timeToWait">
            <summary>
            Intervalo de tiempo entre cada operacion
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.#ctor(System.String,System.Int32,System.TimeSpan)">
            <summary>
            Crea e inicializa una instancia de tipo TableStorageContext 
            </summary>
            <param name="connectionString">Cadena de conexion para crear el  CloudStorageAccount</param>
            <param name="retries">Número de reintentos para cada operación</param>
            <param name="timeToWait">Tiempo de espera entre cada reintento</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.#ctor(System.String,System.Int32,System.TimeSpan,System.String)">
            <summary>
            Crea e inicializa una instancia de tipo TableStorageContext 
            </summary>
            <param name="connectionString">Cadena de conexion para crear el  CloudStorageAccount</param>
            <param name="retries">Número de reintentos para cada operación</param>
            <param name="timeToWait">Tiempo de espera entre cada reintento</param>
            <param name="tableName">Nombre de la tabla en el Table Storage</param>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.CloudTableClient">
            <summary>
            Cliente para conectarse al Table Storage
            </summary>
        </member>
        <member name="P:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.Entity">
            <summary>
            Obtiene un IQueryable de la instancia
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.CreateTableIfNotExist">
            <summary>
            Crea una tabla en el Table Storage si no existe
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.DeleteTable">
            <summary>
            Borra la tabla en el Table Storage si existe
            </summary>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.GetEntities(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <returns>Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.GetEntities(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
            Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <param name="top">numero de elementos a obtener</param>
            <returns>Obtiene IEnumerable con los registros de la tabla casteados al tipo proporcionado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.GetEntity(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Obtiene una entidad del tipo especificado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <returns>el registro encontrado casteado al tipo especificado</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.GetEntity(`0)">
            <summary>
            Obtiene una entidad del Table Storage del tipo especificado
            </summary>
            <param name="entity">Objeto que hereda de TableEntity</param>
            <returns>Obtiene un objeto casteados al tipo proporcionado que hereda de TableEntity</returns>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.InsertEntity(`0)">
            <summary>
            Inserta una entidad a la tabla del Table Storage
            </summary>
            <param name="entity">Entidad que se insertara en la tabla</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.UpdateEntities(System.Collections.Generic.IEnumerable{`0},System.Action)">
            <summary>
            Actualiza registros en la tabla del Table Storage 
            </summary>
            <param name="entities">Entidades que se actualizaran</param>
            <param name="callbackAfterUpdate">Callback que se ejecutara despues de la actualizacion</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.InsertEntities(System.Collections.Generic.IEnumerable{`0},System.Action)">
            <summary>
            Inserta entidades a la tabla del Table Storage
            </summary>
            <param name="entities">lista con las entidades a insertar</param>
            <param name="callbackAfterSave">Callback a ejecutarse despues de la insercion</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.AddOrReplace(`0,Microsoft.WindowsAzure.Storage.Table.CloudTable)">
            <summary>
            Agrega o remplaza un obj en la tabla
            </summary>
            <param name="entity">Objeto a agregar en la tabla del Table Storage</param>
            <param name="ct">Contexto sobre el que se agregará la entidad</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.DeleteEntities(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Int32)">
            <summary>
            Borra las entidades proporcionadas de la tabla de acuerdo a lo encontrado con el predicado
            </summary>
            <param name="predicado">Predicado usado en la clausula where en la obtencion de registros</param>
            <param name="parallelism">Numero de grados de paralelismo</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.DeleteEntities(System.Collections.Generic.List{`0},System.Int32)">
            <summary>
            Borra las lista de entidades de la tabla
            </summary>
            <param name="entities">Lista de entidades a borrar</param>
            <param name="parallelism">Grado maximo de paralelismo</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.UpdateEntity(`0)">
            <summary>
            Actualiza un registro (entidad) en la tabla del Table Storage
            </summary>
            <param name="entity">Entidad a actualizar del Table Storage</param>
        </member>
        <member name="M:RUV.Comun.Servicios.Storage.Table.TableStorageContext`1.DeleteEntity(`0)">
            <summary>
            Borra un registro(entidad) de la tabla del Table Storage
            </summary>
            <param name="entity">Entidad a borrar del Table Storage</param>
        </member>
    </members>
</doc>
