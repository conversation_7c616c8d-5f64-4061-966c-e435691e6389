<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.Comun.Utilerias</name>
    </assembly>
    <members>
        <member name="F:RUV.Comun.Utilerias.AyudanteReintentos.NumeroIntentosPorDefecto">
            <summary>
            Número de intentos por defecto.
            </summary>
        </member>
        <member name="F:RUV.Comun.Utilerias.AyudanteReintentos.EsperaMaximaMilisegundos">
            <summary>
            Tiempo máximo a esperar en milisegundos en caso de una falla.
            </summary>
        </member>
        <member name="F:RUV.Comun.Utilerias.AyudanteReintentos.IncrementoEsperaMilisegundos">
            <summary>
            Incremento al tiempo de espera en milisegundos en caso de una falla.
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AyudanteReintentos.NumeroReintentos">
            <summary>
            Numero de intentos a realizar.
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.AyudanteReintentos.#ctor">
            <summary>
            Constructor de la clase.
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.AyudanteReintentos.#ctor(System.Int32)">
            <summary>
            Constructor de la clase.
            </summary>
            <param name="numeroIntentos">Número de intentos del ayudante.</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.AyudanteReintentos.EjecutarConReintentos(System.Action,System.Func{System.Exception,System.Boolean})">
            <summary>
            Ejecuta una acción reintentandola en caso de una excepción no controlada.
            </summary>
            <param name="funcion">Acción a ejecutar.</param>
            <param name="callbackExcepcion">Función que recibe la excepción generada, en caso de que se dé, e indica si reintentar o no.</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.AyudanteReintentos.EjecutarConReintentos``1(System.Func{``0},System.Func{System.Exception,System.Boolean})">
            <summary>
            Ejecuta una acción reintentandola en caso de una excepción no controlada.
            </summary>
            <typeparam name="R">Tipo del resultado de la acción.</typeparam>
            <param name="funcion">Acción a ejecutar.</param>
            <param name="callbackExcepcion">Función que recibe la excepción generada, en caso de que se dé, e indica si reintentar o no.</param>
            <returns>Resultado de la acción.</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.AyudanteReintentos.EjecutarConReintentosAsync(System.Func{System.Threading.Tasks.Task},System.Func{System.Exception,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Ejecuta una acción reintentandola en caso de una excepción no controlada.
            </summary>
            <param name="funcion">Acción a ejecutar.</param>
            <param name="callbackExcepcion">Función que recibe la excepción generada, en caso de que se dé, e indica si reintentar o no.</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.AyudanteReintentos.EjecutarConReintentosAsync``1(System.Func{System.Threading.Tasks.Task{``0}},System.Func{System.Exception,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Ejecuta una acción reintentandola en caso de una excepción no controlada.
            </summary>
            <typeparam name="R">Tipo del resultado de la acción.</typeparam>
            <param name="funcion">Acción a ejecutar.</param>
            <param name="callbackExcepcion">Función que recibe la excepción generada, en caso de que se dé, e indica si reintentar o no.</param>
            <returns>Resultado de la acción.</returns>
        </member>
        <member name="F:RUV.Comun.Utilerias.AzureDateTime.SystemTimeZoneId">
            <summary>
            Especifica la zona horaria
            </summary>
        </member>
        <member name="F:RUV.Comun.Utilerias.AzureDateTime.Culture">
            <summary>
            Especifica el Culture para la conversion de las fechas
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AzureDateTime.Now">
            <summary>
            Obtiene convertida la fecha(azure) para la zona horaria y la cultura especificada
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AzureDateTime.NowWithoutTime">
            <summary>
            Obtiene la propiedad date la fecha(azure) para la zona horaria y la cultura especificada
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.AzureDateTime.FromString(System.String)">
            <summary>
            Obtiene convertida la fecha(azure) para la zona horaria y la cultura especificada apartir de una cadena
            </summary>
            <param name="selected"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.Comun.Utilerias.ClienteHttpRuv">
            <summary>
            Clase que contiene metodos para realizar solicitudes HTTP a servidores del RUV que requieren seguridad.
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.ClienteHttpRuv.Post(System.String,System.String,System.Object)">
            <summary>
            Permite realizar peticiones tipo post respecto a la url especificada.
            </summary>
            <param name="token">Token del usuario autenticado</param>
            <param name="url">Url hacia donde realizar la peticion</param>
            <param name="body">Contenido enviado dentro de la peticion</param>
            <returns>Valor booleano que indica el resultado de la peticion</returns>
        </member>
        <member name="T:RUV.Comun.Utilerias.Contrasena">
            <summary>
            Clase que permite la generación de una contraseña. 
            La contraseña contiene un número de caracteres fijos y permite especificar el porcentaje
            de caracteres en mayúsculas y símbolos que se quieren obtener
            </summary>
        </member>
        <member name="T:RUV.Comun.Utilerias.Contrasena.tipoCaracterEnum">
            <summary>
            Enumeración que permite conocer el tipo de juego de carácteres a emplear
            para cada carácter
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.Contrasena.LongitudPassword">
            <summary>
            Obtiene o establece la longitud en carácteres de la contraseña a obtener
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.Contrasena.PorcentajeMayusculas">
            <summary>
            Obtiene o establece el porcentaje de carácteres en mayúsculas que 
            contendrá la contraseña
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Se produce al intentar introducir
            un valor que no coincida con un porcentaje</exception>
        </member>
        <member name="P:RUV.Comun.Utilerias.Contrasena.PorcentajeSimbolos">
            <summary>
            Obtiene o establece el porcentaje de símbolos que contendrá la contraseña
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Se produce al intentar introducir
            un valor que no coincida con un porcentaje</exception>
        </member>
        <member name="P:RUV.Comun.Utilerias.Contrasena.PorcentajeNumeros">
            <summary>
            Obtiene o establece el número de caracteres numéricos que contendrá la contraseña
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Se produce al intentar introducir
            un valor que no coincida con un porcentaje</exception>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.#ctor">
            <summary>
            Constructor. La contraseña tendrá 10 caracteres, incluyendo una letra mayúscula, 
            un número y un símbolo
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.#ctor(System.Int32)">
            <summary>
            Constructor. La contraseña tendrá un 20% de caracteres en mayúsculas y otro tanto de 
            símbolos
            </summary>
            <param name="longitudCaracteres">Longitud en carácteres de la contraseña a obtener</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Se produce al intentar introducir
            un porcentaje de caracteres especiales mayor de 100</exception>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Constructor
            </summary>
            <param name="longitudCaracteres">Longitud en carácteres de la contraseña a obtener</param>
            <param name="porcentajeMayusculas">Porcentaje a aplicar de caracteres en mayúscula</param>
            <param name="porcentajeSimbolos">Porcenta a aplicar de símbolos</param>
            <param name="porcentajeNumeros">Porcentaje de caracteres numéricos</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Se produce al intentar introducir
            un porcentaje de caracteres especiales mayor de 100</exception>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.ObtenerNuevoPassword">
            <summary>
            Obtiene el password
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.SetCaracteresEspeciales(System.Int32,System.Int32,System.Int32)">
            <summary>
            Permite establecer el número de caracteres especiales que se quieren obtener
            </summary>
            <param name="numeroCaracteresMayuscula">Número de caracteres en mayúscula</param>
            <param name="numeroCaracteresNumericos">Número de caracteres numéricos</param>
            <param name="numeroCaracteresSimbolos">Número de caracteres de símbolos</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.ObtenerContrasena">
            <summary>
            Constructor. La contraseña tendrá 8 caracteres, incluyendo una letra mayúscula, 
            un número y un símbolo
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.GeneraPassword">
            <summary>
            Método que genera el password. Primero crea una cadena de caracteres 
            en minúscula y va sustituyendo los caracteres especiales
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.ReemplazaCaracteresEspeciales(System.Int32[],System.Int32,System.Int32,RUV.Comun.Utilerias.Contrasena.tipoCaracterEnum)">
            <summary>
            Reemplaza un caracter especial en la cadena Password
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.GetPosicionesCaracteresEspeciales(System.Int32)">
            <summary>
            Obtiene un array con las posiciones en las que deberán colocarse los caracteres
            especiales (Mayúsculas o Símbolos). Es importante que no se repitan los números
            de posición para poder mantener el porcentaje de dichos carácteres
            </summary>
            <param name="numeroPosiciones">Valor que representa el número de posiciones
            que deberán crearse sin repetir</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.GetCaracterAleatorio(RUV.Comun.Utilerias.Contrasena.tipoCaracterEnum)">
            <summary>
            Obtiene un carácter aleatorio en base a la "matriz" del tipo de caracteres
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.CrearSalero(System.String)">
            <summary>
            Obtiene una cadena salero a partir del nombre del usuario
            </summary>
            <param name="nombreUsuario">nombre de usuario Login</param>
            <returns>salero</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.hashSaleroContrasena(System.String,System.String)">
            <summary>
            Obtencion de la huella de la contrasena usando el salero
            </summary>
            <param name="salero"></param>
            <param name="contrasena"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Contrasena.ObtenerHashContrasena(System.String)">
            <summary>
            Devuelve el Hash de la contrasena a partir de un salero constante
            </summary>
            <param name="contrasena">contrasena de usuario </param>
            <returns>La huella de la Contrasena</returns>
        </member>
        <member name="T:RUV.Comun.Utilerias.CriptografiaRuvAsis">
            <summary>
            Clase para encriptar y desencriptar usando el alogoritmo de RUV (Migrado desde Java)
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.CriptografiaRuvAsis.Encriptar(System.String)">
            <summary>
            Encripta un objeto String
            </summary>
            <param name="original">String original</param>
            <returns>String encriptado</returns>
            <exception cref="T:System.ArgumentNullException">Se lanza cuando el String original es nulo o vacío.</exception>
        </member>
        <member name="M:RUV.Comun.Utilerias.CriptografiaRuvAsis.Desencriptar(System.String)">
            <summary>
            Decrypt a crypted string.
            </summary>
            <param name="cryptedString">The crypted string.</param>
            <returns>The decrypted string.</returns>
            <exception cref="T:System.ArgumentNullException">This exception will be thrown when the crypted string is null or empty.</exception>
        </member>
        <member name="M:RUV.Comun.Utilerias.CriptografiaRuvAsis.Cifrar(System.String)">
            <summary>
            Cifra un String para encriptarlo posteriormente
            </summary>
            <param name="sTextoClaro">String original</param>
            <returns>String cifrado</returns>
        </member>
        <member name="T:RUV.Comun.Utilerias.DescriptionHelperExtension">
            <summary>
            Clase para el manejo de atributos de Enums
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.DescriptionHelperExtension.ToDescription(System.Enum)">
            <summary>
            Metodo para obtener el valor del decorador Description
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.EnumerationExtensions.Has``1(System.Enum,``0)">
            <summary>
            comprueba si el valor contiene el tipo proporcionado
            </summary>
            <typeparam name="T">Tipo generico</typeparam>
            <param name="type">Tipo a buscar</param>
            <param name="value">objeto del tipo proporcionado</param>
            <returns>verdadero o falso, segun sea el caso</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.EnumerationExtensions.Is``1(System.Enum,``0)">
            <summary>
            Comprueba si el valor es del tipo especificado
            </summary>
            <typeparam name="T">Tipo generico</typeparam>
            <param name="type">Tipo a evaluar</param>
            <param name="value">Objeto del tipo propocionado</param>
            <returns>Verdadero o falso, segun sea el caso</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.EnumerationExtensions.GetDescription(System.Enum)">
            <summary>
            Permite recuperar la descripcion de un enum especificado en el atributo <see cref="T:System.ComponentModel.DescriptionAttribute"/>
            </summary>
            <param name="enumType"></param>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">Si el enum no tiene especificado el atributo.</exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:RUV.Comun.Utilerias.Extensions.IDictionaryExtensions.GetEntry``2(System.Collections.Generic.IDictionary{``0,``1},``0)" -->
        <member name="T:RUV.Comun.Utilerias.Extensions.TelemetryClientExtensions">
            <summary>
            Contiene métodos de extensión para el cliente de Application Insights.
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.TelemetryClientExtensions.MedirDependencia(Microsoft.ApplicationInsights.TelemetryClient,System.String,System.String,System.Action,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Permite ejecutar una dependencia midiendo su duración y registrandola en Application Insights.
            </summary>
            <param name="cliente">Cliente de telemetría.</param>
            <param name="nombreDependencia">Nombre de la dependencia.</param>
            <param name="nombreComando">Nombre del comando ejecutado.</param>
            <param name="accion">Acción de la dependencia.</param>       
            <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
            <param name="codigoResultado">Resultado del comando.</param>
            <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.TelemetryClientExtensions.MedirDependencia``1(Microsoft.ApplicationInsights.TelemetryClient,System.String,System.String,System.Func{``0},System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Permite ejecutar una dependencia midiendo su duración y registrandola en Application Insights.
            </summary>
            <typeparam name="R">Tipo del resultado.</typeparam>
            <param name="cliente">Cliente de telemetría.</param>
            <param name="nombreDependencia">Nombre de la dependencia.</param>
            <param name="nombreComando">Nombre del comando ejecutado.</param>
            <param name="accion">Acción de la dependencia.</param>
            <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
            <param name="codigoResultado">Resultado del comando.</param>
            <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
            <returns>Resultado de la dependencia.</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.TelemetryClientExtensions.MedirDependenciaAsync(Microsoft.ApplicationInsights.TelemetryClient,System.String,System.String,System.Func{System.Threading.Tasks.Task},System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Permite ejecutar una dependencia asincrona midiendo su duración y registrandola en Application Insights.
            </summary>
            <typeparam name="R">Tipo del resultado.</typeparam>
            <param name="cliente">Cliente de telemetría.</param>
            <param name="nombreDependencia">Nombre de la dependencia.</param>
            <param name="nombreComando">Nombre del comando ejecutado.</param>
            <param name="accion">Acción de la dependencia.</param>
            <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
            <param name="codigoResultado">Resultado del comando.</param>
            <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
            <returns>Resultado de la dependencia.</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.TelemetryClientExtensions.MedirDependenciaAsync``1(Microsoft.ApplicationInsights.TelemetryClient,System.String,System.String,System.Func{System.Threading.Tasks.Task{``0}},System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Permite ejecutar una dependencia asincrona midiendo su duración y registrandola en Application Insights.
            </summary>
            <typeparam name="R">Tipo del resultado.</typeparam>
            <param name="cliente">Cliente de telemetría.</param>
            <param name="nombreDependencia">Nombre de la dependencia.</param>
            <param name="nombreComando">Nombre del comando ejecutado.</param>
            <param name="accion">Acción de la dependencia.</param>
            <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
            <param name="codigoResultado">Resultado del comando.</param>
            <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
            <returns>Resultado de la dependencia.</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Extensions.TelemetryClientExtensions.GenerarTelemetriaDeDependencia(System.String,System.String,System.DateTime,System.TimeSpan,System.Boolean,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Genera la información de telemetría de una dependencia.
            </summary>
            <param name="nombreDependencia">Nombre de la dependencia.</param>
            <param name="nombreComando">Nombre del comando ejecutado.</param>
            <param name="horaInicio">Hora de inicio del comando.</param>
            <param name="duracion">Duración del comando.</param>
            <param name="exito">Tuvo exito el comando.</param>
            <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
            <param name="codigoResultado">Resultado del comando.</param>
            <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
            <returns>Información de telemetría.</returns>
        </member>
        <member name="F:RUV.Comun.Utilerias.EncodeString._codificador">
            <summary>
            Instancia de objetoi para realizar la codificación.
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.EncodeString.NewRNGCGuid(System.Int32)">
            <summary>
            Genera un string a través de un aleatorio codificado
            </summary>
            <param name="size">Define el tamaño del buffer</param>
            <returns>Devuelve el string codificado en base 64</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.EncodeString.CreateEncodingString(System.String,System.String)">
            <summary>
            Genera una contraseña en base 64
            </summary>
            <param name="plainText">Texto que se codificará en Base 64</param>
            <param name="RNGCGuid">String aleatorio</param>
            <returns>Devuelve un string de base 64 de la cadena resultante de concatenar los dos parámetros</returns>
        </member>
        <member name="T:RUV.Comun.Utilerias.InjectionConstructorRelaxed">
            <summary>
            A class that holds the collection of information for a constructor, 
            so that the container can be configured to call this constructor.
            This Class is similar to InjectionConstructor, but you need not provide
            all Parameters, just the ones you want to override or which cannot be resolved automatically
            The given params are used in given order if type matches
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.InjectionConstructorRelaxed.#ctor(System.Object[])">
            <summary>
            Create a new instance of <see cref="T:Microsoft.Practices.Unity.InjectionConstructor"/> that looks
            for a constructor with the given set of parameters.
            </summary>
            <param name="parameterValues">The values for the parameters, that will
            be converted to <see cref="T:Microsoft.Practices.Unity.InjectionParameterValue"/> objects.</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.InjectionConstructorRelaxed.AddPolicies(System.Type,System.Type,System.String,Microsoft.Practices.ObjectBuilder2.IPolicyList)">
            <summary>
            Add policies to the <paramref name="policies"/> to configure the
            container to call this constructor with the appropriate parameter values.
            </summary>
            <param name="serviceType">Interface registered, ignored in this implementation.</param>
            <param name="implementationType">Type to register.</param>
            <param name="name">Name used to resolve the type object.</param>
            <param name="policies">Policy list to add policies to.</param>
        </member>
        <member name="M:RUV.Comun.Utilerias.InjectionConstructorRelaxed.ConstructorLengthComparer.Compare(System.Reflection.ConstructorInfo,System.Reflection.ConstructorInfo)">
            <summary>
            Compares two objects and returns a value indicating whether one is less than, equal to, or greater than the other.
            </summary>
            <param name="y">The second object to compare.</param>
            <param name="x">The first object to compare.</param>
            <returns>
            Value Condition Less than zero is less than y. Zero equals y. Greater than zero is greater than y.
            </returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Numerics.NumerosCardinales.ConvertirNumeroALetra(System.Decimal)">
            <summary>
            Convierte un numero de tipo <code>decimal</code> a su representacion en numeros cardinales.
            El numero maximo a convertir es 999999999999999999.99M
            </summary>
            <param name="numeroALetra">Numero a convertir</param>
            <returns>
            Regresa un <code>string</code> vacio si no pudo hacer la conversion de lo contrario regresa el numero cardinal
            </returns>
        </member>
        <member name="T:RUV.Comun.Utilerias.ResultadoPaginado`1">
            <summary>
            Clase que representa el resultado de una consulta paginada.
            </summary>
            <typeparam name="T">Tipo del resultado.</typeparam>
        </member>
        <member name="P:RUV.Comun.Utilerias.ResultadoPaginado`1.TotalRegistros">
            <summary>
            Total de registros existentes.
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.ResultadoPaginado`1.TamanioPagina">
            <summary>
            Tamaño de la pagina solicitada.
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.ResultadoPaginado`1.PaginaActual">
            <summary>
            Pagina solicitada.
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.ResultadoPaginado`1.TotalPaginas">
            <summary>
            Total de páginas posibles con base en el tamaño de pagina.
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.ResultadoPaginado`1.Resultado">
            <summary>
            Resultado de la consulta en la pagina actual
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.Serializer.ToByteArray``1(``0)">
            <summary>
            Serealiza un objeto a un arreglo de bytes
            </summary>
            <typeparam name="T"></typeparam>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Serializer.BinarySerialize``1(``0)">
            <summary>
            Serealiza un objet del tipo generico especificado a un MemoryStream
            </summary>
            <typeparam name="T"></typeparam>
            <param name="objeto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Serializer.BinaryDeserialize``1(System.Byte[])">
            <summary>
            Deserealiza un MemoryStream al tipo generico especificado
            </summary>
            <typeparam name="T"></typeparam>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Serializer.deserializeBlobXML``1(System.String)">
            <summary>
            convierte un xml al tipo generico especificado
            </summary>
            <typeparam name="T">Tipo Generico</typeparam>
            <param name="xml">xml a convertir</param>
            <returns>EL xml convertido al tipo</returns>
        </member>
        <member name="M:RUV.Comun.Utilerias.Serializer.serializeToXML``1(``0)">
            <summary>
            Serealiza un Objeto a un xml
            </summary>
            <typeparam name="T"></typeparam>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="F:RUV.Comun.Utilerias.AzureSmallDateTime.SystemTimeZoneId">
            <summary>
            Especifica la zona horaria
            </summary>
        </member>
        <member name="F:RUV.Comun.Utilerias.AzureSmallDateTime.Culture">
            <summary>
            Especifica el Culture para la conversion de las fechas
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AzureSmallDateTime.MinValue">
            <summary>
            Valor minimo para una fecha tipo SmallDateTime en Sql Server
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AzureSmallDateTime.MaxValue">
            <summary>
            Valor maximo para una fecha tipo SmallDateTime en Sql Server
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AzureSmallDateTime.Now">
            <summary>
            Obtiene convertida la fecha(azure) para la zona horaria y la cultura especificada
            </summary>
        </member>
        <member name="P:RUV.Comun.Utilerias.AzureSmallDateTime.NowWithoutTime">
            <summary>
            Obtiene la propiedad date la fecha(azure) para la zona horaria y la cultura especificada
            </summary>
        </member>
        <member name="M:RUV.Comun.Utilerias.AzureSmallDateTime.FromString(System.String)">
            <summary>
            Obtiene convertida la fecha(azure) para la zona horaria y la cultura especificada apartir de una cadena
            </summary>
            <param name="selected"></param>
            <returns></returns>
        </member>
    </members>
</doc>
