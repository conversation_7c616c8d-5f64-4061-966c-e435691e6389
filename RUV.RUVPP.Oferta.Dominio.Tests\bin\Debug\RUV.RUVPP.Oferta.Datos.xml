<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Oferta.Datos</name>
    </assembly>
    <members>
        <member name="T:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.IAgenteServicioConvivenciaASIS">
            <summary>
            Contiene operaciones para integrar los servicios web de convivienca de RUV As Is.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.IAgenteServicioConvivenciaASIS.LlamarOperacion``2(``0,System.String)">
            <summary>
            Realiza una llamada genérica al servicio web del RUV AS IS.
            </summary>
            <typeparam name="M">Tipo del modelo a enviar.</typeparam>
            <typeparam name="R">Tipo del resultado.</typeparam>
            <param name="modelo">Modelo a enviar en la petición.</param>
            <param name="urlAccion">Url de la acción a ejecutar.</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.Implementacion.AgenteServicioConvivenciaASIS">
            <inheritdoc />
        </member>
        <member name="F:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.Implementacion.AgenteServicioConvivenciaASIS._clienteTelemetria">
            <summary>
            CLiente de telemetría.
            </summary>
        </member>
        <member name="F:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.Implementacion.AgenteServicioConvivenciaASIS._urlBase">
            <summary>
            URL base del servicio.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.Implementacion.AgenteServicioConvivenciaASIS.#ctor(System.String)">
            <summary>
            Constructor de la clase.
            </summary>
            <param name="urlBase">Url base del servicio.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.Implementacion.AgenteServicioConvivenciaASIS.LlamarOperacion``2(``0,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.Implementacion.DictaminacionDataMapper.GuardarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Guarda el JSON de dictaminacion para un registro y orden de trabajo determinados
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.Implementacion.DictaminacionDataMapper.ActualizarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Actualiza el JSON de dictaminacion de un registro para una ODT determinada
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.Implementacion.DictaminacionDataMapper.ObtenerDictaminacionAsync(System.Int16,System.Int32)">
            <summary>
            Obtiene el ultimo JSON de dictaminacion para un registro determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.Implementacion.DictaminacionDataMapper.ObtenerDictaminacionPorOrdenTrabajoAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de dictaminacion de un registro para una ODT determinada
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.Implementacion.DictaminacionDataMapper.ObtenerUltimaDictaminacionOAnteriorAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de dictaminacion de la una oferta para una ODT determinada, si no la encuentra, regresa el JSON de la ultima ODT de ese registro.
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la ODT</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.IDictaminacionDataMapper.GuardarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Guarda el JSON de dictaminacion para un registro y orden de trabajo determinados
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.IDictaminacionDataMapper.ActualizarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Actualiza el JSON de dictaminacion de un registro para una ODT determinada
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.IDictaminacionDataMapper.ObtenerDictaminacionAsync(System.Int16,System.Int32)">
            <summary>
            Obtiene el ultimo JSON de dictaminacion para un registro determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.IDictaminacionDataMapper.ObtenerUltimaDictaminacionOAnteriorAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de dictaminacion de la una oferta para una ODT determinada, si no la encuentra, regresa el JSON de la ultima ODT de ese registro.
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la ODT</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Dictaminacion.IDictaminacionDataMapper.ObtenerDictaminacionPorOrdenTrabajoAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de dictaminacion de un registro para una ODT determinada
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarHistoricoVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusViviendas)">
            <summary>
            
            </summary>
            <param name="estatusVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarHistoricoOfertaVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusOfertaVivienda)">
            <summary>
            
            </summary>
            <param name="estatusOfertaVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarHistoricoProyecto(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusProyectos)">
            <summary>
            
            </summary>
            <param name="estatusProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarDetalleHistoricoOfertaVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusOfertaVivienda,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="estatusOfertaVivienda"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarAdicionalHistorico(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarDetalleHistoricoProyecto(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusProyectos,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="estatusProyecto"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.GuardarDetalleHistoricoVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusViviendas,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.DetalleEstatusVivienda})">
            <summary>
            
            </summary>
            <param name="estatusVivienda"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.ObtenerHistoricoCuvPorIdViviendaAsync(System.Int32)">
            <summary>
            Obtiene el historico de CUVs por IdVivienda
            </summary>
            <param name="idVivienda">Identificador de la vivienda</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.InsertarDocumentoPorEventoVivienda(System.Int32,System.Int32)">
            <summary>
            Inserta datos en la tabla de Documento Por Evento Vivienda en la base.
            </summary>
            <param name="idEventoVivienda">Identificador del Evento de Vivienda</param>
            <param name="idDocumento">Identificador del Documento</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper.ObtenerDocumentosPorEventoVivienda(System.Int32)">
            <summary>
            Obtiene los documentos asociados a un Evento de Vivienda.
            </summary>
            <param name="idEventoVivienda">Identificador del Evento de Vivienda</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Historico.Implementacion.HistoricosDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
            <param name="gestorConexiones"></param>
            <param name="nombreCadenaConexion"></param>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper">
            <summary>
            Data Mapper de mediciones
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerCuvsAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS
            </summary>
            <param name="ClaveOferta">Clave de oferta</param>
            <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerCuvsPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS con paginación
            </summary>
            <param name="ClaveOferta">Identificador de oferta</param>
            <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
            <param name="pagina">pagina consultada</param>
            <param name="tamanioPagina">tamaño de pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerDatosCuvsAsync(System.String,System.String,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv})">
            <summary>
            Obtiene datos de las cuvs especificadas para la oferta y/o orden de verificacion desde el ASIS
            </summary>       
            <param name="ClaveOferta">Identificador de oferta</param>
            <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
            <param name="filtroCuvs">Lista de cuvs a buscar</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerEcotecnologiasXCuvsAsync(System.Int32,System.Int32,System.String)">
            <summary>
            Obtiene la lista paginada de ecotecnologias de una cuv.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv">clave de la cuv</param>
            <returns>Lista con ecotecnologias de la cuv</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerAtributosXCuvsAsync(System.Int32,System.Int32,System.String)">
            <summary>
            Obtiene la lista paginada de atributos de una cuv.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv">clave de la cuv</param>
            <returns>Lista con atributos de la cuv</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerEquipamientoPorOrden(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerEquipamientoPorOferta(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerViviendasPorEquipamiento(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="atributo"></param>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerEquipamientoPorVivienda(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper.ObtenerDatosCuvsAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuvs"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion.MedicionesDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion.MedicionesDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la Clase.
            </summary>
            <param name="clienteTelemetria">Cliente de Telemetri para Application Insights.</param>
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
            <param name="nombreCadenaConexion">Cadena de Conexión a Base de Datos.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion.MedicionesDataMapper.ObtenerCuvsAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS
            </summary>
            <param name="ClaveOferta">Identificador de oferta</param>
            <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion.MedicionesDataMapper.ObtenerDatosCuvsAsync(System.String,System.String,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv})">
            <summary>
            Obtiene datos de las cuvs especificadas para la oferta y/o orden de verificacion desde el ASIS
            </summary>       
            <param name="ClaveOferta">Identificador de oferta</param>
            <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
            <param name="filtroCuvs">Lista de cuvs a buscar</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion.MedicionesDataMapper.ObtenerCuvsPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS con paginación
            </summary>
            <param name="ClaveOferta">Identificador de oferta</param>
            <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
            <param name="pagina">pagina consultada</param>
            <param name="tamanioPagina">tamaño de pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion.MedicionesDataMapper.ObtenerEquipamientoPorVivienda(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper">
            <summary>
            Clase que contiene métodos para guardar, obtener y actualizar las tablas relacionadas a la administración de oferta.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la Clase.
            </summary>
            <param name="clienteTelemetria">Cliente de Telemetri para Application Insights.</param>
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
            <param name="nombreCadenaConexion">Cadena de Conexión a Base de Datos.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarDirectorResponsableObraxOfertaViviendaAsync(System.Int32,System.Int32)">
            <summary>
            Método que ejecuta el sp "oferta.usp_InsertarDirectorResponsableObraxOfertaVivienda."
            </summary>
            <param name="idDRO">id del Director Responsable de Obra.</param>
            <param name="idOfertaVivienda">id de la Oferta.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerViviendasFiltroAsync(System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="idEmpresa"></param>
            <param name="noRuv"></param>
            <param name="nombreProyecto"></param>
            <param name="idProyecto"></param>
            <param name="idOferta"></param>
            <param name="claveOferta"></param>
            <param name="idVivienda"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarOfertaViviendaxRiesgoOfertaAsync(RUV.RUVPP.Oferta.Modelo.Oferta.Data.ViviendaRiesgoOferta)">
            <summary>
            Método que ejecuta el sp oferta.usp_InsertarOfertaViviendaxRiesgoOferta.
            </summary>
            <param name="viviendaRiesgoOferta">Objeto ViviendaRiesgoOferta a guardar en base de datos.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarPromotorExternoxOfertaViviendaAsync(System.Int32,System.Int32)">
            <summary>
            Método que ejecute el sp oferta.usp_InsertarPromotorExternoxOfertaVivienda
            </summary>
            <param name="idPromotorExterno">id del Promotor Externo</param>
            <param name="idOfertaVivienda">id de la Oferta</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarPropietarioTerrenoxOfertaViviendaAsync(System.Int32,System.Int32)">
            <summary>
            Método que ejecuta el oferta.usp_InsertarPropietarioTerrenoxOfertaVivienda.
            </summary>
            <param name="idPropertarioTerreno">id del Propietario del Terreno.</param>
            <param name="idOfertaVivienda">id de Oferta.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarOfertaViviendaAsync(System.Int32,System.String,System.String)">
            <summary>
            Método que ejecuta el oferta.usp_InsertarOfertaVivienda
            </summary>
            <param name="ofertaVivienda">Objeto OfertaVivienda a guardar en base de datos.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarDocumentoxOfertaViviendaAsync(System.Int32,System.Int32)">
            <summary>
            Método que ejecuta el sp oferta.usp_InsertarDocumentoxPrototipoxOfertaVivienda
            </summary>
            <param name="idOfertaVivienda">id de Oferta Vivienda</param>
            <param name="idDocumento">id Documento</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.GuardarPrototipoxOfertaViviendaAsync(System.Int32,System.Int32)">
            <summary>
            Método que ejecuta el sp oferta.usp_InsertarPrototipoxOfertaVivienda
            </summary>
            <param name="idOfertaVivienda"></param>
            <param name="idPrototipo"></param>
            <returns>id del registro insertado</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerOfertaPorIdAsync(System.Int32)">
             <summary>
            
             </summary>
             <param name="idOferta"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerOfertasFiltradoAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Int32})">
             <summary>
            
             </summary>
             <param name="tamanioPagina"></param>
             <param name="pagina"></param>
             <param name="claveOferta"></param>
             <param name="nombreProyecto"></param>
             <param name="idEmpresa"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerCatalogoDocumentosComplementariosAsync">
             <summary>
            
             </summary>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerVivendasPorIdProyectoAsync(System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para una oferta por idOferta
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta</param>
            <param name="tamanioPagina">Tamaño de pagina</param>
            <param name="pagina">Pagina</param>
            <returns>Lista de ordenes de trabajo de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerCatalogoEstatusViviendaAsync">
            <summary>
            Obtiene el catalogo de estatus de vivienda
            </summary>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.Implementacion.OfertaDataMapper.ObtenerIdsOfertaPorProyectoAsync(System.Int32,System.Int32)">
            <summary>
            Obtiene los ids y clave de oferta con estatus determinado dado un idProyecto
            </summary>
            <param name="idProyecto">Identificador dle proyecto</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerViviendasFiltroAsync(System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="idEmpresa"></param>
            <param name="noRuv"></param>
            <param name="nombreProyecto"></param>
            <param name="idProyecto"></param>
            <param name="idOferta"></param>
            <param name="claveOferta"></param>
            <param name="idVivienda"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerOfertaPorIdAsync(System.Int32)">
             <summary>
            
             </summary>
             <param name="idOferta"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerOfertasFiltradoAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Int32})">
             <summary>
            
             </summary>
             <param name="tamanioPagina"></param>
             <param name="pagina"></param>
             <param name="idOferta"></param>
             <param name="nombreProyecto"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerVivendasPorIdProyectoAsync(System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para una oferta por idOferta
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta</param>
            <param name="tamanioPagina">Tamaño de pagina</param>
            <param name="pagina">Pagina</param>
            <returns>Lista de ordenes de trabajo de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerVivienasPorOfertaPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de viviendas para una oferta por idOferta paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de pagina</param>
            <param name="pagina">Pagina</param>
            <returns>Lista de viviendas de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerCatalogoEstatusViviendaAsync">
            <summary>
            Obtiene el catalogo de estatus de vivienda
            </summary>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerIdsOfertaPorProyectoAsync(System.Int32,System.Int32)">
            <summary>
            Obtiene los ids y clave de oferta con estatus determinado dado un idProyecto
            </summary>
            <param name="idProyecto">Identificador dle proyecto</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerViviendasPorIdOfertaViviendaAsync(System.Int32)">
            <summary>
            Obtiene una lista de tuplas que contienen el idVivienda e idEstatusVivienda a partir de una
            oferta que se adquiere previamente por medio del idVivienda que se manda.
            </summary>
            <param name="idOfertaVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerCUVPorIdViviendaAsync(System.Int32)">
            <summary>
            Obtiene una CUV a partir de un idVivienda.
            </summary>
            <param name="idVivienda">Identificador de la vivienda.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerCUVGeograficaPorIdViviendaAsync(System.Int32)">
            <summary>
            Obtiene una CUV Geográfica a partir de un idVivienda.
            </summary>
            <param name="idVivienda">Identificador de la vivienda.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerIdOfertaPorIdViviendaAsync(System.String)">
            <summary>
            Obtiene una lista de tuplas que contienen el idVivienda y el idOfertaVivienda.
            </summary>
            <param name="idVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerClaveOfertaxCuvAsync(System.String)">
            <summary>
            Obtiene la clave de la oferta de una cuv
            </summary>
            <param name="cuv">clave unica de vivienda</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerDocumentosPorIdentificadores(System.String)">
            <summary>
            Obtiene los documentos asociados a los Identificadores
            que se envíen en forma de cadena de caracteres separados por comas.
            </summary>
            <param name="idDocumentos">Identificadores de los Documentos en cadena de caracteres separados por una coma.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.InsertarValoresDocumentoPorEstatusVivienda(System.Int32,System.Int32,System.String)">
            <summary>
            Inserta valores en la tabla de Documentos por Cambio de Estatus de Vivienda.
            </summary>
            <param name="idDocumento">Identificador del Documento.</param>
            <param name="idVivienda">Identificador de la Vivienda.</param>
            <param name="descripcionArchivo">Descripción del Documento.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.EliminarRegistrosDocumentoPorEventoVivienda(System.Int32)">
            <summary>
            Procedimiento Almacenado para Eliminar un registro de la tabla de Descripciones de Documentos por Evento Vivienda.
            </summary>
            <param name="idDocumento">Identificador del Documento.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerMedioIndividualizacion">
            <summary>
            Método para obtener los medios de individualización junto con un campo extra de Banco.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerBancosIndividualizacion">
            <summary>
            Método para obtener los bancos como medio de individualización.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.ObtenerTipoIndividualizacion(System.Int32)">
            <summary>
            Método para obtener los tipos de individualización de acuerdo a un medio introducido.
            </summary>
            <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.InsertarIndividualizacion(System.Int32,System.Int32,System.Nullable{System.Int32},System.DateTime)">
            <summary>
            Método para insertar valores en la tabla de Individualización de Vivienda.
            </summary>
            <param name="idVivienda">Identificador de la Vivienda.</param>
            <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
            <param name="idTipoIndividualizacion">Identificador del Tipo de Individualización. El dato es opcional.</param>
            <param name="fechaIndividualizacion">Fecha seleccionada.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper.EliminarIndividualizacion(System.Int32)">
            <summary>
            Método para eliminar valores en la tabla de Individualización de Vivienda.
            </summary>
            <param name="idVivienda">Identificador de la Vivienda.</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper">
            <summary>
            Clase que contiene métodos para obtener las tablas relacionadas a las ordenes de verificacion.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la Clase.
            </summary>
            <param name="clienteTelemetria">Cliente de Telemetri para Application Insights.</param>
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
            <param name="nombreCadenaConexion">Cadena de Conexión a Base de Datos.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="idVivienda"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerOrdenesVerificacionFiltroAsync(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <param name="idRUVAsis"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(System.String)">
            <summary>
            Obtiene la ultima orden de verificacion activa
            </summary>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerDetalleOrdenVerificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerOrdenesVerificacionPorClavesOfertaPaginadorAsync(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.Implementacion.OrdenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveCUV"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.ObtenerOrdenesVerificacionFiltroAsync(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="idVivienda"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.ObtenerDetalleOrdenVerificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(System.String)">
            <summary>
            Obtiene la ultima orden de verificacion activa de una oferta vivienda
            </summary>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper.obtenerDetalleViviendasAsis(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda})">
            <summary>
            
            </summary>
            <param name="cuvs"></param>
            <returns></returns>
            
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.Implementacion.PlanoDataMapper.ValidacionProyecto(System.Int32)">
            <summary>
            Método para realizar la validación espacial de un proyecto en la BD temporal.
            </summary>
            <param name="idProyecto">identificador por proyecto.</param>
            <returns>cadena con error si es que los hay.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.GuardarProyectoTemporal(System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            Método para crear proyecto en oracle.
            </summary>
            <param name="idProyecto">Identificador de Proyecto.</param>
            <param name="idUsuario">Identificador de Usuario.</param>
            <param name="claveDesarrollador">Clave de Desarrollador.</param>
            <param name="nomProyecto">Nombre de Proyecto.</param>
            <param name="estatus">Estatus del Proyecto.</param>
            <param name="descEvento">Descripción del evento.</param>
            <returns>true: si se guardó el proyecto | false: si hubo error al guardar el proyecto.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.ValidacionProyecto(System.Int32)">
            <summary>
            Método para realizar la validación espacial de un proyecto en la BD temporal.
            </summary>
            <param name="idProyecto">identificador por proyecto.</param>
            <returns>cadena con error si es que los hay.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.ObtenerCoordenadasOferta(System.Int32)">
            <summary>
            Método para calcular el centroide de una vivienda.
            </summary>
            <param name="featId">Identificador único en bd oracle</param>
            <returns>Objeto Coordenada con latitud y longitud</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.ObtenerViviendasSinMunEdo(System.Int32)">
            <summary>
            Método para obtener la lista de viviendas que estan fuera de México.
            </summary>
            <param name="idProyecto">Identificador de Proyecto.</param>
            <returns>Lista de viviendas fuera de México.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.BloquearVivienda(System.Int32,System.String)">
            <summary>
            Método que bloquea un vivienda en la base de datos Oracle.
            </summary>
            <param name="featId">Identificador de la vivienda en oracle</param>
            <param name="bloqueada">"1" : Bloquea Vivienda | "0" : Vivienda No Bloqueda</param>
            <returns>true: si se actualizo algún registro | false: si ningún registro se actualizo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.BloquearVivienda(System.String,System.String)">
            <summary>
            Método que bloquea un vivienda en la base de datos Oracle.
            </summary>
            <param name="cuvAsis">CUV en ASIS de la vivienda.</param>
            <param name="bloqueada">"1" : Bloquea Vivienda | "0" : Vivienda No Bloqueda</param>
            <returns>true: si se actualizo algún registro | false: si ningún registro se actualizo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.AsignarCUVAsisVivienda(System.Int32,System.String)">
            <summary>
            Método para asignar la CUV generada por el ASIS a una vivienda.
            </summary>
            <param name="featId">Identificador de la vivienda en Oracle.</param>
            <param name="cuvAsis">CUV generada por el ASIS para la vivienda</param>
            <returns>true: si se actualizo algún registro | false: si ningún registro se actualizo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.ActualizarEstatusCUV(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Método para actualizar el estatus de la CUV en Oracle.
            </summary>
            <param name="listaEstatusCUVS">Listado de CUV con su estatus de BLOQUEADA o DESBLOQUEADA</param>
            <returns>true: si se actualizo algún registro | false: si ningún registro se actualizo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.CancelarCUV(System.String)">
            <summary>
            Método para cancelación de CUV
            </summary>
            <param name="cuvGeografica">CUV Geográfica</param>
            <returns>true:si se eliminó la CUV | false: si no se eliminó la CUV</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.ValidarTipologia(System.Int32)">
            <summary>
            Método para validar el prototipo y tipologia de las viviendas del proyecto.
            </summary>
            <param name="idProyecto">identificador de Proyecto.</param>
            <returns>true: Si el proyecto es válido | false: Si el proyecto no es válido.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.EliminarProyecto(System.Int32)">
            <summary>
            Eliminación de proyecto.
            </summary>
            <param name="idProyecto">Identidicador del proyecto.</param>
            <returns>true: Si se elimina el proyecto | false: No se eliminó el proyecto</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.EliminarVivienda(System.Int32)">
            <summary>
            Elimina una vivienda por featId.
            </summary>
            <param name="featId"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.EliminarViviendaTemporal(System.Int32)">
            <summary>
            Elimina una vivienda por featId en la tabla temporal.
            </summary>
            <param name="featId"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.EliminarViviendaTemporal(System.Int32,System.Int32)">
            <summary>
            Elimina una vivienda por featId en la tabla temporal.
            </summary>
            <param name="idVivienda"></param>
            <param name="idProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Plano.IPlanoDataMapper.ActualizarEstatusViviendaXFeatId(System.Int32,System.String)">
            <summary>
            Método para actualizar el estatus de una vivienda por featid
            </summary>
            <param name="featid">featid de la vivienda</param>
            <param name="idEstatusVivienda">string con el estatus de la vivienda</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.ObtenerJSONConfiguracionReglasAsync(System.Int32,System.Int32)">
            <summary>
            Método para obtener los archivos de reglas y configuraciones
            desde la base de datos por medio del ID de servicio.
            </summary>
            <param name="idServicio"></param>
            /// <param name="conReglas">Parámetro que especifica si se traerá
            el contenido de las reglas, en caso contrario regresará NULL</param>
            <returns>Dupla de "string" conteniendo JSON de ambos archivos.
            (Configuraciones, Reglas)</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de la etapa de registro o vyd por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">>Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.ProductoServicio.IProductoServicioDataMapper">
            <summary>
            Interface que incluye métodos relacionados con los archivos
            JSON de configuraciones y reglas.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.IProductoServicioDataMapper.ObtenerJSONConfiguracionReglasAsync(System.Int32,System.Int32)">
            <summary>
            Método para obtener los archivos de reglas y configuraciones
            desde la base de datos por medio del ID de servicio.
            </summary>
            <param name="idServicio"></param>
            /// <param name="conReglas">Parámetro que especifica si se traerá
            el contenido de las reglas, en caso contrario regresará NULL</param>
            <returns>Dupla de "string" conteniendo JSON de ambos archivos.
            (Configuraciones, Reglas)</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.IProductoServicioDataMapper.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de la etapa de registro o vyd por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">>Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.IProductoServicioDataMapper.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.IProductoServicioDataMapper.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.ProductoServicio.IProductoServicioDataMapper.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper__.ObtenerEmpresaRUV">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper__.ObtenerTelefonosEmpresaRUV(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper__.ObtenerCorreosEmpresaRUV(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper__.ObtenerAccionistasEmpresaRUV(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la Clase.
            </summary>
            <param name="clienteTelemetria">Cliente de Telemetri para Application Insights.</param>
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
            <param name="nombreCadenaConexion">Cadena de Conexión a Base de Datos.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerOfertaViviendaPaginado(System.Int32,System.Int32,RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.GuardarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.GuardarPromotorXCobertura(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoPromotorXCobertura(System.Int32)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.EliminarPromotorXCobertura(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCoberturaDTO)">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ActualizarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerPromotorPaginado(System.Int32,System.Int32,RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.GuardarOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO)">
            <summary>
            
            </summary>
            <param name="idOfertaVivienda"></param>
            <param name="idPromotores"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.EliminarOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO)">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ActualizarOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO)">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerOfertaxPromotorPaginado(System.Int32,System.Int32,System.String[],RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idOfertaVivienda"></param>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.GuardarDocumento(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoDocumentos(System.Int32[])">
            <summary>
            
            </summary>
            <param name="idDocumentos"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoCatEstado">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.GuardarOfertaxPromotorNotificacion(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacion)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoOfertaxPromotorNotificacion(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacion)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.GuardarGrupoNotificacionOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoGrupoNotificacionOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ActualizarGrupoNotificacionOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerListadoOfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.getSqlEnumerable(System.Int32[])">
            <summary>
            
            </summary>
            <param name="primaryKeys"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.getSqlEnumerable(System.String[])">
            <summary>
            
            </summary>
            <param name="primaryKeys"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Promotor.Implementacion.PromotorDataMapper.ObtenerPromotorAsis(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Promotor.IPromotorDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Prototipos.ICatalogosPrototiposDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.ICatalogosPrototiposDataMapper.ObtenerCatalogoDeTipologiaViviendaAsync">
            <summary>
            Obtiene los elementos del catalogo de Programas.
            </summary>
            <returns>Elementos del catalogo.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.ICatalogosPrototiposDataMapper.ObtenerCatalogoDeClasificacionViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.ICatalogosPrototiposDataMapper.ObtenerCatalogoTipoDimensionAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.ICatalogosPrototiposDataMapper.ObtenerCatalogoAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.CatalogosPrototiposDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.CatalogosPrototiposDataMapper.ObtenerCatalogoDeTipologiaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.CatalogosPrototiposDataMapper.ObtenerCatalogoDeClasificacionViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.CatalogosPrototiposDataMapper.ObtenerCatalogoTipoDimensionAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.CatalogosPrototiposDataMapper.ObtenerCatalogoAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ExistePrototipoPorNombreAsync(System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="nombre"></param>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
            <param name="gestorConexiones"></param>
            <param name="connectionString"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ObtenerPrototipoPorIdAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ObtenerViviendasPorPrototipoIdAsync(System.Int32,System.Int32,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idPrototipo"></param>
            <param name="idVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.GuardarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.GuardarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.GuardarTipoDistribucionViviendaAsync(System.Int32,System.Decimal,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idTipoDimensionAreaVivienda"></param>
            <param name="valor"></param>
            <param name="idPrototipo"></param>
            <param name="idTipoAreaVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.GuardarDocumentosPrototipoAsync(RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv,System.Int32)">
            <summary>
            
            </summary>
            <param name="documento"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.GuardarDocumentosComunAsync(RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv,System.Int32)">
            <summary>
            
            </summary>
            <param name="documento"></param>
            <param name="idCatalogo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarTipoDistribucionViviendaAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ActualizarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ActualizarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ObtenerListaDocumentosRuvAsync(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="idCatalogo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ObtenerPrototipoGuardadoTemporalAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.ObtenerPrototiposFiltradoAsync(System.Int32,System.Int32,System.Nullable{System.Int32},System.String,System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Int32},System.String,System.Nullable{System.Boolean},System.String,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarDimensionAreaAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarDocumentoPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarDocumentoPrototipoAsync(System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="idDocumento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.Implementacion.PrototiposDataMapper.EliminarDocumentoComunAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idDocumento"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ExistePrototipoPorNombreAsync(System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="nombre"></param>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ObtenerPrototipoGuardadoTemporalAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.GuardarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.GuardarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.GuardarTipoDistribucionViviendaAsync(System.Int32,System.Decimal,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idTipoDimensionAreaVivienda"></param>
            <param name="valor"></param>
            <param name="idPrototipo"></param>
            <param name="idTipoAreaVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.GuardarDocumentosPrototipoAsync(RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv,System.Int32)">
            <summary>
            
            </summary>
            <param name="documento"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarTipoDistribucionViviendaAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ObtenerListaDocumentosRuvAsync(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="idCatalogo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.GuardarDocumentosComunAsync(RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv,System.Int32)">
            <summary>
            
            </summary>
            <param name="documento"></param>
            <param name="idCatalogo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ObtenerPrototiposFiltradoAsync(System.Int32,System.Int32,System.Nullable{System.Int32},System.String,System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Int32},System.String,System.Nullable{System.Boolean},System.String,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <param name="precioDesde"></param>
            <param name="precioHasta"></param>
            <param name="idTipologiaVivienda"></param>
            <param name="nombre"></param>
            <param name="esPorPrecio"></param>
            <param name="oferente"></param>
            <param name="recamaras"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ObtenerPrototipoPorIdAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ObtenerViviendasPorPrototipoIdAsync(System.Int32,System.Int32,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idPrototipo"></param>
            <param name="idVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ActualizarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ActualizarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarDocumentoPrototipoAsync(System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="idDocumento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarDocumentoComunAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idDocumento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarDocumentoPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.EliminarDimensionAreaAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper.ObtenerPrototiposAsociados(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idProyecto"></param>
            <param name="idOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCatalogoDeZonasDeRiesgoAsync">
            <summary>
            Obtiene los elementos del catalogo de las zonas de riesgo.
            </summary>
            <returns>Elementos del catalogo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCatalogoPrototiposxEmpresasync(System.Int32)">
            <summary>
            Obtiene el id y nombre de los prototipos por empresa
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCatalogoProyectosxEmpresasync(System.Int32)">
            <summary>
            Obtiene el id y nombre de los proyectos por empresa
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCatalogoVialidadesFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de vialidades de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCatalogoasentamientosFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de asentamientos de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerVialidadesFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de vialidades de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerAsentamientosFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de asentamientos de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCatalogoLocalidadesFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de localidadse de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.ICatalogosProyectosDataMapper.ObtenerCodigoPostalXEstadoMunicipio(System.String,System.String)">
            <summary>
            Obtiene una lista de cp validos correspondientes al estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCatalogoDeZonasDeRiesgoAsync">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCatalogoPrototiposxEmpresasync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCatalogoProyectosxEmpresasync(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCatalogoVialidadesFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCatalogoLocalidadesFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCatalogoasentamientosFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.CatalogosProyectosDataMapper.ObtenerCodigoPostalXEstadoMunicipio(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.ObtenerVivendasPorIdProyectoAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para una un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de pagina</param>
            <param name="pagina">Pagina</param>
            <returns>Lista de ordenes de trabajo de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.ObtenerCuvsFiltradasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.ObtenerCuvsFiltradasPaginadasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv,System.Int32,System.Int32)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
            <param name="pagina">Pagina consultada</param>
            <param name="tamanioPagina">Tamaño de la pagina consultada</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.AgregarAsentamientoAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Asentamiento)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.AgregarVialidadAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vialidad)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion.ProyectosDataMapper.obtenerListaEstatusOfertaViviendas(System.Collections.Generic.List{System.String})">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ObtenerVivendasPorIdProyectoAsync(System.Int32)">
             <summary>
            
             </summary>
             <param name="idProyecto"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para una un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de pagina</param>
            <param name="pagina">Pagina</param>
            <returns>Lista de ordenes de trabajo de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ObtenerVivienasPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de viviendas para una un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de pagina</param>
            <param name="pagina">Pagina</param>
            <returns>Lista de viviendas del proyecto</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ObtenerCuvsFiltradasPaginadasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv,System.Int32,System.Int32)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
            <param name="pagina">Pagina consultada</param>
            <param name="tamanioPagina">Tamaño de la pagina consultada</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.EliminarVivienda(System.Int32)">
            <summary>
            Elimina una vivienda cuando no tiene cuv
            </summary>
            <param name="idVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.AgregarAsentamientoAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Asentamiento)">
            <summary>
            Agrega un asentamiento
            </summary>
            <param name="asentamiento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.AgregarVialidadAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vialidad)">
            <summary>
            Agrega una vialidad
            </summary>
            <param name="vialidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ActualizarAsentamientoAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Asentamiento)">
            <summary>
            Actualiza un asentamiento
            </summary>
            <param name="asentamiento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ActualizarVialidadAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vialidad)">
            <summary>
            Actualiza una vialidad
            </summary>
            <param name="vialidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.obtenerListaEstatusOfertaViviendas(System.Collections.Generic.List{System.String})">
            <summary>
            Obtiene una lista de estatus oferta para una lista de viviendas.
            </summary>
            <param name="cuvs"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ObtenerCuvsFiltradasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ObtenerEstatusUltimaOdtProyecto(System.Int32)">
            <summary>
            Obtiene el estatus de la ultima Odt generada por el envio de un proyecto, en caso de no existir , regresa 0
            </summary>
            <param name="idProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper.ValidarMismaTipologiaViviendaAsync(System.String)">
            <summary>
            Valida la actualizacion de la tipologia de una lista de viviendas
            </summary>
            <param name="listaCUVsViviendas"></param>
            <returns>Lista de viviendas con su tipologia anterior</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerFichaPoliza(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.TipoPagoSeguroCalidad,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tipoPagoSegurCalidad"></param>
            <param name="idOrdenVerificacion"></param>
            <param name="idCuvsSolicitadas"></param>
            <param name="idFichaPago"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.CargarDatosFichaPagoAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.BuscarProyectoPrepagoAsync(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.AfectaSaldoFichaPago(System.Decimal,System.Decimal,System.Int32,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.AfectaSaldoManualFichaPago(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AfectacionSaldo)">
            <summary>
            
            </summary>
            <param name="afectacionSaldo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.PagosFichasSC(System.Data.DataTable)">
            <summary>
            
            </summary>
            <param name="dtAfectaSaldos"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.InsertaLogErrores(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.GuardarIdDocumentoFichaPago(System.String,System.String,System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.CambiarEstatusFichaPago(System.Int32,System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Cambia el estatus de una ficha de pago
            </summary>
            <param name="idFichaPago"></param>
            <param name="idEstatusFichaPago"></param>
            <param name="fechaPago"></param>
            <param name="fechaRealPago"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerTarifaxIdEmpresaSCAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idRUVAsIs"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ValidarSaldoDisponibleSCAsync(System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Metodo para validar si tiene el monto disponible de prepago
            </summary>
            <param name="idRuvAsIs"></param>
            <param name="montoTotal"></param>
            <param name="costoUsoPlataforma"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerEmpresaxIdRuvAsis(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idRuvAsIs"></param>
            <param name="idEntidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.AgregarDetalleFichaPagoSC(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DetalleFichaPagoPoliza)">
            <summary>
            
            </summary>
            <param name="detalleFicha"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerDetalleFichaPagoSC(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DetalleFichaPagoPoliza)">
            <summary>
            Consulta la información del detalle de ficha pago póliza
            </summary>
            <param name="detalleFicha"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerFichaSeguroCalidadExistente(System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="tipoPagoSegurCalidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerTarifaActiva(System.Int32)">
            <summary>
            
            </summary>
            <param name="idServicio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.AgregarYObtenerFichaPago(RUV.RUVPP.Oferta.Modelo.Oferta.Api.FichaPagoDTO)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerClave(RUV.RUVPP.Oferta.Modelo.Oferta.Api.ServicioDto)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerProducto(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ProductoDto)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.CancelarFichaPagoDC(System.Int32,System.String,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="idFichaPagoPol"></param>
            <param name="clavePol"></param>
            <param name="idFichaPagoDif"></param>
            <param name="claveDif"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper__.ObtenerDatosUsuariosxEmpresa(System.Int32)">
             <summary>
            
             </summary>
             <param name="idEmpresa"></param>
             <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper">
            <summary>
            Acceso a datos de Seguro de Calidad
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.getSqlEnumerableString(System.String[])">
            <summary>
            
            </summary>
            <param name="primaryKeys"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.AgregarPolizaMasivoAsync(System.Data.DataTable)">
             <summary>
            
             </summary>
             <param name="tblCuvs"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneOrdenesVerificacionConPaginadoAsync(System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <param name="ordenVerificacion"></param>
            <param name="noContrato"></param>
            <param name="idEmpresaInstAseguradora"></param>
            <param name="idTipoAsignacion"></param>
            <param name="razonSocialAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneRelacionComercialConPaginadoAsync(System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <param name="idEmpresaInst"></param>
            <param name="nombreRazonSocial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarRelacionComercialOrdenAsync(System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="idAseguradora"></param>
            <param name="idRelacionComercial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerViviendasOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerRelacionComercialOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.BuscarCuv(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="numeroCredito"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.QuitarCandadoCuvs(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="cuvs"></param>
            <param name="idFichas"></param>
            <param name="idGrupoPoliza"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizaPagadoCuvs(System.String,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="cuvs"></param>
            <param name="idFichas"></param>
            <param name="idGrupoPoliza"></param>
            <param name="idEmpresa"></param>
            <param name="numerosDocumento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.BuscarTieneCobertura(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.BuscarTieneSeguroCalidad(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.GuardarDatosValorAvaluoAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.EliminarPolizaVigenteAsync(System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idVvGeneral"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.EliminarPolizaPuroAsync(System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idPoliza"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.BuscarxCuvAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ConsultarAvaluoxCuvAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ConsultarCostoEvaluacionCuvxOVSCAsync(System.Data.DataTable,System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="cambioBandera"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerRelacionComercialxOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerParametroAsIsAsync(System.String)">
            <summary>
            
            </summary>
            <param name="clave"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneCriterioSinInicioObraAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneCriterioSinInicioObraOVSCAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneOrdenVeirificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarCostoPolizaAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarCostoPolizaConIvaAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.InsertarActualizarCostoEvaluacionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.InsertarActualizarCostoEvaluacionConIvaAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.InsertarActualizarCostoEvaluacionReCalculoAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerUltimoGrupoPolizaAsync">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerCuvsXGrupoPolizaAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idGrupo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerCuvsXGrupoPolizaTotalViviendaAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneCriteriosCuvsAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.RecalcularCostoPolizaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.RecalcularCostoIvaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.RecalcularCostoConIvaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerDatosGeneralesPolizaAsync(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerDatosGeneralesCuvPolizaAsync(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerDireccionCuvPolizaAsync(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarUrlPolizaxCuv(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerViviendasAGenerarPoliza(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerUrlPoliza(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerOrdenesValidacionDocAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerOrdenesReporteInicialAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerOrdenesReporteDocumentalAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerOrdenesReporteHabitabilidadAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarNotificacionAseguradoraxOrden(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarNotificacionReporteInicial(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarNotificacionReporteDocumental(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarNotificacionReporteHabitabilidad(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerMontoPagadoxViviendaAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObteneDocumentoxCUVAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.InsertarEnBitacoraPeticionesAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ViviendaAsegurada,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerOrdenVerificacionAseguradoraAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarCuvsBloqueadasAsync(System.String,System.Boolean)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerParametroAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.EliminarDatosFichaPagoSeguroAsync">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.InsertarEnFichaPagoSeguroAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.FichaPagoSeguroCalidad)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.InsertarPagosAExternosAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ConfirmacionPagoAseguradora)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerDatosPagoAseguradoraEvaluacionAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerDatosPagoAseguradoraPolizaAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.AgregarPagosPendientesPolizaAseguradoraAsync">
            <summary>
            
            </summary>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.AgregarPagosPendientesEvaluacionAseguradoraAsync">
            <summary>
            
            </summary>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarEstatusSolicitudPagoAsync(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerOrdenesEnviarAseguradoraAsync">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ObtenerSiniestrosAbiertos">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.Implementacion.SeguroCalidadDataMapper.ActualizarSiniestroEnviado(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISAPDataMapper.ObtenerDatosEstadoDeCuentaSaldos(System.Int32)">
            <summary>
            Obtiene los saldos para el estado de cuenta por empresa
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISAPDataMapper.ObtenerDatosEstadoDeCuentaMovimientos(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene los movimientos para el estado de cuenta por empresa
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDatosContactoEmpresaAsync(System.String)">
            <summary>
            Obtiene el telefono, correo y pagina web de una empresa.
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDomicilioGeograficoAsync(System.String,System.String)">
            <summary>
            Obtiene la domicilio geografico de una lista de idEmpresas
            </summary>
            <param name="listaIdEmpresas"></param>
            <param name="listaidEmpresaInst"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDatosContactoStringAsync(System.String,System.String)">
            <summary>
            Obtiene los correos y los telegonos de una lista de idEmpresas
            </summary>
            <param name="listaIdEmpresas"></param>
            <param name="listaidEmpresaInst"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDetalleFichaPolizaAsync(System.Int32,System.Int32)">
            <summary>
            Obtiene Detalle Ficha Poliza
            </summary>
            <param name="idCuvSolicitada"></param>
            <param name="idTipoPagoSeguroCalidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerCuentaBancariaStringAsync(System.Int32)">
            <summary>
            Obtiene lista de cuentas bancarias
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerFichaPoliza(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.TipoPagoSeguroCalidad,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tipoPagoSegurCalidad"></param>
            <param name="idOrdenVerificacion"></param>
            <param name="idCuvsSolicitadas"></param>
            <param name="idFichaPago"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDetalleFichaPagoAnticipadoAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.AfectaSaldoFichaPago(System.Decimal,System.Decimal,System.Int32,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="montoPoliza"></param>
            <param name="montoDiferencia"></param>
            <param name="idEmpresa"></param>
            <param name="claveProd"></param>
            <param name="descripcion"></param>
            <param name="idTipoCargo"></param>
            <param name="idTipoOperacion"></param>
            <param name="montoUnitario"></param>
            <param name="idUsuario"></param>
            <param name="referencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.AfectaSaldoManualFichaPago(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AfectacionSaldo)">
            <summary>
            
            </summary>
            <param name="afectacionSaldo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.PagosFichasSC(System.Data.DataTable)">
            <summary>
            
            </summary>
            <param name="dtAfectaSaldos"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.InsertaLogErrores(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="claveProd"></param>
            <param name="descripcion"></param>
            <param name="ordenVerificacion"></param>
            <param name="idEmpresa"></param>
            <param name="idUsuario"></param>
            <param name="referencia"></param>
            <param name="descripcionError"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.GuardarIdDocumentoFichaPago(System.String,System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="referencia"></param>
            <param name="clave"></param>
            <param name="idDocumento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.CambiarEstatusFichaPago(System.Int32,System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Cambia el estatus de una ficha de pago
            </summary>
            <param name="idFichaPago"></param>
            <param name="idEstatusFichaPago"></param>
            <param name="fechaPago"></param>
            <param name="fechaRealPago"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerTarifaxIdEmpresaSCAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idRUVAsIs"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ValidarSaldoDisponibleSCAsync(System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Metodo para validar si tiene el monto disponible de prepago
            </summary>
            <param name="idRuvAsIs"></param>
            <param name="montoTotal"></param>
            <param name="costoUsoPlataforma"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerEmpresaxIdRuvAsis(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idRuvAsIs"></param>
            <param name="idEntidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.AgregarDetalleFichaPagoSC(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DetalleFichaPagoPoliza)">
            <summary>
            
            </summary>
            <param name="detalleFicha"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDetalleFichaPagoSC(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DetalleFichaPagoPoliza)">
            <summary>
            Consulta la información del detalle de ficha pago póliza
            </summary>
            <param name="detalleFicha"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerFichaSeguroCalidadExistente(System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="tipoPagoSegurCalidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerTarifaActiva(System.Int32)">
            <summary>
            
            </summary>
            <param name="idServicio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.AgregarYObtenerFichaPago(RUV.RUVPP.Oferta.Modelo.Oferta.Api.FichaPagoDTO)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerClave(RUV.RUVPP.Oferta.Modelo.Oferta.Api.ServicioDto)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerProducto(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ProductoDto)">
            <summary>
            
            </summary>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.CancelarFichaPagoDC(System.Int32,System.String,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="idFichaPagoPol"></param>
            <param name="clavePol"></param>
            <param name="idFichaPagoDif"></param>
            <param name="claveDif"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDatosEmpresaAsync(System.Nullable{System.Int32},System.String)">
            <summary>
            Obtiene los datos de la empresa pantalla estado de cuenta
            </summary>
            <param name="idEmpresa"></param>
            <param name="idEmpresaInst"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__.ObtenerDatosUsuariosxEmpresa(System.Int32)">
            <summary>
            Obtiene los datos de contacto de los usuarios por empresa
            </summary>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarDatosTitulacionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosTitulacionINFONAVIT)">
            <summary>
            Guarda los datos de titulación enviados por el INFONAVIT
            </summary>
            <param name="datosTitulacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerTipoIncidenciasAsync">
            <summary>
            Obtiene el catalogo de tipo de incidencias
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerEstatusPagoEvaluacionRiesgoAsync">
            <summary>
            Obtener el catalogo de estatus pago evaluacion riesgo
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerEstatusIncidenciasAsync">
            <summary>
            Obtiene el catalogo de estatus de incidencias
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerClasificacionIncidenciasAsync">
            <summary>
            Obtiene el catalogo de clasificacion de incidencias
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIncidenciasGestionAsync(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIncidenciasGestionSinPaginadoAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIdVerificadorAsync(System.String)">
            <summary>
            Obtiene el idVerificador del ASIS por medio del Numero de registro RUV
            </summary>
            <param name="idEmpresaIns"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIdAseguradoraAsync(System.String)">
            <summary>
            Obtiene el IdAseguradora del ASIS por medio del Numero de registro RUV
            </summary>
            <param name="idEmpresaIns"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerOrdenesVerificacionAsync(System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Obtiene una lista con las claves de las ordenes de verificacion con los parametros especificados
            </summary>
            <param name="idAseguradora"></param>
            <param name="idVerificador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerInformacionOVNuevaIncidenciaAsync(System.String)">
            <summary>
            Obtiene la informacion del encabezado mostrado en el registro de una nueva incidencia/notificacion
            </summary>
            <param name="OrdenVerificacion"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ValidarCuvVerificadorAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Valida si una cuv pertenece a un verificador
            </summary>
            <param name="cuv"></param>
            <param name="idVerificador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ValidarCuvAseguradoraAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Valida si una cuv tiene relacion con una aseguradora
            </summary>
            <param name="cuv"></param>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerCuvsXOVAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerCuvsXOVPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV paginado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerCatalogoCoberturaAfectadaAsync">
            <summary>
            Obtiene el catalogo de cobertura afectada
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerCatalogoClasificacioRiesgoAsync">
            <summary>
            Obtiene el catalogo de clasificacion de riesos
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarDocumentoXIncidenciaAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Guarda la relacion de un documento con una notificaión
            </summary>
            <param name="idDocumento"></param>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarIncidenciaNotificacionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.Incidencia,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.CuvSeleccionada)">
            <summary>
            Guarda una incidencia / Notificacion
            </summary>
            <param name="incidencia"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarIncidenciaNotificacionBitacoraAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.Incidencia,System.String)">
            <summary>
            Guarda una incidencia / Notificacion
            </summary>
            <param name="incidencia"></param>
            <param name="tipoAccion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerDocumentosIncidenciaNotificacionAsync(System.Int32,System.Int32)">
            <summary>
            Obtiene los documentos que pertenecen a una incidencia
            </summary>
            <param name="idIncidencia"></param>
            <param name="idVvGeneral"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIncidenciaNotificacionAsync(System.Int32)">
            <summary>
            Obtiene una incidencia / notificacion
            </summary>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ValidarCuvAseguradoraEvaluacionRiesgosAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Valida si una cuv tiene relacion con una aseguradora sin registro de evaluacion de riesgos
            </summary>
            <param name="cuv"></param>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerCuvsXOVEvaluacionRiesgosAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV sin registro de evaluacion de riesgos
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV paginado sin registro de evaluacion de riesgos
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarEvaluacionRiesgoAsync(System.String,System.Int32)">
            <summary>
            Guarda la relacion de una evaluacion de riesgo con una ov
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarEvaluacionRiesgoXViviendaAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.EvaluacionRiesgo,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.CuvSeleccionada)">
            <summary>
            Guarda una evaluacion de riesgo por cada vivienda
            </summary>
            <param name="evaluacionRiesgo"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarDocumentoXEvaluacionRiesgoXViviendaAsync(System.Int32,System.Int32)">
            <summary>
            Guarda la relacion de un documento con una evaluacion de riesgo
            </summary>
            <param name="idDocumento"></param>
            <param name="evaluacionRiesgoXVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerDetalleIncidenciaAsync(System.Nullable{System.Int32})">
            <summary>
            Obtiene la informacion general de una incidencia
            </summary>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerDatosMitigacionAsync(System.Nullable{System.Int32})">
            <summary>
            Obtiene los datos mostrados en ek registro de la mmitigacion de incidencia
            </summary>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ActualizarIncidenciaNotificacion(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.Incidencia)">
            <summary>
            Actualiza una incidencia/Notificacion
            </summary>
            <param name="incidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneOrdenesVerificacionConPaginadoAsync(System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneRelacionComercialConPaginadoAsync(System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.BuscarCuv(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.QuitarCandadoCuvs(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="cuvs"></param>
            <param name="idFichas"></param>
            <param name="idGrupoPoliza"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ActualizaPagadoCuvs(System.String,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="cuvs"></param>
            <param name="idFichas"></param>
            <param name="idGrupoPoliza"></param>
            <param name="idEmpresa"></param>
            <param name="numerosDocumento"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.BuscarTieneCobertura(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.BuscarTieneSeguroCalidad(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.GuardarDatosValorAvaluoAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ActualizarRelacionComercialOrdenAsync(System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="idAseguradora"></param>
            <param name="idRelacionComercial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerRelacionComercialOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerViviendasOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.EliminarEvaluacionRiesgoAsync(System.Nullable{System.Int32})">
            <summary>
            Elimina un registro de la tabla evaluacion de riesgo para una vivienda en particular
            </summary>
            <param name="idVvGeneral"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIncidenciasInternoGestionAsync(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <param name="verificador"></param>
            <param name="aseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerIncidenciasInternoGestionSinPaginadoAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <param name="verificador"></param>
            <param name="aseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerOferentePorOVAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.EliminarPolizaVigenteAsync(System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="idVvGeneral"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.BuscarxCuvAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ConsultarAvaluoxCuvAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ConsultarCostoEvaluacionxCuvAsync(System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ConsultarCostoEvaluacionCuvxOVSCAsync(System.Data.DataTable,System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="listaCuvs"></param>
            <param name="cuv"></param>
            <param name="cambioBandera"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerRelacionComercialxOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerParametroAsIsAsync(System.String)">
            <summary>
            
            </summary>
            <param name="clave"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneCriterioSinInicioObraAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneCriterioSinInicioObraOVSCAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneOrdenVeirificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ActualizarCostoPolizaAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.InsertarActualizarCostoEvaluacionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.ViviendasCostoxOrden)">
            <summary>
            
            </summary>
            <param name="viviendasCostoxOrden"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerUltimoGrupoPolizaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneCriteriosCuvsAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObteneListaCriteriosCuvsAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.RecalcularCostoPolizaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerDatosGeneralesPolizaAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerDireccionCuvPolizaAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper.ObtenerDatosGeneralesCuvPolizaAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDynamicParameters.#ctor">
            <summary>
            construct a dynamic parameter bag
            </summary>
        </member>
        <member name="M:OracleDynamicParameters.#ctor(System.Object)">
            <summary>
            construct a dynamic parameter bag
            </summary>
            <param name="template">can be an anonymous type or a DynamicParameters bag</param>
        </member>
        <member name="M:OracleDynamicParameters.AddDynamicParams(System.Object)">
            <summary>
            Append a whole object full of params to the dynamic
            EG: AddDynamicParams(new {A = 1, B = 2}) // will add property A and B to the dynamic
            </summary>
            <param name="param"></param>
        </member>
        <member name="M:OracleDynamicParameters.Add(System.String,System.Object,System.Nullable{Oracle.ManagedDataAccess.Client.OracleDbType},System.Nullable{System.Data.ParameterDirection},System.Nullable{System.Int32})">
            <summary>
            Add a parameter to this dynamic parameter list
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <param name="dbType"></param>
            <param name="direction"></param>
            <param name="size"></param>
        </member>
        <member name="M:OracleDynamicParameters.AddParameters(System.Data.IDbCommand,Dapper.SqlMapper.Identity)">
            <summary>
            Add all the parameters needed to the command just before it executes
            </summary>
            <param name="command">The raw command prior to execution</param>
            <param name="identity">Information about the query</param>
        </member>
        <member name="P:OracleDynamicParameters.ParameterNames">
            <summary>
            All the names of the param in the bag, use Get to yank them out
            </summary>
        </member>
        <member name="M:OracleDynamicParameters.Get``1(System.String)">
            <summary>
            Get the value of a parameter
            </summary>
            <typeparam name="T"></typeparam>
            <param name="name"></param>
            <returns>The value, note DBNull.Value is not returned, instead the value is returned as null</returns>
        </member>
    </members>
</doc>
