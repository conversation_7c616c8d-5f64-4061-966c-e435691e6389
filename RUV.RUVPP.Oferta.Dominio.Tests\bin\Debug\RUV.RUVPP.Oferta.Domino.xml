<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Oferta.Domino</name>
    </assembly>
    <members>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Avaluo.Implementacion.ServicioAvaluo.CargarBlobStorage(RUV.RUVPP.Oferta.Modelo.Avaluo.DocumentoFotoAvaluo)">
            <summary>
            Carga Fotos
            </summary>
            <param name="documentoFotoAvaluo"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.GuardarDictaminacionConSuspensionODTAsync(System.Int16,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Guardar el json de dictaminacion con suspension de la ODT
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idUsuario">Identificador del usuario</param>
            <param name="dictaminacionJSON">cadena conteniendo el JSON de dictaminacion</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ActualizarDictaminacionConSuspensionODTAsync(System.Int16,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Actualiza el JSON de dictaminacion suspendiendo la ODT
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idUsuario">Identificador del usuario que suspende la ODT</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.GuardarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Guarda el JSON de dictaminacion
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ActualizarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Actualiza el JSON de dictaminacion
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerDictaminacionAsync(System.Int16,System.Int32)">
            <summary>
            Obtiene el ultimo JSON de dictaminacion de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerDictaminacionPorOrdenTrabajoAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de dictaminacion de un registro por orden de trabajo
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerUltimaDictaminacionOAnteriorAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de un registro por orden de trabajo, y si no existe para esa orden, regresa el ultimo de ese registro.
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerMotivosRechazo(System.Int16,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Obtiene la lista de motivos de rechazo de un registro para una ODT determinada
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <param name="incluirMotivosArchivados">Bandera que indica si se incluyen los motivos de rechazo archivados en el json</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerMotivosRechazoCorreo(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene una cadena con los motivos de rechazo con formato de tabla HTML para mostrar en los correos.
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerSecciones(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion})">
            <summary>
            Obtiene el total de secciones cpntenidos ene le json de configuración
            </summary>
            <param name="seccion">Lista de secciones que contienen a su vez otras secciones</param>
            <param name="seccionesFinales">Lista de secciones finales</param>
            <returns>Lista con el total de secciones</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ObtenerElementos(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Elemento})">
            <summary>
            Obtiene el total de controles contenidos en el json de configuracion
            </summary>
            <param name="seccion">Lista de secciones que contienen los contoles</param>
            <param name="elementos">Lista de elementos a obtener</param>
            <returns>Lista con el total de controles de cada seccion</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.ConstruirListaMotivosRechazo(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Elemento},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo})">
            <summary>
            Genera una lista de tipo MotivosRechazo con la informacion obtenida del json de dictaminaciones
            </summary>
            <param name="seccionesFinales"></param>
            <param name="elementos">Total de elementos contenidos en las secciones obtenidos del json de configuracion</param>
            <param name="seccionesDictaminacion">Total de secciones contenidos en el json de ocnfiguracion</param>
            <param name="listaMotivosRechazo"></param>
            <returns>Lista de motivos rechazo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.obtenerRutaSecciones(System.Int32,System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion})">
            <summary>
            Construye el path de secciones a partir de un idSeccion y una lista de secciones
            </summary>
            <param name="idSeccion">identificador de la seccion a partir de la cual se construira la ruta</param>
            <param name="secciones">listado de secciones del json</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.GenerarListaHtmlMotivosRechazo(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo})">
            <summary>
            Genera una cadena que contiene el codigo html para generar la tabla de motivos de rechazo en el correo
            </summary>
            <param name="motivosRechazo">Lista de motivos de rechazo a renderizar</param>
            <returns>Cadena con el codigo html generado</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.Implementacion.ServicioDictaminacion.AjustarJsonDictaminacionVoluntaria(RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionContenedor)">
            <summary>
            Ajusta el Json de dictaminacion para actualizaciones voluntarias por primera vez.
            </summary>
            <param name="dictaminacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.GuardarDictaminacionConSuspensionODTAsync(System.Int16,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Guardar el json de dictaminacion con suspension de la ODT
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idUsuario">Identificador del usuario</param>
            <param name="dictaminacionJSON">cadena conteniendo el JSON de dictaminacion</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ActualizarDictaminacionConSuspensionODTAsync(System.Int16,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Actualiza el JSON de dictaminacion suspendiendo la ODT
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idUsuario">Identificador del usuario que suspende la ODT</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.GuardarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Guarda el JSON de dictaminacion
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ActualizarDictaminacionAsync(System.Int16,System.Int32,System.Int32,System.String)">
            <summary>
            Actualiza el JSON de dictaminacion
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ObtenerDictaminacionAsync(System.Int16,System.Int32)">
            <summary>
            Obtiene el ultimo JSON de dictaminacion de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ObtenerUltimaDictaminacionOAnteriorAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de un registro por orden de trabajo, y si no existe para esa orden, regresa el ultimo de ese registro.
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
            <param name="idRegistro">Identificador del registro</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ObtenerMotivosRechazo(System.Int16,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Obtiene la lista de motivos de rechazo de un registro para una ODT determinada
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <param name="incluirMotivosArchivados">Incluye los motivos de rechazo archivados</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ObtenerMotivosRechazoCorreo(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene una cadena con los motivos de rechazo con formato de tabla HTML para mostrar en los correos.
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion.ObtenerDictaminacionPorOrdenTrabajoAsync(System.Int16,System.Int32,System.Int32)">
            <summary>
            Obtiene el JSON de dictaminacion de un registro por orden de trabajo
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Historico.Implementacion.ServicioHistoricos">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.Implementacion.ServicioHistoricos.#ctor(RUV.RUVPP.Oferta.Datos.Historico.IHistoricosDataMapper)">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
            <param name="_historicosDataMapper"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.Implementacion.ServicioHistoricos.GuardarHistoricoOfertaVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusOfertaVivienda,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="estatusOfertaVivienda"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.Implementacion.ServicioHistoricos.GuardarHistoricoProyecto(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusProyectos,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="estatusProyecto"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.Implementacion.ServicioHistoricos.GuardarHistoricoVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusViviendas,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.DetalleEstatusVivienda})">
            <summary>
            
            </summary>
            <param name="estatusVivienda"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.Implementacion.ServicioHistoricos.ObtenerHistoricoCuvPorIdViviendaAsync(System.Int32)">
            <summary>
            Obtiene el historico de CUVs por IdVivienda
            </summary>
            <param name="idVivienda">Identificador de la vivienda</param>        
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos.GuardarHistoricoOfertaVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusOfertaVivienda,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="estatusOfertaVivienda"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos.GuardarHistoricoProyecto(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusProyectos,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.CampoAdicional})">
            <summary>
            
            </summary>
            <param name="estatusProyecto"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos.GuardarHistoricoVivienda(RUV.RUVPP.Oferta.Modelo.Historico.Data.EstatusViviendas,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Historico.Data.DetalleEstatusVivienda})">
            <summary>
            
            </summary>
            <param name="estatusVivienda"></param>
            <param name="listaDetalleEstatus"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos.ObtenerHistoricoCuvPorIdViviendaAsync(System.Int32)">
            <summary>
            Obtiene el historico de CUVs por IdVivienda
            </summary>
            <param name="idVivienda">Identificador de la vivienda</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos.InsertarDocumentoPorEventoVivienda(System.Int32,System.Int32)">
            <summary>
            Inserta datos en la tabla de Documento Por Evento Vivienda en la base.
            </summary>
            <param name="idEventoVivienda">Identificador del Evento de Vivienda</param>
            <param name="idDocumento">Identificador del Documento</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Historico.IServicioHistoricos.ObtenerDocumentosPorEventoVivienda(System.Int32)">
            <summary>
            Obtiene los documentos asociados a un Evento de Vivienda.
            </summary>
            <param name="idEventoVivienda">Identificador del Evento de Vivienda</param>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Mediciones.Implementacion.ServicioMediciones">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.Implementacion.ServicioMediciones.#ctor(RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper,RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos,RUV.RUVPP.Oferta.Datos.Empresa.IEmpresaDataMapper,RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta,RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper)">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
            <param name="_medicionesDataMapper"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.Implementacion.ServicioMediciones.ObtenerCuvsFiltradasPaginadasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv,RUV.Comun.Seguridad.JWT.CustomUserRuv,System.Int32,System.Int32)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Clave oferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv, OrdenVerificacion</param>        
            <param name="usuario">Datos del usuario</param>
            <param name="pagina">pagina consultada</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.Implementacion.ServicioMediciones.ObtenerCuvsAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs por clave oferta y/o orden verificacion
            </summary>
            <param name="ClaveOferta">Clave de la oferta</param>
            <param name="OrdenVerificacion">Orden de verificacion</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.Implementacion.ServicioMediciones.ObtenerEquipamientoPorVivienda(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerCuvsFiltradasPaginadasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv,RUV.Comun.Seguridad.JWT.CustomUserRuv,System.Int32,System.Int32)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Clave oferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv, OrdenVerificacion</param>        
            <param name="usuario">Datos del usuario</param>
            <param name="pagina">pagina consultada</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerCuvsAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs por clave oferta y/o orden verificacion
            </summary>
            <param name="ClaveOferta">Clave de la oferta</param>
            <param name="OrdenVerificacion">Orden de verificacion</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerEcotecnologiasXCUVConPaginadoAsync(System.Int32,System.Int32,System.String)">
            <summary>
            Obtienen una lista con las ecotecnologias de una determinada cuv.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerEquipamientoPorVivienda(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerAtributosXCUVConPaginadoAsync(System.Int32,System.Int32,System.String)">
            <summary>
            Obtienen una lista con los atributos de una determinada cuv.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerEquipamientoPorOferta(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerEquipamientoPorOrden(System.Int32,System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerViviendasPorEquipamiento(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="atributo"></param>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Mediciones.IServicioMediciones.ObtenerViviendasConsultaSembradoAsync(System.Nullable{System.Int32},System.Nullable{System.Int32},System.String)">
            <summary>
            Obtiene una lista de viviendas 
            </summary>
            <param name="idProyecto"></param>
            <param name="idOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta">
             <summary>
            
             </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerViviendasFiltroAsync(System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="idEmpresa"></param>
            <param name="noRuv"></param>
            <param name="nombreProyecto"></param>
            <param name="idProyecto"></param>
            <param name="idOferta"></param>
            <param name="claveOferta"></param>
            <param name="idVivienda"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerOfertaPorIdAsync(System.Int32)">
             <summary>
            
             </summary>
             <param name="idOferta"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerOfertasFiltradoAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Int32})">
             <summary>
            
             </summary>
             <param name="tamanioPagina"></param>
             <param name="pagina"></param>
             <param name="idOferta"></param>
             <param name="nombreProyecto"></param>
             <param name="idEmpresa"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerCatalogoDocumentosAsyn">
             <summary>
            
             </summary>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.EnviarCorreoOdtGenerada(System.Int32,RUV.RUVPP.Oferta.Modelo.Oferta.Api.Oferta,RUV.RUVPP.Negocio.General.OrdenTrabajo.Comun.OrdenBase,System.String)">
            <summary>
            Envia un correo electronico informando envio de datos a VyD
            </summary>
            <param name="idEmpresa">idEmpresa que registra el proyecto</param>
            <param name="oferta">Oferta a dictaminar</param>
            <param name="odt">Orden de trabajo generada</param>
            /// <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.EnviarCorreoOfertaAceptada(RUV.RUVPP.Oferta.Modelo.Oferta.Api.Oferta)">
            <summary>
            Envia un correo electronico informando dictaminacion aceptada
            </summary>
            <param name="oferta">Oferta dictaminado</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.EnviarCorreoOfertaRechazado(RUV.RUVPP.Oferta.Modelo.Oferta.Api.Oferta,System.Int32)">
            <summary>
            Envia un correo electronico informando dictaminacion rechazada
            </summary>
            <param name="oferta">Oferta dictaminado</param>
            <param name="idOrdenTrabajo">Orden de trabajo generada</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ActualizarEstatusOfertaViviendaAsync(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Actualiza el estatus de oferta vivienda
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
            <param name="idEstatus">Identificador del estatus</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerCatalogoEstatusViviendaAsync">
            <summary>
            Obtiene el catalogo de estatus de vivienda
            </summary>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerIdsOfertaPorProyectoAsync(System.Int32,System.Int32)">
            <summary>
            Obtienes los ids y clave de oferta con estatus pertenecientes a un idProyecto
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.EnviarDictaminacionOfertaVivienda(System.Int32,System.Int16,System.Int32,RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion,RUV.Comun.Seguridad.JWT.CustomUserRuv,System.Boolean)">
            <summary>
            Guarda/Actualiza la dictaminación, actualiza el estatus de la oferta y atiende la ODT.
            </summary>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro de oferta</param>
            <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
            seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
            DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
            aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.VerificarEstatusPeticion(System.Int32)">
            <summary>
            Realiza peticiones continuas al ASIS para conocer el estatus de la peticios previamente hecha
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
            <param name="idOfertaVivivenda"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ConfirmarPagoPorIdAsync(RUV.RUVPP.Oferta.Modelo.Oferta.Api.FichaPagoDTO)">
            <summary>
            Metodo para confirmar el pago en SAP una vez que éste sea pagado
            </summary>
            <param name="fichaPagoDTO">id de la oferta de vivienda</param>
            <returns>Regresa true si el proceso se ejecuta correctamente</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerMontoOfertaAsync(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Oferta.Api.ViviendaPrototipo},System.Int32)">
             <summary>
            Metodo para calcular el monto de las viviendas seleccionadas
             </summary>
             <param name="viviendasProyecto">Viviendas del proyecto</param>
             /// <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
             <returns>Regresa el monto total de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerPrecioViviendasSeleccionadas(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Oferta.Api.ViviendaPrototipo},System.Int32)">
            <summary>
            Metodo para extraer las viviendas seleccionadas con sus costos
            </summary>
            <param name="viviendasProyecto">Viviendas del proyecto</param>
            <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
            <returns>Regresa una lista de viviendas con sus precios para calcular el monto total</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.GuardarHistoricoProyectoAsync(RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoProyecto,System.Int32,System.String,RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Envía los parámetros introducidos al Procedimiento Almacenado
            correspondiente para insertarlos en la base de datos de histórico.
            </summary>
            <param name="idEvento">Enumerador del tipo de evento registrado.</param>
            <param name="idProyecto">Identificador del Proyecto.</param>
            <param name="estatusProyecto">Línea del tipo de evento registrado.</param>
            <param name="usuario">Clase Usuario con Identificador y Nombre.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.GuardarHistoricoOfertaAsync(RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoOferta,System.Int32,System.String,System.String,RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Envía los parámetros introducidos al Procedimiento Almacenado
            correspondiente para insertarlos en la base de datos de histórico.
            </summary>
            <param name="idEvento">Enumerador del tipo de evento registrado.</param>
            <param name="claveOferta">Clave de la Oferta.</param>
            <param name="_estatusOferta">Línea del tipo de evento registrado.</param>
            <param name="usuario">Clase Usuario con Identificador y Nombre.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para una oferta por idOferta
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerEstatusViviendaASISXVivienda(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda})">
            <summary>
            Obtiene el estatus de cada vivienda determinado por el ASIS para saber si se puede o no editar x Vivienda
            </summary>
            <param name="viviendas"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.ObtenerEstatusViviendaAsIs(System.String)">
            <summary>
            Obtiene la clave del AsIs para saber el estado de una CUV (Editable, Cancelable, etc.).
            </summary>
            <param name="cuv">Clave Única de Vivienda a verificar.</param>
            <returns>Clave con valor que indica su estado según catálogo.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.Implementacion.ServicioOferta.IndividualizarViviendasMasivo(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Viviendas.ViviendaIndividualizable},RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Individualiza las viviendas que sean enviadas en el arreglo.
            </summary>
            <param name="arregloViviendas">Arreglo que contiene las viviendas para individualizar.
            Estas deben contener CUV, Fecha, ONAVI y Tipo de Venta.</param>
            <returns>Booleano que representa resultado de la transacción.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerOfertaPorIdAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerOfertasFiltradoAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idOferta"></param>
            <param name="nombreProyecto"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ActualizarEstatusOfertaViviendaAsync(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Actualiza el estatus de oferta vivienda
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
            <param name="idEstatus">Identificador del estatus</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.EnviarDictaminacionOfertaVivienda(System.Int32,System.Int16,System.Int32,RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion,RUV.Comun.Seguridad.JWT.CustomUserRuv,System.Boolean)">
            <summary>
            Guarda/Actualiza la dictaminación, actualiza el estatus de la oferta y atiende la ODT.
            </summary>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
            seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
            DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
            aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para una oferta por idOferta
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerViviendasOfertaPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado viviendas para una oferta por idOferta
            </summary>
            <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerDocumentosOfertaAsync(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene la documentación ligada al idOferta introducido.
            </summary>
            <param name="idOferta">Identificador de la oferta.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerCatalogoEstatusViviendaAsync">
            <summary>
            Obtiene el catalogo de estatus de vivienda
            </summary>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerIdsOfertaPorProyectoAsync(System.Int32,System.Int32)">
            <summary>
            Obtienes los ids y clave de oferta con estatus pertenecientes a un idProyecto
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ConfirmarPagoPorIdAsync(RUV.RUVPP.Oferta.Modelo.Oferta.Api.FichaPagoDTO)">
            <summary>
            Metodo para confirmar el pago en SAP una vez que éste sea pagado
            </summary>
            <param name="fichaPagoDTO">id de la oferta de vivienda</param>
            <returns>Regresa true si el proceso se ejecuta correctamente</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerMontoOfertaAsync(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Oferta.Api.ViviendaPrototipo},System.Int32)">
             <summary>
            Metodo para calcular el monto de las viviendas seleccionadas
             </summary>
             <param name="viviendasProyecto">Viviendas del proyecto</param>
             /// <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
             <returns>Regresa el monto total de la oferta</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerViviendasPorIdOfertaAsync(System.Int32)">
            <summary>
            Envía un idOferta para obtener la lista de viviendas con sus estatus respectivos.
            </summary>
            <param name="idOferta">Identificador de la oferta.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerIdOfertaPorIdViviendaAsync(System.Int32[])">
            <summary>
            Envía un idVivienda y obtiene el idOfertaVivienda que tiene asignado.
            </summary>
            <param name="idVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.IndividualizarViviendasMasivo(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Viviendas.ViviendaIndividualizable},RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Envía un idVivienda y obtiene el idOfertaVivienda que tiene asignado.
            </summary>
            <param name="idVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerClaveOfertaxCuvAsync(System.String)">
            <summary>
            Obtiene la clave oferta de una cuv
            </summary>
            <param name="cuv">Clave única de vivienda</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.DisponibilizarListaCuv(System.Int32[],System.Int32[],RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Actualiza el estatus de las CUV a Disponible.
            </summary>
            <param name="listaIdVivienda">Arreglo de Identificadores de Vivienda.</param>
            <param name="listaIdDocumento">Arreglo de Identificadores de Documentos Asociados.</param>
            <param name="usuario">Usuario que realiza el acción.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ActualizaCuvsAIndividualizadaPorIdsViviendaAsync(System.Int32[],System.String,System.Int32,System.Nullable{System.Int32},RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Actualiza el estatus de las CUV a Individualizada.
            </summary>
            <param name="idsVivienda">Identificadores de vivienda como 'string' separados por comas.</param>
            <param name="fecha">Fecha de Individualizar.</param>
            <param name="medioIndividualizacion">Identificador del Medio de Individualizacion.</param>
            <param name="tipoIndividualizacion">Identificador del Tipo de Individualizacion.</param>
            <param name="usuario">Usuario que realiza el acción.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.EliminarCuvsPorIdsViviendaAsync(System.Int32[],RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Elimina las cuvs especificadas.
            </summary>
            <param name="idsVivienda">Identificadores de las viviendas.</param>
            <param name="usuario">Usuario que realiza la eliminación.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerDocumentosEventoVivienda(System.Int32)">
            <summary>
            Obtiene los documentos asociados a un Evento Vivienda.
            </summary>
            <param name="idEventoVivienda">Identificadores de las viviendas.</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.InsertarValoresDocumentoPorEstatusVivienda(System.Int32,System.Int32,System.String)">
            <summary>
            Inserta valores en la tabla de Documentos por Cambio de Estatus de Vivienda.
            </summary>
            <param name="idDocumento">Identificador del Documento.</param>
            <param name="idVivienda">Identificador de la Vivienda.</param>
            <param name="descripcionArchivo">Descripción del Documento.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerDocumentosPorIdentificadores(System.Int32[])">
            <summary>
            Obtiene documentos de acuerdo a los identificadores enviados.
            </summary>
            <param name="idDocumentos">Identificadores de los Documentos.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.EliminarRegistrosDocumentoPorEventoVivienda(System.Int32)">
            <summary>
            Procedimiento Almacenado para Eliminar un registro de la tabla de Descripciones de Documentos por Evento Vivienda.
            </summary>
            <param name="idDocumento">Identificador del Documento.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerMedioIndividualizacion">
            <summary>
            Método para obtener los medios de individualización junto con un campo extra de Banco.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerBancosIndividualizacion">
            <summary>
            Método para obtener los bancos como medio de individualización.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.ObtenerTipoIndividualizacion(System.Int32)">
            <summary>
            Método para obtener los tipos de individualización de acuerdo a un medio introducido.
            </summary>
            <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.InsertarIndividualizacion(System.Int32,System.Int32,System.Nullable{System.Int32},System.DateTime)">
            <summary>
            Método para insertar valores en la tabla de Individualización de Vivienda.
            </summary>
            <param name="idVivienda">Identificador de la Vivienda.</param>
            <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
            <param name="idTipoIndividualizacion">Identificador del Tipo de Individualización. El dato es opcional.</param>
            <param name="fechaIndividualizacion">Fecha seleccionada.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Oferta.IServicioOferta.EliminarIndividualizacion(System.Int32)">
            <summary>
            Método para eliminar valores en la tabla de Individualización de Vivienda.
            </summary>
            <param name="idVivienda">Identificador de la Vivienda.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.#ctor(RUV.RUVPP.Oferta.Datos.OrdenVerificacion.IOrdenVerificacionDataMapper,RUV.RUVPP.Oferta.Datos.Proyectos.IProyectosDataMapper,RUV.RUVPP.Oferta.Datos.Mediciones.IMedicionesDataMapper,RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.IAgenteServicioConvivenciaASIS,RUV.RUVPP.Oferta.Datos.Oferta.IOfertaDataMapper,RUV.RUVPP.Negocio.Empresa.Comun.IServicioEmpresaComun,RUV.RUVPP.Datos.Comun.Contratos.IPersistible{RUV.RUVPP.Entidades.General.Documentos.DocumentoDto})">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
            <param name="_ordenVerificacionDataMapper"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerDetalleOrdenVerificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerOrdenesVerificacionFiltroAsync(System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerOrdenesVerificacionFiltroAsync(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <param name="idRUVAsis"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerViviendasPorOrdenesVerificacionAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="idVivienda"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(System.String)">
            <summary>
            
            </summary>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <param name="idCuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idVivienda"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerViviendasReportePorOrdenesVerificacionAsync(System.String)">
            <summary>
            Obtiene los datos del sembrado, habitabilidad, puntajes, numero de ecotecnologias, numero de atributos de las viviendas por OV
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion.ServicioOrdenVerificacion.ObtenerViviendasPorOrdenesVerificacionAsync(System.String)">
            <summary>
            Obtiene los datos del sembrado, y su estatus ASIS de las viviendas por OV
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerOrdenesVerificacionFiltroAsync(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerOrdenesVerificacionFiltroAsync(System.String,System.Int32)">
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(System.String)">
            <summary>
            Obtiene la ultima orden de verificacion de una oferta
            </summary>
            <param name="claveOferta"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerViviendasPorOrdenesVerificacionAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="idVivienda"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerDetalleOrdenVerificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="claveOferta"></param>
            <param name="idOrdenVerificacion"></param>
            <param name="idCuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idVivienda"></param>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.IServicioOrdenVerficacion.ObtenerViviendasReportePorOrdenesVerificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ObtenerConfiguracionAsync(System.Int32)">
            <summary>
            Obtiene el JSON de configuracion como objeto de un servicio especificado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ObtenerConfiguracionReglasAsync(System.Int32)">
            <summary>
            Obtiene una tupla que contiene el JSON de configuracion y de reglas como objeto y arreglo de un servicio determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de registro/vyd como objeto para un registro determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador de registro</param>
            <param name="jsonEstatus">Cadena conteniendo elJSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena conteniendo el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio.ObtenerConfiguracionAsync(System.Int32)">
            <summary>
            Obtiene el JSON de configuracion como objeto de un servicio especificado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio.ObtenerConfiguracionReglasAsync(System.Int32)">
            <summary>
            Obtiene una tupla que contiene el JSON de configuracion y de reglas como objeto y arreglo de un servicio determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena conteniendo el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de registro/vyd como objeto para un registro determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador de registro</param>
            <param name="jsonEstatus">Cadena conteniendo elJSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.GuardarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO)">
            <summary>
            
            </summary>
            <param name="model"></param>
            <param name="idEntidades"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.EliminarPromotorXCobertura(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCoberturaDTO)">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerListadoPromotorXCobertura(System.Int32)">
            <summary>
            
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ActualizarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO)">
            <summary>
            
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerPromotorPaginado(System.Int32,System.Int32,RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerOfertaViviendaPaginado(System.Int32,System.Int32,RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.GuardarOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO)">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ActualizarOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO)">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.EliminarOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO)">
            <summary>
            
            </summary>
            <param name="idPromotores"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerListadoOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerOfertaxPromotorPaginado(System.Int32,System.Int32,System.String[],RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idOfertaVivienda"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.GuardarDocumento(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos)">
            <summary>
            
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerListadoDocumentos(System.Int32[])">
            <summary>
            
            </summary>
            <param name="idDocumentos"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerListadoCatEstado">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.GuardarOfertaxPromotorNotificacion(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacion)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerListadoOfertaxPromotorNotificacion(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacion)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.GuardarGrupoNotificacionOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ObtenerListadoGrupoNotificacionOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.ActualizarGrupoNotificacionOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.getJsonPromotor(System.String[],System.Int32[])">
            <summary>
            
            </summary>
            <param name="idOfertaViviendas"></param>
            <param name="idPromotores"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.saveJson(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO,System.Boolean,System.Collections.Generic.Dictionary{System.String,System.Boolean})">
            <summary>
            
            </summary>
            <param name="dto"></param>
            <param name="asignar"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.reEnviarJSON">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.ServicioPromotor.registrarPromotor">
            <summary>
            
            </summary>
            <param name="model"></param>
            <param name="idEntidades"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.CallService`1.getEntity(System.String)">
             <summary>
            
             </summary>
             <param name="url"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.CallService`1.getListEntity(System.String)">
             <summary>
            
             </summary>
             <param name="url"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.CallService`1.getObject(System.String)">
            <summary>
            
            </summary>
            <param name="url"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion.CallService`1.PostEntity(System.String,`0)">
             <summary>
            
             </summary>
             <param name="url"></param>
             <param name="entity"></param>
             <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo">
             <summary>
            
             </summary>
        </member>
        <member name="F:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo._prototipoDM">
             <summary>
            
             </summary>
        </member>
        <member name="F:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo._servicioDocumento">
             <summary>
            
             </summary>
        </member>
        <member name="F:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo._servicioDictaminacion">
             <summary>
            
             </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.#ctor(RUV.RUVPP.Oferta.Datos.Prototipos.IPrototiposDataMapper,RUV.RUVPP.Negocio.General.Documentos.Abstracta.IServicioDocumento{RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta},RUV.RUVPP.Oferta.Dominio.Dictaminacion.IServicioDictaminacion,RUV.RUVPP.Oferta.Dominio.ProductoServicio.IServicioProductoServicio,RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.IAgenteServicioConvivenciaASIS,RUV.RUVPP.Oferta.Datos.Empresa.IEmpresaDataMapper)">
             <summary>
            
             </summary>
             <param name="clienteTelemetria"></param>
             <param name="prototipoDM"></param>
             <param name="servicioDocumento"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.ExistePrototipoPorNombreAsync(System.String,System.Int32,System.Nullable{System.Int32})">
             <summary>
            
             </summary>
             <param name="nombre"></param>
             <param name="idEmpresa"></param>
             <param name="idPrototipo"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.GuardarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo,System.Boolean)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <param name="guardadoTemporal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.GuardarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
             <summary>
            
             </summary>
             <param name="prototipo"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.DuplicarPrototipoAsync(System.Int32,System.String)">
             <summary>
            
             </summary>
             <param name="idPrototipo"></param>
             <param name="nombrePrototipo"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.ValidarEstatusAsisAsync(System.Int32,System.Int32)">
            <summary>
            Valida en el ASIS si el prototipo puede ser actualizado.
            </summary>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.ActualizarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo,System.Boolean)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <param name="guardadoTemporal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.ObtenerPrototipoPorIdAsync(System.Int32)">
             <summary>
            
             </summary>
             <param name="idPrototipo"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.ObtenerViviendasPorPrototipoIdAsync(System.Int32,System.Int32,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idPrototipo"></param>
            <param name="idVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.ObtenerPrototiposFiltradoAsync(System.Int32,System.Int32,System.Nullable{System.Int32},System.String,System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32})">
             <summary>
            
             </summary>
             <param name="tamanioPagina"></param>
             <param name="pagina"></param>
             <param name="idEmpresa"></param>
             <param name="idPrototipo"></param>
             <param name="precioDesde"></param>
             <param name="precioHasta"></param>
             <param name="idTipologiaVivienda"></param>
             <param name="nombre"></param>
             <param name="oferente"></param>
             <param name="recamaras"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.EliminarPrototipoAsync(System.Int32,System.Boolean)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="soloJSON"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipo.EliminarDocumentoPrototipoAsync(System.Int32,System.Int32)">
             <summary>
            
             </summary>
             <param name="idPrototipo"></param>
             <param name="idDocumento"></param>
             <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos">
            <summary>
            
            </summary>
        </member>
        <member name="F:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos._catalogosPM">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos.#ctor(RUV.RUVPP.Oferta.Datos.Prototipos.ICatalogosPrototiposDataMapper)">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
            <param name="catalogosPM"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos.ObtenerCatalogoDeTipologiaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos.ObtenerCatalogoDeClasificacionViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos.ObtenerCatalogoTipoDimensionAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion.ServicioPrototipoCatalogos.ObtenerCatalogoAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipoCatalogos">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipoCatalogos.ObtenerCatalogoDeTipologiaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipoCatalogos.ObtenerCatalogoDeClasificacionViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipoCatalogos.ObtenerCatalogoTipoDimensionAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipoCatalogos.ObtenerCatalogoAreaViviendaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.ExistePrototipoPorNombreAsync(System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="nombre"></param>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.GuardarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo,System.Boolean)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <param name="guardadoTemporal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.GuardarDetallePrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.ValidarEstatusAsisAsync(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.DuplicarPrototipoAsync(System.Int32,System.String)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="nombrePrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.ObtenerPrototiposFiltradoAsync(System.Int32,System.Int32,System.Nullable{System.Int32},System.String,System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idPrototipo"></param>
            <param name="precioDesde"></param>
            <param name="precioHasta"></param>
            <param name="idTipologiaVivienda"></param>
            <param name="nombre"></param>
            <param name="oferente"></param>
            <param name="recamaras"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.EliminarPrototipoAsync(System.Int32,System.Boolean)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <param name="soloJSON"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.ObtenerPrototipoPorIdAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idPrototipo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.ObtenerViviendasPorPrototipoIdAsync(System.Int32,System.Int32,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idPrototipo"></param>
            <param name="idVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Prototipo.IServicioPrototipo.ActualizarPrototipoAsync(RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo,System.Boolean)">
            <summary>
            
            </summary>
            <param name="prototipo"></param>
            <param name="guardadoTemporal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ActualizarEstatusProyectoAsync(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Actualiza el estatus de un proyecto y si no logra actualizar el estatus realiza el trace en appInsights y manda una excepción.
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="idEstatus">Identificador del estatus</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ActualizarViviendasAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Sembrado,System.Nullable{System.Int32},System.String,System.String)">
            <summary>
            Actualiza las viviendas en ASIS, SIG y OFERTA
            </summary>
            <param name="sembrado"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.EnviarDictaminacionProyecto(System.Int32,System.Int16,System.Int32,RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion,RUV.Comun.Seguridad.JWT.CustomUserRuv,System.Boolean)">
            <summary>
            Guarda/Actualiza la dictaminación, actualiza el estatus del proyecto y atiende la ODT.
            </summary>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del proyecto</param>
            <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
            seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
            DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
            aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerVivendasPorIdProyectoAsync(System.Int32)">
            <summary>
            Obtiene las viviendas por medio del id del proyecto
            </summary>
            <param name="idProyecto"> id del proyecto</param>
            <returns>regresa una lista de las viviendas del proyecto</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerOrdenesVerificacionPorIdProyectoAsync(System.Int32,System.Int32,System.Int32)">
             <summary>
            
             </summary>
             <param name="tamanioPagina"></param>
             <param name="pagina"></param>
             <param name="idProyecto"></param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.EnviarCorreoOdtGenerada(System.Int32,RUV.RUVPP.Oferta.Modelo.Proyectos.Api.Proyecto,RUV.RUVPP.Negocio.General.OrdenTrabajo.Comun.OrdenBase,RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Envia un correo electronico informando envio de datos a VyD
            </summary>
            <param name="idEmpresa">idEmpresa que registra el proyecto</param>
            <param name="proyecto">Proyecto a dictaminar</param>
            <param name="odt">Orden de trabajo generada</param>
            /// <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.EnviarCorreoProyectoAceptado(RUV.RUVPP.Oferta.Modelo.Proyectos.Api.Proyecto)">
            <summary>
            Envia un correo electronico informando dictaminacion aceptada
            </summary>
            <param name="proyecto">Proyecto dictaminado</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.EnviarCorreoProyectoRechazado(RUV.RUVPP.Oferta.Modelo.Proyectos.Api.Proyecto,System.Int32,RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Envia un correo electronico informando dictaminacion rechazada
            </summary>
            <param name="proyecto">Proyecto dictaminado</param>
            <param name="idOrdenTrabajo">Orden de trabajo generada</param>
            <param name="usuario">Datos del usuario</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ConstruirListaMotivosRechazo(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Elemento},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo})">
            <summary>
            Genera una lista de tipo MotivosRechazo con la informacion obtenida del json de dictaminaciones
            </summary>
            <param name="seccionesFinales"></param>
            <param name="elementos">Total de elementos contenidos en las secciones obtenidos del json de configuracion</param>
            <param name="seccionesDictaminacion">Total de secciones contenidos en el json de ocnfiguracion</param>
            <param name="listaMotivosRechazo"></param>
            <returns>Lista de motivos rechazo</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.GenerarListaHtmlMotivosRechazo(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo})">
            <summary>
            Genera una cadena que contiene el codigo html para generar la tabla de motivos de rechazo en el correo
            </summary>
            <param name="motivosRechazo">Lista de motivos de rechazo a renderizar</param>
            <returns>Cadena con el codigo html generado</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerSecciones(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion})">
            <summary>
            Obtiene el total de secciones cpntenidos ene le json de configuración
            </summary>
            <param name="seccion">Lista de secciones que contienen a su vez otras secciones</param>
            <param name="seccionesFinales">Lista de secciones finales</param>
            <returns>Lista con el total de secciones</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerElementos(System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Seccion},System.Collections.Generic.List{RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion.Elemento})">
            <summary>
            Obtiene el total de controles contenidos en el json de configuracion
            </summary>
            <param name="seccion">Lista de secciones que contienen los contoles</param>
            <param name="elementos">Lista de elementos a obtener</param>
            <returns>Lista con el total de controles de cada seccion</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.obtenerListaCUVSASISPlano(System.Collections.Generic.List{System.String})">
            <summary>
            Obtiene una lista de lista de cuvs.
            </summary>
            <param name="viviendas"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerCuvsFiltradasPaginadasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv,System.Int32,System.Int32)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada desde la BD del RUV++
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">pagina consultada</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.GuardarHistoricoProyectoAsync(RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoProyecto,System.Int32,System.String,RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Envía los parámetros introducidos al Procedimiento Almacenado
            correspondiente para insertarlos en la base de datos de histórico.
            </summary>
            <param name="idEvento">Enumerador del tipo de evento registrado.</param>
            <param name="idProyecto">Identificador del Proyecto.</param>
            <param name="estatusProyecto">Línea del tipo de evento registrado.</param>
            <param name="usuario">Clase Usuario con Identificador y Nombre.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerCuvsFiltradasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerViviendasPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de viviendas para un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.EliminarViviendaTemporal(System.Int32)">
            <summary>
            Elimina una vivienda de la tabla temporal de Oracle
            </summary>
            <param name="featId"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.EliminarViviendaOficial(System.Int32)">
            <summary>
            Elimina una vivienda de la tabla temporal de Oracle
            </summary>
            <param name="featId"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerViviendaASIS(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda)">
            <summary>
            Construye uno objeto ViviendaActualizacionASIS a partir de una vivienda, para enviarlo al ASIS
            </summary>
            <param name="vivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.obtenerListaCUVSASIS(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda})">
            <summary>
            Obtiene una lista de lista de cuvs a partir de una lista de viviendas del tamanio especificado.
            </summary>
            <param name="viviendas"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.obtenerListaViviendasDivididas``1(System.Collections.Generic.List{``0})">
            <summary>
            Obtiene una lista de lista de viviendas del tamanio especificado.
            </summary>
            <param name="viviendas"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerEstatusViviendaASISXProyecto(RUV.RUVPP.Oferta.Modelo.Proyectos.Api.Proyecto)">
            <summary>
            Obtiene el estatus de cada vivienda determinado por el ASIS para saber si se puede o no editar x Proyecto
            </summary>
            <param name="proyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ObtenerDatosComplementariosVivienda(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vialidad},System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Asentamiento},RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda,System.Boolean)">
            <summary>
            Obtiene los datos complementarios de las viviendas a partir de la infomracion proporcionada del sembrado.
            </summary>
            <param name="vialidades"></param>
            <param name="asentamientos"></param>
            <param name="vivienda"></param>
            <param name="esNuevo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.AgregarVialidadCompleto(RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda,System.Int32,System.String,System.Int32)">
            <summary>
            Agrega una vialidad deltipo especificado
            </summary>
            <param name="vivienda"></param>
            <param name="tipoVialidad"></param>
            <param name="nombreVialidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.ValidarMismoDomicilioViviendas(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda})">
            <summary>
            Valida que las viviendas no tengan el mismo domicilio
            </summary>
            <param name="viviendas"></param>
            <returns>Valor que indica si al menos dos viviendas tiene el mismo domicilio.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectos.compararDomiciliosViviendas(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda},RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda)">
            <summary>
            Compara los domicilios de las viviendas
            </summary>
            <param name="viviendas"></param>
            <param name="vivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerZonasDeRiesgoAsync">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerCatalogoPrototiposxEmpresa(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerCatalogoProyectosxEmpresa(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerCatalogoVialidadesFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerCatalogoasentamientosFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerCatalogoLocalidadesFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerCatalogoCPFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerVialidadesFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion.ServicioProyectosCatalogos.ObtenerAsentamientosFiltradoAsync(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ActualizarEstatusProyectoAsync(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Actualiza el estatus de un proyecto, manda y loguea excepcion si no puedo actualizarlo.
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="idEstatus">Identificador del estatus</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.EnviarDictaminacionProyecto(System.Int32,System.Int16,System.Int32,RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion,RUV.Comun.Seguridad.JWT.CustomUserRuv,System.Boolean)">
            <summary>
            Guarda/Actualiza la dictaminación, actualiza el estatus del proyecto y atiende la ODT.
            </summary>
            <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del proyecto</param>
            <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
            seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
            DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
            aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerViveindasReportePorProyectoAsync(System.Int32)">
            <summary>
            
            </summary>
            <param name="idProyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de ordenes de trabajo para un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerViviendasPorProyectoPaginadoAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            Obtiene el listado de viviendas para un proyecto por idProyecto paginado
            </summary>
            <param name="idProyecto">Identificador del proyecto</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">Pagina</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerCuvsFiltradasPaginadasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv,System.Int32,System.Int32)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada desde la BD del RUV++
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>
            <param name="tamanioPagina">Tamaño de la pagina</param>
            <param name="pagina">pagina consultada</param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerEquipamientosProyecto(System.Int32,System.Boolean)">
            <summary>
            Método para obtener los equipamientos de un proyecto.
            </summary>
            <param name="idProyecto">Identificador del proyecto.</param>
            <returns>Objeto con la lista de equipamientos del proyecto.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.EliminarViviendaTemporal(System.Int32)">
            <summary>
            Elimina una viveinda de la tabla temporal de Oracle
            </summary>
            <param name="featId"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.EliminarViviendaOficial(System.Int32)">
            <summary>
            Elimina una viveinda de la tabla oficial de Oracle
            </summary>
            <param name="featId"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerCuvsFiltradasAsync(RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv)">
            <summary>
            Obtiene el resultado de la consulta de CUVS filtrada y paginada
            </summary>
            <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.GuardarProyectoAsincronoAsync(RUV.RUVPP.Oferta.Modelo.Proyectos.Api.Proyecto,RUV.Comun.Seguridad.JWT.CustomUserRuv)">
            <summary>
            Agrega un mensaje a la queue del WebJob de Azure para procesar el guardado o actualizacion de un proyecto
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ObtenerEstadosMunicipiosViviendasAsync(System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.Tuple{System.String,System.String}})">
            <summary>
            Obtiene dos cadenas con los listados de los nombres de estados y municipios de las viviendas de un proyecto
            </summary>
            <param name="idsEstados">identificadores de estados</param>
            <param name="idsMunicipios">identificadores de municipios</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectos.ValidarProyecto(RUV.RUVPP.Oferta.Modelo.Proyectos.Api.Proyecto,System.Boolean,System.Boolean)">
            <summary>
            Valida la tipologia y espacio geografico del proyecto
            </summary>
            <param name="proyecto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerZonasDeRiesgoAsync">
            <summary>
            Obtiene los elementos del catalago de zonas de riesgo del proyecto.
            </summary>
            <returns>Elementos del catalogo.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerCatalogoPrototiposxEmpresa(System.Int32)">
            <summary>
            Obtiene los elementos del catalago para mostrar los prototipos por empresa.
            </summary>
            <returns>Elementos del catalogo.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerCatalogoProyectosxEmpresa(System.Int32)">
            <summary>
            Obtiene los elementos del catalago para mostrar los proyectos por empresa.
            </summary>
            <returns>Elementos del catalogo.</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerCatalogoVialidadesFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de vialidades de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerCatalogoasentamientosFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de asentamientos de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerVialidadesFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de vialidades de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerAsentamientosFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de asentamientos de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerCatalogoLocalidadesFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de localidadse de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Proyectos.IServicioProyectosCatalogos.ObtenerCatalogoCPFiltradoAsync(System.String,System.String)">
            <summary>
            Obtiene la lista de CP de un estado y municipio
            </summary>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ExcelPOI.CrearHojaPPS(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ReporteExcelPPS})">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ExcelPOI.CrearHojaPER(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ReporteExcelPER})">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ExcelPOI.WriteExcelWithNPOI(System.Collections.Generic.List{RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.Sheet},RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ExcelPOI.EXTENSION)">
            <summary>
            
            </summary>
            <param name="listHoja"></param>
            <param name="extension"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ExcelPOI.getValue(System.Object)">
            <summary>
            
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.Sheet">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad">
            <summary>
            Servicio Seguro de Calidad
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.#ctor(RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper,RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISeguroCalidadDataMapper__,RUV.RUVPP.Negocio.General.Seguridad.IServicioSeguridad,RUV.RUVPP.Negocio.General.Seguridad.ISeguridadAdmonUsuarios,RUV.RUVPP.Negocio.Empresa.EmpresaConsulta.IServicioEmpresaConsulta,RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales,RUV.RUVPP.Negocio.Empresa.Comun.IServicioEmpresaComun,RUV.RUVPP.Negocio.General.Notificaciones.Interfaces.INotificacion{RUV.RUVPP.Entidades.General.Notificaciones.MensajeDto},RUV.RUVPP.Oferta.Datos.Empresa.IEmpresaDataMapper,RUV.RUVPP.Oferta.Reportes.IServicioReportes,RUV.RUVPP.Oferta.Datos.SeguroCalidad.ISAPDataMapper)">
            
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerAseguradoraConPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene una lista de aseguradoras con su relacion comercial
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="razonSocial"></param>
            <param name="noRegistroRUV"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.PasarAseguradoraPadronAsync(System.Collections.Generic.List{System.Int32},System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="listaAseguradoras"></param>
            <param name="enPadronAseguradora"></param>
            <param name="idUsuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerSancionesAsync(System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Obtiene las sanciones de una aseguradora
            </summary>
            <param name="idSancion"></param>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarSancionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Sancion,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="sancion"></param>
            <param name="idUsuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarSancionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Sancion,System.Int32)">
            <summary>
            
            </summary>
            <param name="sancion"></param>
            <param name="idUsuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.CerrarSancionesAbiertasAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.AsignarOVAseguradoraPorDefectoAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AsignacionPorDefectoAseguradora)">
            <summary>
            Asignación de aseguradora por defecto.
            </summary>
            <param name="asignacionPorDefectoAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerEstatusParametrosConfigs90DiasAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerInfoOrdenVerificacionAsync(System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerConsultaAseguradoraConPaginadoAsync(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Boolean})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="noRegistroRUV"></param>
            <param name="razonSocial"></param>
            <param name="ordenVerificacion"></param>
            <param name="noContrato"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <param name="fechaAceptacionInicio"></param>
            <param name="fechaAceptacionFinal"></param>
            <param name="idTipoAsignacion"></param>
            <param name="idOferenteExterno"></param>
            <param name="idAseguradoraExterno"></param>
            <param name="idEstatusPagoEvaluacionRiesgo"></param>
            <param name="soloRelacionesComercialesbool"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCuvSinPolizaConPaginadoAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Boolean})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <param name="idOferente"></param>
            <param name="cambioValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCuvConPolizaConPaginadoAsync(System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <param name="idOferente"></param>
            <param name="idAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.AgregarPolizaAsync(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.CuvPoliza},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="listaCuvPoliza"></param>
            <param name="idUsuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarRelacionComercialAsync(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Aseguradora},System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="listaAseguradora"></param>
            <param name="idUsuario"></param>
            <param name="idEmpresainst"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarRelacionComercialAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.RelacionComercial,System.Nullable{System.Int32})">
            <summary>
            Actualiza la relacion comercial en todo los sentidos, puede cambiar de estatus los pagos y las evaluaciones de riesgo
            </summary>
            <param name="relacion"></param>
            <param name="idUsuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarRelacionComercialXRechazoAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.RelacionComercial,System.Nullable{System.Int32})">
            <summary>
            Actualiza la relacion comercial en todo los sentidos, puede cambiar de estatus los pagos y las evaluaciones de riesgo
            </summary>
            <param name="relacion"></param>
            <param name="idUsuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.DocumentoPolizaCuv(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarDatosRespuestaSiniestro(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.RespuestaSiniestro)" -->
        <!-- Badly formed XML comment ignored for member "M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarDatosRespuestaSiniestro(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.RespuestaSiniestro)" -->
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarSiniestrosAbiertos">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarDatosValorAvaluo(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarDatosValorAvaluoRecalculo(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarDatosValorAvaluoRecalculoIva(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EliminarPoliza(System.String)">
            <summary>
            Se recibe el valor avaluo y se elimina la poliza en caso de 
            </summary>
            <param name="Cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarSeguroVivienda(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosVivienda)">
            <summary>
            
            </summary>
            <param name="datosVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarDatosSiniestro(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.RespuestaSiniestro)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarDatosAvaluo(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarDatosCuv(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosValorAvaluo)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoNuevaIncidenciaVerificadorAseguradora(System.Int32,System.String,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,System.String,System.Boolean,System.String)">
            <summary>
            Envia una notificacion al verificador indicando el registro de una nueva incidencia
            </summary>
            <param name="idIncidencia"></param>
            <param name="ordenVerificacion"></param>
            <param name="aseguradora"></param>
            <param name="verificador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoNuevaIncidenciaAElMismo(System.Int32,System.String,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,System.String,System.Boolean,System.String)">
            <summary>
            Envia una notificacion a la aseguradora indicando el registro de una nueva incidencia
            </summary>
            <param name="idIncidencia"></param>
            <param name="ordenVerificacion"></param>
            <param name="aseguradora"></param>
            <param name="desarrollador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoNuevaIncidenciaDesarrollador(System.Int32,System.String,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,System.String,System.Boolean,System.String)">
            <summary>
            Envia una notificacion al desarrollador indicando el registro de una nueva incidencia
            </summary>
            <param name="idIncidencia"></param>
            <param name="ordenVerificacion"></param>
            <param name="aseguradora"></param>
            <param name="desarrollador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoCierreIncidenciaDesarrollador(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.IncidenciaGestion},RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,System.Int32)">
            <summary>
            
            </summary>
            <param name="listaIncidencias"></param>
            <param name="aseguradora"></param>
            <param name="desarrollador"></param>
            <param name="idEntidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoMitigacionVerificador(System.String,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.IncidenciaGestion})">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="verificador"></param>
            <param name="desarrollador"></param>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoMitigacionAseguradora(System.String,RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.IncidenciaGestion},System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="aseguradora"></param>
            <param name="desarrollador"></param>
            <param name="idIncidencia"></param>
            <param name="tipoIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoRechazoIncidenciaDesarrollador(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.IncidenciaGestion},RUV.RUVPP.Entidades.Empresa.EmpresaDto,RUV.RUVPP.Entidades.Empresa.EmpresaDto)">
            <summary>
            Envia una notificacion al desarrollador indicando el rechazo de una incidencia
            </summary>
            <param name="idIncidencia"></param>
            <param name="ordenVerificacion"></param>
            <param name="aseguradoraVerificador"></param>
            <param name="desarrollador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarFichaPagoEvaluacion(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.FichaPagoRiesgo)">
            <summary>
            Metodo para la generacion de ficha de pago de la evaluacion de riesgo
            </summary>
            <param name="fichaPagoRiesgo">Objeto con los datos necesarios para la generacion de la ficha de pago</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerListaPrecios(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.PrecioViviendas})">
            <summary>
            Metodo para obtener la lista de precios para enviarla en el metodo de generacion de ficha
            </summary>
            <param name="precioViviendas">Objeto con los datos de precio de vivienda</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerOrdenesDeVerificacionAsync(System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <param name="ordenVerificacion"></param>
            <param name="noContrato"></param>
            <param name="idEmpresaInstAseguradora"></param>
            <param name="idTipoAsignacion"></param>
            <param name="razonSocialAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerRelacionesComercialesAsync(System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <param name="idEmpresaInst"></param>
            <param name="nombreRazonSocial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCostoEvaluacionRiesgo(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCostoEvaluacionRiesgoD(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarSaldoEvaluacionRiesgo(System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="idRUVAsis"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarRelacionesComercialOrdenAsync(System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="idAseguradora"></param>
            <param name="idRelacionComercial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GuardarRelacionesComercialPrepagoOrdenAsync(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="idAseguradora"></param>
            <param name="idRelacionComercial"></param>
            <param name="idRUVAsIs"></param>
            <param name="idUsuario"></param>
            <param name="monto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarSaldoRelacionComercialAsync(System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="idAseguradora"></param>
            <param name="idRelacionComercial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnvioCorreoAseguradoraAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.EnvioCorreoConfirmacion)">
            <summary>
            
            </summary>
            <param name="envioCorreoConfirmacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarCorreoAseguradoraCuvsBloqueadasAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.NotificarAceptacionOVPorVerificadorAsync(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerRelacionComercialOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerViviendasOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ConsultarDatosGeneralesEmpresa(System.Int32)">
            <summary>
            
            </summary>
            <param name="idRuvAsIs"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarCostoPolizaReCalculoAsync(System.String,System.String,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.CreditoVivienda)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarCostoPolizaAsync(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarCostoPolizaRecalculoIvaAsync(System.String,System.String,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.CreditoVivienda)">
            <summary>
            
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCostoPoliza(System.Decimal,System.Decimal,System.Decimal,System.Boolean)">
            <summary>
            
            </summary>
            <param name="costoEvaluacionRiesgo"></param>
            <param name="costoAvaluo"></param>
            <param name="costoRelacion"></param>
            <param name="criterioSinInicioObra"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCostoDiferencia(System.Decimal,System.Decimal)">
            <summary>
            
            </summary>
            <param name="costoOrdenVerificacion"></param>
            <param name="costoAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.RestarMontoPagadoDiferencia(System.Decimal,System.Decimal)">
            <summary>
            
            </summary>
            <param name="costoDiferencia"></param>
            <param name="pagosRealizadosDiferencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCostoEvaluacion(System.Decimal)">
            <summary>
            
            </summary>
            <param name="costoVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerCostoEvaluacionSinIva(System.Decimal)">
            <summary>
            
            </summary>
            <param name="costoVivienda"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.AgregarIvaMontoTotalAPagar(System.Decimal)">
            <summary>
            
            </summary>
            <param name="calculoMontoTotalAPagar"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.TruncarDecimales(System.Decimal)">
            <summary>
            
            </summary>
            <param name="cantidadATruncar"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.RecalcularCostoPoliza">
            <summary>
            Metodo para la recalcular el costo de polizas cuando se tiene el monto del valor avaluo
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.RecalcularIva">
            <summary>
            Metodo para la recalcular el costo de polizas cuando se tiene el monto del valor avaluo
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.RecalcularCostosConIva">
            <summary>
            Metodo para la recalcular el costo de polizas cuando se tiene el monto del valor avaluo
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarDatosViviendaCredi(RUV.RUVPP.Oferta.Modelo.Oferta.Api.EstatusCreditoVivienda)">
            <summary>
            
            </summary>
            <param name="datosValorAvaluo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarPolizaVivienda(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarDocumentoPoliza(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.PeticionSolicitudPoliza,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.UrlPoliza)">
            <summary>
            Metodo para la recalcular el costo de polizas cuando se tiene el monto del valor avaluo
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.SolicitarPolizaAseguradora(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosGeneracionPoliza,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.UrlPoliza,System.String)">
            <summary>
            
            </summary>
            <param name="datosGeneracionPoliza_"></param>
            <param name="urlPoliza"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.SolicitarPagoDiferenciaOV(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.PagoDiferenciaOV,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DesarrolladorRelacion)">
            <summary>
            
            </summary>
            <param name="pagoDiferenciaOV"></param>
            <param name="verificador"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerPagoDiferenciaOV(System.Int32,System.Int32,System.String,System.Int32,System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="idEmpresa"></param>
            <param name="idEstatusPago"></param>        
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerConsultaPagoDiferenciaOV(System.Int32,System.Int32,System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="idEstatusPago"></param>
            <param name="idEmpresaInst"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnvioCorreoAseguradoraAsync(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnvioCorreoAseguradoraDocMinAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnvioCorreoAseguradoraReporteInicialAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnvioCorreoAseguradoraReporteDocumentalAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnvioCorreoAseguradoraReporteHabitabilidadAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.GenerarFichaPagoDiferenciaPorCUV(System.String)">
            <summary>
            Genera una ficha de pago de diferencia de Orden de Verificación usando solo la CUV
            </summary>
            <param name="cuv">CUV de la vivienda</param>
            <returns>Resultado de la operación</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarSiniestro(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Siniestro)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerPaginadoSiniestroContacto(System.Int32,System.Int32,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.SiniestroContacto)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarSiniestroContacto(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.SiniestroContacto)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerPaginadoSiniestroRespuestaAseguradora(System.Int32,System.Int32,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.SiniestroRespuestaAseguradora)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="entidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ActualizarSiniestroRespuestaAseguradora(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.SiniestroRespuestaAseguradora)">
            <summary>
            
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarInformacionSiniestroCuv(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.SiniestroAseguradora)">
            <summary>
            
            </summary>
            <param name="idAseguradora"></param>
            <param name="siniestroAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.CargarDatosFichaPagoPolizaAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ConfirmacionPagosAseguradoraAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ConfirmacionPagoAseguradora)">
            <summary>
            
            </summary>
            <param name="confirmacionPagoAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerDatosConfirmacionAsync">
            <summary>
            
            </summary>
            <param name="confirmacionPagoAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarPagosAseguradoraPolizaAsync">
            <summary>
            
            </summary>
            <param name="confirmacionPagoAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.EnviarPagosAseguradoraEvaluacionAsync">
            <summary>
            
            </summary>
            <param name="confirmacionPagoAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ValidarMontoPrepago(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.ConfirmarPrepago,System.Boolean)">
            <summary>
            
            </summary>
            <param name="confirmarPrepago"></param>
            <param name="esCargoPrepago"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ConfirmarPrepagoEvaluacionRiesgo(System.String,System.Int32,System.Nullable{System.Int32},System.Decimal)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="idRUVAsIs"></param>
            <param name="idUsuario"></param>
            <param name="monto"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ConfirmarPrepago(System.Nullable{System.Int32},RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.CargoPrepago)">
            <summary>
            
            </summary>
            <param name="idUsuario"></param>
            <param name="cargoPrepago"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.AfectarSaldoPoliza(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AfectacionSaldo)">
            <summary>
            
            </summary>
            <param name="afectacionSaldo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.AfectarSaldoDiferencia(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AfectacionSaldo)">
            <summary>
            
            </summary>
            <param name="afectacionSaldo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.ObtenerLineaPoliza(System.String,System.Int32,System.Int32,System.Decimal,System.DateTime,System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Metodo para generar la linea de captura por producto y por servicio
            </summary>
            <param name="idEmpresa"></param>
            <param name="idEmpresaInst"></param>
            <param name="idProducto"></param>
            <param name="idServicio"></param>
            <param name="monto"></param>
            <param name="fechaVigencia"></param>
            <param name="numeroAcreedorSap"></param>
            <param name="idReferencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.DigitoVerificador(System.String)">
            <summary>
            
            </summary>
            <param name="linea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion.ServicioSeguroCalidad.RealizarCargoPrepagoSAPSC(RUV.RUVPP.Oferta.Modelo.Oferta.Api.FichaPagoDTO,RUV.RUVPP.Oferta.Modelo.Oferta.Api.EmpresaDto,System.String,System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="fichaDTO"></param>
            <param name="datosEmpresaDto"></param>
            <param name="claveSAP"></param>
            <param name="monto"></param>
            <param name="usuario"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GenerarFichaPagoDiferenciaPorCUV(System.String)">
            <summary>
            Genera una ficha de pago de diferencia de Orden de Verificación usando solo la CUV
            </summary>
            <param name="cuv">CUV de la vivienda</param>
            <returns>Resultado de la operación</returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GuardarDatosTitulacionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DatosTitulacionINFONAVIT)">
            <summary>
            Guarda los datos de titulación enviados por el INFONAVIT
            </summary>
            <param name="datosTitulacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerTipoIncidenciasAsync">
            <summary>
            Obtiene el catalogo de tipo de incidencias
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerEstatusIncidenciasAsync">
            <summary>
            Obtiene el catalogo de estatus de incidencias
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerClasificacionIncidenciasAsync">
            <summary>
            Obtiene el catalogo de clasificacion de incidencias
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerEstatusPagoEvaluacionRiesgoAsync">
            <summary>
            Obtiene el catalogo de estatus pago evaluacion riesgo
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCatalogoCoberturaAfectadaAsync">
            <summary>
            Obtiene el catalogo de cobertura afectada
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCatalogoClasificacioRiesgoAsync">
            <summary>
            Obtiene el catalogo de clasificacion de riesos
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerIncidenciasGestionAsync(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerIncidenciasGestionSinPaginadoAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerOrdenesVerificacionAsync(System.String,System.String)">
            <summary>
            Obtiene una lista con las claves de las ordenes de verificacion con los parametros especificados
            </summary>
            <param name="idEntidad"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerOrdenesVerificacionConsultaAsync(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="noRuv"></param>
            <param name="razonSocial"></param>
            <param name="rfc"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerInformacionOVNuevaIncidenciaAsync(System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene la informacion del encabezado mostrado en el registro de una nueva incidencia/notificacion
            </summary>
            <param name="OrdenVerificacion"></param>
            <param name="cuv"></param>
            <param name="idEntidad"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ValidarCuvValidaAsync(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Verifica que una cuv especifica pertenezca a un verificador o bien tenga una relacion comercial aseuradora - desarrollador, dependiendo del perfil del usuario firmado
            </summary>
            <param name="cuv"></param>
            <param name="idEntidad"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCuvsXOVAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCuvsXOVPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV paginado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GuardarIncidenciaNotificacionAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.Incidencia,System.Int32)">
            <summary>
            Guarda una incidencia / notificacion
            </summary>
            <param name="incidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerInformacionPreviaNotificacionAsync(System.Int32,System.Int32)">
            <summary>
            Obtiene los documentos que pertenecen a una incidencia
            </summary>
            <param name="idIncidencia"></param>
            <param name="idVvGeneral"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCuvsXOVEvaluacionRiesgosAsync(System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV
            </summary>
            <param name="cuv"></param>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Obtiene las cuvs de una OV paginado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ValidarCuvValidaEvaluacionRiesgosAsync(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Verifica que una cuv especifica pertenezca a un verificador o bien tenga una relacion comercial aseuradora - desarrollador, dependiendo del perfil del usuario firmado
            </summary>
            <param name="cuv"></param>
            <param name="idEntidad"></param>
            <param name="idEmpresa"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GuardarEvaluacionRiesgoAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.EvaluacionRiesgo)">
            <summary>
            Guarda la relacion de una evaluacion de riesgo con una ov
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerDetalleIncidenciaAsync(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Obtiene la informacion general de una incidencia
            </summary>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerDatosMitigacionAsync(System.Nullable{System.Int32})">
            <summary>
            Obtiene los datos mostrados en ek registro de la mmitigacion de incidencia
            </summary>
            <param name="idIncidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GuardarMitigacionIncidencia(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Mitigacion,System.String)">
            <summary>
            
            </summary>
            <param name="mitigacion"></param>
            <param name="idEmpresainst"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.CerrarIncidenciaAsync(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.IncidenciaGestion},System.Int32,System.String,System.Int32)">
            <summary>
            
            </summary>
            <param name="listaIncidencias"></param>
            <param name="idUsuario"></param>
            <param name="idEmpresaInst"></param>
            <param name="idEntidad"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.RechazarMitigacionAsync(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.IncidenciaGestion})">
            <summary>
            Rechaza una mitigacion de incidencia
            </summary>
            <param name="incidencia"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GenerarFichaPagoEvaluacion(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.FichaPagoRiesgo)">
            <summary>
            Metodo para generar la ficha de pago de la evaluacion de riesgo
            </summary>
            <param name="fichaPagoRiesgo">Objeto que contiene el id de la empresa, id de la orden de verificacion y los precios de las viviendas que conforman la orden.</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerCostoEvaluacionRiesgo(System.String)">
            <summary>
            
            </summary>
            <param name="ordenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerOrdenesDeVerificacionAsync(System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.Nullable{System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <param name="ordenVerificacion"></param>
            <param name="noContrato"></param>
            <param name="idEmpresaInstAseguradora"></param>
            <param name="idTipoAsignacion"></param>
            <param name="razonSocialAseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerRelacionesComercialesAsync(System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idRuvAsis"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GuardarRelacionesComercialOrdenAsync(System.String,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <param name="idAseguradora"></param>
            <param name="idRelacionComercial"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerRelacionComercialOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerViviendasOrdenAsync(System.String)">
            <summary>
            
            </summary>
            <param name="idOrdenVerificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GenerarDocumentoIncidenciaNotificacionCorreo(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.CuvSeleccionada},System.String)">
            <summary>
            
            </summary>
            <param name="listaCuvs"></param>
            <param name="tipoIncidenciaNotificacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ConsultarDatosGeneralesEmpresa(System.Int32)">
            <summary>
            
            </summary>
            <param name="idRuvAsIs"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerIncidenciasInternoGestionAsync(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <param name="verificador"></param>
            <param name="aseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.ObtenerIncidenciasInternoGestionSinPaginadoAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Obtiene una lista de incidencias de acuerdo a los parametros especificados.
            </summary>
            <param name="ordenVerificacion"></param>
            <param name="cuv"></param>
            <param name="desarrollador"></param>
            <param name="claveIncidencia"></param>
            <param name="idTipoIncidencia"></param>
            <param name="idEstatusIncidencia"></param>
            <param name="idClasificacionIncidencia"></param>
            <param name="esFechaRegistro"></param>
            <param name="esFechaAtencion"></param>
            <param name="fechaInicial"></param>
            <param name="fechaFinal"></param>
            <param name="verificador"></param>
            <param name="aseguradora"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.EnvioCorreoAseguradoraAsync(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.EnvioCorreoConfirmacion)">
            <summary>
            
            </summary>
            <param name="envioCorreoConfirmacion"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.RecalcularCostoPoliza">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GenerarDocumentoPoliza(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.PeticionSolicitudPoliza,RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data.UrlPoliza)">
            <summary>
            
            </summary>
            <param name="datosGeneracionPoliza"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.GenerarPolizaVivienda(System.Int32)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.EnvioCorreoAseguradoraAsync(System.String)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.EnvioCorreoAseguradoraDocMinAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.EnvioCorreoAseguradoraReporteInicialAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.EnvioCorreoAseguradoraReporteDocumentalAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.EnvioCorreoAseguradoraReporteHabitabilidadAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.AfectarSaldoPoliza(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AfectacionSaldo)">
            <summary>
            
            </summary>
            <param name="afectacionSaldo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.SeguroCalidad.IServicioSeguroCalidad.AfectarSaldoDiferencia(RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.AfectacionSaldo)">
            <summary>
            
            </summary>
            <param name="afectacionSaldo"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.cuv">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.id_avaluo">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.id_foto">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.foto">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.id_perito_shf">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.usuarioVO">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto.metadatosVO">
            <remarks/>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoUsuarioVO">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoUsuarioVO.id_usuario">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoUsuarioVO.contrasenia">
            <remarks/>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res.folio">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res.codigo_recepcion">
            <remarks/>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoMetadatosVO">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoMetadatosVO.latitud">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoMetadatosVO.longitud">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoMetadatosVO.fechaVisitaInmueble">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoMetadatosVO.modeloTelefono">
            <remarks/>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFotoMetadatosVO.versionSO">
            <remarks/>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.create(System.Type)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.getPropertyValue(System.Object,System.String)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="property"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.setPropertyValue(System.Object,System.String,System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="property"></param>
            <param name="value"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.setPropertyValue(System.Object,System.Reflection.PropertyInfo,System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="propertyInfo"></param>
            <param name="value"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.getPropertys(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.getProperty(System.Object,System.String)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <param name="property"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isString(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isDecimal(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isDouble(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isLong(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isInt32(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isChar(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isDateTime(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isBoolean(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.Reflections.isByte(System.Reflection.PropertyInfo)">
            <summary>
            
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.AppSettings(System.String)">
            <summary>
            
            </summary>
            <param name="appSettings"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.getConnection(System.String)">
            <summary>
            
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.Insert(System.String,Microsoft.WindowsAzure.Storage.Table.TableEntity)">
            <summary>
            
            </summary>
            <param name="tableName"></param>
            <param name="obj"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.Insert``1(System.String,System.Collections.Generic.List{``0})">
            <summary>
            
            </summary>
            <typeparam name="T"></typeparam>
            <param name="tableName"></param>
            <param name="list"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.query(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="appSettings"></param>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.getEntity``1(Microsoft.WindowsAzure.Storage.Table.DynamicTableEntity,System.Type)">
            <summary>
            
            </summary>
            <param name="dynamicTableEntity"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.query(System.String)">
            <summary>
            
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Util.TableStorage.getListEntity``1(System.Collections.Generic.List{Microsoft.WindowsAzure.Storage.Table.DynamicTableEntity},System.Type)">
            <summary>
            
            </summary>
            <param name="listDynamicTableEntity"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Util.BitacoraApp">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Dominio.Viviendas.Implementacion.ServicioVivienda">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Viviendas.Implementacion.ServicioVivienda.#ctor(Microsoft.ApplicationInsights.TelemetryClient)">
            <summary>
            
            </summary>
            <param name="clienteTelemetria"></param>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Viviendas.Implementacion.ServicioVivienda.GenerarCuvs(System.Int32,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda},System.DateTime,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="idProyecto"></param>
            <param name="vivienda"></param>
            <param name="fechaRegitro"></param>
            <param name="idEstado"></param>
            <param name="idMunicipio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Viviendas.Implementacion.ServicioVivienda.GenerarCuvTabular(System.Int32,System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda},System.DateTime,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="idProyecto">id del proyecto</param>
            <param name="viviendas">Viviendas correspondientes al proyecto</param>
            <param name="fechaRegitro">Fecha de registro del proyecto</param>
            <param name="idEstado">id del municipio de 2 posiciones</param>
            <param name="idMunicipio">id del municipio de 3 posiciones</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Dominio.Viviendas.Implementacion.ServicioVivienda.GenerarCuvGeografica(System.Collections.Generic.List{RUV.RUVPP.Oferta.Modelo.Proyectos.Data.Vivienda})">
            <summary>
            Metodo para invocar el servicio de SIG para generar la CUV 
            </summary>
            <param name="viviendas">Viviendas pertenecientes al proyecto que se le generaran las CUVs</param>
            <returns>Regresa el listado de viviendas con las CUVs generadas</returns>
        </member>
        <member name="T:SqlServerTypes.Utilities">
            <summary>
            Utility methods related to CLR Types for SQL Server 
            </summary>
        </member>
        <member name="M:SqlServerTypes.Utilities.LoadNativeAssemblies(System.String)">
            <summary>
            Loads the required native assemblies for the current architecture (x86 or x64)
            </summary>
            <param name="rootApplicationPath">
            Root path of the current application. Use Server.MapPath(".") for ASP.NET applications
            and AppDomain.CurrentDomain.BaseDirectory for desktop applications.
            </param>
        </member>
    </members>
</doc>
