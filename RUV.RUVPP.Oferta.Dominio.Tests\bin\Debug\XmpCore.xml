<?xml version="1.0"?>
<doc>
    <assembly>
        <name>XmpCore</name>
    </assembly>
    <members>
        <member name="T:XmpCore.Impl.ByteBuffer">
            <summary>Byte buffer container including length of valid data.</summary>
            <author><PERSON></author>
            <since>11.10.2006</since>
        </member>
        <member name="P:XmpCore.Impl.ByteBuffer.Length">
            <value>
            Returns the length, that means the number of valid bytes, of the buffer;
            the inner byte array might be bigger than that.
            the inner byte array might be bigger than that.
            </value>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.#ctor(System.Int32)">
            <param name="initialCapacity">the initial capacity for this buffer</param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.#ctor(System.Byte[])">
            <param name="buffer">a byte array that will be wrapped with <c>ByteBuffer</c>.</param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.#ctor(System.Byte[],System.Int32)">
            <param name="buffer">a byte array that will be wrapped with <c>ByteBuffer</c>.</param>
            <param name="length">the length of valid bytes in the array</param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.#ctor(System.IO.Stream)">
            <summary>Loads the stream into a buffer.</summary>
            <param name="stream">an Stream</param>
            <exception cref="T:System.IO.IOException">If the stream cannot be read.</exception>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.#ctor(System.Byte[],System.Int32,System.Int32)">
            <param name="buffer">a byte array that will be wrapped with <c>ByteBuffer</c>.</param>
            <param name="offset">the offset of the provided buffer.</param>
            <param name="length">the length of valid bytes in the array</param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.GetByteStream">
            <returns>Returns a byte stream that is limited to the valid amount of bytes.</returns>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.ByteAt(System.Int32)">
            <param name="index">the index to retrieve the byte from</param>
            <returns>Returns a byte from the buffer</returns>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.CharAt(System.Int32)">
            <param name="index">the index to retrieve a byte as int or char.</param>
            <returns>Returns a byte from the buffer</returns>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.Append(System.Byte)">
            <summary>Appends a byte to the buffer.</summary>
            <param name="b">a byte</param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.Append(System.Byte[],System.Int32,System.Int32)">
            <summary>Appends a byte array or part of to the buffer.</summary>
            <param name="bytes">a byte array</param>
            <param name="offset">an offset with</param>
            <param name="len" />
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.Append(System.Byte[])">
            <summary>Append a byte array to the buffer</summary>
            <param name="bytes">a byte array</param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.Append(XmpCore.Impl.ByteBuffer)">
            <summary>Append another buffer to this buffer.</summary>
            <param name="anotherBuffer">another <c>ByteBuffer</c></param>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.GetEncoding">
            <summary>Detects the encoding of the byte buffer, stores and returns it.</summary>
            <remarks>
            Detects the encoding of the byte buffer, stores and returns it.
            Only UTF-8, UTF-16LE/BE and UTF-32LE/BE are recognized.
            </remarks>
            <returns>Returns the encoding string.</returns>
        </member>
        <member name="M:XmpCore.Impl.ByteBuffer.EnsureCapacity(System.Int32)">
            <summary>
            Ensures the requested capacity by increasing the buffer size when the
            current length is exceeded.
            </summary>
            <param name="requestedLength">requested new buffer length</param>
        </member>
        <member name="T:XmpCore.Impl.FixAsciiControlsReader">
            <author>Stefan Makswit</author>
            <since>22.08.2006</since>
        </member>
        <member name="F:XmpCore.Impl.FixAsciiControlsReader._state">
            <summary>the state of the automaton</summary>
        </member>
        <member name="F:XmpCore.Impl.FixAsciiControlsReader._control">
            <summary>the result of the escaping sequence</summary>
        </member>
        <member name="F:XmpCore.Impl.FixAsciiControlsReader._digits">
            <summary>count the digits of the sequence</summary>
        </member>
        <member name="M:XmpCore.Impl.FixAsciiControlsReader.#ctor(System.IO.StreamReader)">
            <summary>The look-ahead size is 6 at maximum (&amp;#xAB;)</summary>
            <seealso cref="M:Sharpen.PushbackReader.#ctor(System.IO.StreamReader,System.Int32)" />
            <param name="reader">a Reader</param>
        </member>
        <member name="M:XmpCore.Impl.FixAsciiControlsReader.Read(System.Char[],System.Int32,System.Int32)">
            <exception cref="T:System.IO.IOException" />
        </member>
        <member name="M:XmpCore.Impl.FixAsciiControlsReader.ProcessChar(System.Char)">
            <summary>Processes numeric escaped chars to find out if they are a control character.</summary>
            <param name="ch">a char</param>
            <returns>Returns the char directly or as replacement for the escaped sequence.</returns>
        </member>
        <member name="T:XmpCore.Impl.Iso8601Converter">
            <summary>Converts between ISO 8601 Strings and <c>Calendar</c> with millisecond resolution.</summary>
            <author>Stefan Makswit</author>
            <since>16.02.2006</since>
        </member>
        <member name="M:XmpCore.Impl.Iso8601Converter.Parse(System.String)">
            <summary>Converts an ISO 8601 string to an <c>XMPDateTime</c>.</summary>
            <remarks>
            Converts an ISO 8601 string to an <c>XMPDateTime</c>.
            Parse a date according to ISO 8601 and
            http://www.w3.org/TR/NOTE-datetime:
            <list type="bullet">
            <item>YYYY</item>
            <item>YYYY-MM</item>
            <item>YYYY-MM-DD</item>
            <item>YYYY-MM-DDThh:mmTZD</item>
            <item>YYYY-MM-DDThh:mm:ssTZD</item>
            <item>YYYY-MM-DDThh:mm:ss.sTZD</item>
            </list>
            Data fields:
            <list type="bullet">
            <item>YYYY = four-digit year</item>
            <item>MM = two-digit month (01=January, etc.)</item>
            <item>DD = two-digit day of month (01 through 31)</item>
            <item>hh = two digits of hour (00 through 23)</item>
            <item>mm = two digits of minute (00 through 59)</item>
            <item>ss = two digits of second (00 through 59)</item>
            <item>s = one or more digits representing a decimal fraction of a second</item>
            <item>TZD = time zone designator (Z or +hh:mm or -hh:mm)</item>
            </list>
            Note that ISO 8601 does not seem to allow years less than 1000 or greater
            than 9999. We allow any year, even negative ones. The year is formatted
            as "%.4d".
            <para />
            <em>Note:</em> Tolerate missing TZD, assume is UTC. Photoshop 8 writes
            dates like this for exif:GPSTimeStamp.
            <para />
            <em>Note:</em> DOES NOT APPLY ANYMORE.
            Tolerate missing date portion, in case someone foolishly
            writes a time-only value that way.
            </remarks>
            <param name="iso8601String">a date string that is ISO 8601 conform.</param>
            <returns>Returns a <c>Calendar</c>.</returns>
            <exception cref="T:XmpCore.XmpException">Is thrown when the string is non-conform.</exception>
        </member>
        <member name="M:XmpCore.Impl.Iso8601Converter.Parse(System.String,XmpCore.IXmpDateTime)">
            <param name="iso8601String">a date string that is ISO 8601 conform.</param>
            <param name="binValue">an existing XMPDateTime to set with the parsed date</param>
            <returns>Returns an XMPDateTime-object containing the ISO8601-date.</returns>
            <exception cref="T:XmpCore.XmpException">Is thrown when the string is non-conform.</exception>
        </member>
        <member name="M:XmpCore.Impl.Iso8601Converter.Render(XmpCore.IXmpDateTime)">
            <summary>Converts a <c>Calendar</c> into an ISO 8601 string.</summary>
            <remarks>
            Converts a <c>Calendar</c> into an ISO 8601 string.
            Format a date according to ISO 8601 and http://www.w3.org/TR/NOTE-datetime:
            <list type="bullet">
            <item>YYYY</item>
            <item>YYYY-MM</item>
            <item>YYYY-MM-DD</item>
            <item>YYYY-MM-DDThh:mmTZD</item>
            <item>YYYY-MM-DDThh:mm:ssTZD</item>
            <item>YYYY-MM-DDThh:mm:ss.sTZD</item>
            </list>
            Data fields:
            <list type="bullet">
            <item>YYYY = four-digit year</item>
            <item>MM     = two-digit month (01=January, etc.)</item>
            <item>DD     = two-digit day of month (01 through 31)</item>
            <item>hh     = two digits of hour (00 through 23)</item>
            <item>mm     = two digits of minute (00 through 59)</item>
            <item>ss     = two digits of second (00 through 59)</item>
            <item>s     = one or more digits representing a decimal fraction of a second</item>
            <item>TZD     = time zone designator (Z or +hh:mm or -hh:mm)</item>
            </list>
            <para />
            <em>Note:</em> ISO 8601 does not seem to allow years less than 1000 or greater than 9999.
            We allow any year, even negative ones. The year is formatted as "%.4d".
            <para />
            <em>Note:</em> Fix for bug 1269463 (silently fix out of range values) included in parsing.
            The quasi-bogus "time only" values from Photoshop CS are not supported.
            </remarks>
            <param name="dateTime">an XMPDateTime-object.</param>
            <returns>Returns an ISO 8601 string.</returns>
        </member>
        <member name="T:XmpCore.Impl.ParseState">
            <author>Stefan Makswit</author>
            <since>22.08.2006</since>
        </member>
        <member name="P:XmpCore.Impl.ParseState.Pos">
            <returns>Returns the current position.</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseState.#ctor(System.String)">
            <param name="str">initializes the parser container</param>
        </member>
        <member name="P:XmpCore.Impl.ParseState.HasNext">
            <value>Returns whether there are more chars to come.</value>
        </member>
        <member name="M:XmpCore.Impl.ParseState.Ch(System.Int32)">
            <param name="index">index of char</param>
            <returns>Returns char at a certain index.</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseState.Ch">
            <returns>Returns the current char or 0x0000 if there are no more chars.</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseState.Skip">
            <summary>Skips the next char.</summary>
        </member>
        <member name="M:XmpCore.Impl.ParseState.GatherInt(System.String,System.Int32)">
            <summary>Parses a integer from the source and sets the pointer after it.</summary>
            <param name="errorMsg">Error message to put in the exception if no number can be found</param>
            <param name="maxValue">the max value of the number to return</param>
            <returns>Returns the parsed integer.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown if no integer can be found.</exception>
        </member>
        <member name="T:XmpCore.Impl.Latin1Converter">
            <author>Stefan Makswit</author>
            <since>12.10.2006</since>
        </member>
        <member name="M:XmpCore.Impl.Latin1Converter.Convert(XmpCore.Impl.ByteBuffer)">
            <summary>A converter that processes a byte buffer containing a mix of UTF8 and Latin-1/Cp1252 chars.</summary>
            <remarks>
            A converter that processes a byte buffer containing a mix of UTF8 and Latin-1/Cp1252 chars.
            The result is a buffer where those chars have been converted to UTF-8;
            that means it contains only valid UTF-8 chars.
            <para />
            <em>Explanation of the processing:</em> First the encoding of the buffer is detected looking
            at the first four bytes (that works only if the buffer starts with an ASCII-char,
            like xmls &apos;&lt;&apos;). UTF-16/32 flavours do not require further proccessing.
            <para />
            In the case, UTF-8 is detected, it assumes wrong UTF8 chars to be a sequence of
            Latin-1/Cp1252 encoded bytes and converts the chars to their corresponding UTF-8 byte
            sequence.
            <para />
            The 0x80..0x9F range is undefined in Latin-1, but is defined in Windows code
            page 1252. The bytes 0x81, 0x8D, 0x8F, 0x90, and 0x9D are formally undefined
            by Windows 1252. These are in XML's RestrictedChar set, so we map them to a
            space.
            <para />
            The official Latin-1 characters in the range 0xA0..0xFF are converted into
            the Unicode Latin Supplement range U+00A0 - U+00FF.
            <para />
            <em>Example:</em> If an Euro-symbol (€) appears in the byte buffer (0xE2, 0x82, 0xAC),
            it will be left as is. But if only the first two bytes are appearing,
            followed by an ASCII char a (0xE2 - 0x82 - 0x41), it will be converted to
            0xC3, 0xA2 (â) - 0xE2, 0x80, 0x9A (‚) - 0x41 (a).
            </remarks>
            <param name="buffer">a byte buffer contain</param>
            <returns>Returns a new buffer containing valid UTF-8</returns>
        </member>
        <member name="M:XmpCore.Impl.Latin1Converter.ConvertToUtf8(System.Byte)">
            <summary>
            Converts a Cp1252 char (contains all Latin-1 chars above 0x80) into a
            UTF-8 byte sequence.
            </summary>
            <remarks>
            Converts a Cp1252 char (contains all Latin-1 chars above 0x80) into a
            UTF-8 byte sequence. The bytes 0x81, 0x8D, 0x8F, 0x90, and 0x9D are
            formally undefined by Windows 1252 and therefore replaced by a space
            (0x20).
            </remarks>
            <param name="ch">an Cp1252 / Latin-1 byte</param>
            <returns>Returns a byte array containing a UTF-8 byte sequence.</returns>
        </member>
        <member name="T:XmpCore.Impl.ParameterAsserts">
            <author>Stefan Makswit</author>
            <since>11.08.2006</since>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertArrayName(System.String)">
            <summary>Asserts that an array name is set.</summary>
            <param name="arrayName">an array name</param>
            <exception cref="T:XmpCore.XmpException">Array name is null or empty</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertPropName(System.String)">
            <summary>Asserts that a property name is set.</summary>
            <param name="propName">a property name or path</param>
            <exception cref="T:XmpCore.XmpException">Property name is null or empty</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertSchemaNs(System.String)">
            <summary>Asserts that a schema namespace is set.</summary>
            <param name="schemaNs">a schema namespace</param>
            <exception cref="T:XmpCore.XmpException">Schema is null or empty</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertPrefix(System.String)">
            <summary>Asserts that a prefix is set.</summary>
            <param name="prefix">a prefix</param>
            <exception cref="T:XmpCore.XmpException">Prefix is null or empty</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertSpecificLang(System.String)">
            <summary>Asserts that a specific language is set.</summary>
            <param name="specificLang">a specific lang</param>
            <exception cref="T:XmpCore.XmpException">Specific language is null or empty</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertStructName(System.String)">
            <summary>Asserts that a struct name is set.</summary>
            <param name="structName">a struct name</param>
            <exception cref="T:XmpCore.XmpException">Struct name is null or empty</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertNotNull(System.Object)">
            <summary>Asserts that a parameter is not null.</summary>
            <param name="param">the parameter's value</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the parameter is null.</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertNotNullOrEmpty(System.String)">
            <summary>Asserts that any string parameter is not null or empty.</summary>
            <param name="param">a string parameter's value</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the parameter is null or has length 0.</exception>
        </member>
        <member name="M:XmpCore.Impl.ParameterAsserts.AssertImplementation(XmpCore.IXmpMeta)">
            <summary>Asserts that the xmp object is of this implemention (<see cref="T:XmpCore.Impl.XmpMeta" />).</summary>
            <param name="xmp">the XMP object</param>
            <exception cref="T:XmpCore.XmpException">A wrong implentaion is used.</exception>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.Rdf">
            <summary>Start of coreSyntaxTerms.</summary>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.Datatype">
            <summary>End of coreSyntaxTerms</summary>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.Description">
            <summary>Start of additions for syntax Terms.</summary>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.Li">
            <summary>End of of additions for syntaxTerms.</summary>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.AboutEach">
            <summary>Start of oldTerms.</summary>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.BagId">
            <summary>End of oldTerms.</summary>
        </member>
        <member name="F:XmpCore.Impl.RdfTerm.FirstSyntax">
            <summary>! Yes, the syntax terms include the core terms.</summary>
        </member>
        <member name="T:XmpCore.Impl.ParseRdf">
            <summary>Parser for "normal" XML serialisation of RDF.</summary>
            <author>Stefan Makswit</author>
            <since>14.07.2006</since>
        </member>
        <member name="F:XmpCore.Impl.ParseRdf.DefaultPrefix">
            <summary>this prefix is used for default namespaces</summary>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Parse(System.Xml.Linq.XElement,XmpCore.Options.ParseOptions)">
            <summary>The main parsing method.</summary>
            <remarks>
            The main parsing method. The XML tree is walked through from the root node and and XMP tree
            is created. This is a raw parse, the normalisation of the XMP tree happens outside.
            </remarks>
            <param name="xmlRoot">the XML root node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <returns>Returns an XMP metadata object (not normalized)</returns>
            <exception cref="T:XmpCore.XmpException">Occurs if the parsing fails for any reason.</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_RDF(XmpCore.Impl.XmpMeta,System.Xml.Linq.XElement,XmpCore.Options.ParseOptions)">
            <summary>
            Each of these parsing methods is responsible for recognizing an RDF
            syntax production and adding the appropriate structure to the XMP tree.
            </summary>
            <remarks>
            Each of these parsing methods is responsible for recognizing an RDF
            syntax production and adding the appropriate structure to the XMP tree.
            They simply return for success, failures will throw an exception.
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="rdfRdfNode">the top-level xml node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_NodeElementList(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,XmpCore.Options.ParseOptions)">
            <summary>
            7.2.10 nodeElementList
            <para />
            ws* ( nodeElement ws* )
            </summary>
            <remarks>
            This method is only called from the rdf:RDF-node (top level).
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="rdfRdfNode">the top-level xml node</param>
            /// <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_NodeElement(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean,XmpCore.Options.ParseOptions)">
            <summary>
            7.2.5 nodeElementURIs
            anyURI - ( coreSyntaxTerms | rdf:li | oldTerms )
            7.2.11 nodeElement
            start-element ( URI == nodeElementURIs,
            attributes == set ( ( idAttr | nodeIdAttr | aboutAttr )?, propertyAttr* ) )
            propertyEltList
            end-element()
            A node element URI is rdf:Description or anything else that is not an RDF
            term.
            </summary>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_NodeElementAttrs(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean,XmpCore.Options.ParseOptions)">
            <remarks>
            7.2.7 propertyAttributeURIs
            anyURI - ( coreSyntaxTerms | rdf:Description | rdf:li | oldTerms )
            7.2.11 nodeElement
            start-element ( URI == nodeElementURIs,
            attributes == set ( ( idAttr | nodeIdAttr | aboutAttr )?, propertyAttr* ) )
            propertyEltList
            end-element()
            Process the attribute list for an RDF node element. A property attribute URI is
            anything other than an RDF term. The rdf:ID and rdf:nodeID attributes are simply ignored,
            as are rdf:about attributes on inner nodes.
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_PropertyElementList(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean,XmpCore.Options.ParseOptions)">
            <summary>
            7.2.13 propertyEltList
            ws* ( propertyElt ws* )
            </summary>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlParent">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_PropertyElement(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean,XmpCore.Options.ParseOptions)">
            <remarks>
            7.2.14 propertyElt
            resourcePropertyElt | literalPropertyElt | parseTypeLiteralPropertyElt |
            parseTypeResourcePropertyElt | parseTypeCollectionPropertyElt |
            parseTypeOtherPropertyElt | emptyPropertyElt
            7.2.15 resourcePropertyElt
            start-element ( URI == propertyElementURIs, attributes == set ( idAttr? ) )
            ws* nodeElement ws
            end-element()
            7.2.16 literalPropertyElt
            start-element (
            URI == propertyElementURIs, attributes == set ( idAttr?, datatypeAttr?) )
            text()
            end-element()
            7.2.17 parseTypeLiteralPropertyElt
            start-element (
            URI == propertyElementURIs, attributes == set ( idAttr?, parseLiteral ) )
            literal
            end-element()
            7.2.18 parseTypeResourcePropertyElt
            start-element (
            URI == propertyElementURIs, attributes == set ( idAttr?, parseResource ) )
            propertyEltList
            end-element()
            7.2.19 parseTypeCollectionPropertyElt
            start-element (
            URI == propertyElementURIs, attributes == set ( idAttr?, parseCollection ) )
            nodeElementList
            end-element()
            7.2.20 parseTypeOtherPropertyElt
            start-element ( URI == propertyElementURIs, attributes == set ( idAttr?, parseOther ) )
            propertyEltList
            end-element()
            7.2.21 emptyPropertyElt
            start-element ( URI == propertyElementURIs,
            attributes == set ( idAttr?, ( resourceAttr | nodeIdAttr )?, propertyAttr* ) )
            end-element()
            The various property element forms are not distinguished by the XML element name,
            but by their attributes for the most part. The exceptions are resourcePropertyElt and
            literalPropertyElt. They are distinguished by their XML element content.
            NOTE: The RDF syntax does not explicitly include the xml:lang attribute although it can
            appear in many of these. We have to allow for it in the attribute counts below.
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_ResourcePropertyElement(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean,XmpCore.Options.ParseOptions)">
            <remarks>
            7.2.15 resourcePropertyElt
            start-element ( URI == propertyElementURIs, attributes == set ( idAttr? ) )
            ws* nodeElement ws
            end-element()
            This handles structs using an rdf:Description node,
            arrays using rdf:Bag/Seq/Alt, and typedNodes. It also catches and cleans up qualified
            properties written with rdf:Description and rdf:value.
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_LiteralPropertyElement(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean)">
            <summary>
            7.2.16 literalPropertyElt
            start-element ( URI == propertyElementURIs,
            attributes == set ( idAttr?, datatypeAttr?) )
            text()
            end-element()
            Add a leaf node with the text value and qualifiers for the attributes.
            </summary>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_ParseTypeLiteralPropertyElement">
            <summary>
            7.2.17 parseTypeLiteralPropertyElt
            start-element ( URI == propertyElementURIs,
            attributes == set ( idAttr?, parseLiteral ) )
            literal
            end-element()
            </summary>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_ParseTypeResourcePropertyElement(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean,XmpCore.Options.ParseOptions)">
            <remarks>
            7.2.18 parseTypeResourcePropertyElt
            start-element ( URI == propertyElementURIs,
            attributes == set ( idAttr?, parseResource ) )
            propertyEltList
            end-element()
            Add a new struct node with a qualifier for the possible rdf:ID attribute.
            Then process the XML child nodes to get the struct fields.
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <param name="options">ParseOptions to indicate the parse options provided by the client</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_ParseTypeCollectionPropertyElement">
            <summary>
            7.2.19 parseTypeCollectionPropertyElt
            start-element ( URI == propertyElementURIs,
            attributes == set ( idAttr?, parseCollection ) )
            nodeElementList
            end-element()
            </summary>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_ParseTypeOtherPropertyElement">
            <summary>
            7.2.20 parseTypeOtherPropertyElt
            start-element ( URI == propertyElementURIs, attributes == set ( idAttr?, parseOther ) )
            propertyEltList
            end-element()
            </summary>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.Rdf_EmptyPropertyElement(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.Boolean)">
            <remarks>
            7.2.21 emptyPropertyElt
            start-element ( URI == propertyElementURIs,
            attributes == set (
            idAttr?, ( resourceAttr | nodeIdAttr )?, propertyAttr* ) )
            end-element()
            &lt;ns:Prop1/&gt;  &lt;!-- a simple property with an empty value --&gt;
            &lt;ns:Prop2 rdf:resource="http: *www.adobe.com/"/&gt; &lt;!-- a URI value --&gt;
            &lt;ns:Prop3 rdf:value="..." ns:Qual="..."/&gt; &lt;!-- a simple qualified property --&gt;
            &lt;ns:Prop4 ns:Field1="..." ns:Field2="..."/&gt; &lt;!-- a struct with simple fields --&gt;
            An emptyPropertyElt is an element with no contained content, just a possibly empty set of
            attributes. An emptyPropertyElt can represent three special cases of simple XMP properties: a
            simple property with an empty value (ns:Prop1), a simple property whose value is a URI
            (ns:Prop2), or a simple property with simple qualifiers (ns:Prop3).
            An emptyPropertyElt can also represent an XMP struct whose fields are all simple and
            unqualified (ns:Prop4).
            It is an error to use both rdf:value and rdf:resource - that can lead to invalid  RDF in the
            verbose form written using a literalPropertyElt.
            The XMP mapping for an emptyPropertyElt is a bit different from generic RDF, partly for
            design reasons and partly for historical reasons. The XMP mapping rules are:
            <list type="bullet">
            <item> If there is an rdf:value attribute then this is a simple property
            with a text value.
            All other attributes are qualifiers.</item>
            <item> If there is an rdf:resource attribute then this is a simple property
            with a URI value.
            All other attributes are qualifiers.</item>
            <item> If there are no attributes other than xml:lang, rdf:ID, or rdf:nodeID
            then this is a simple
            property with an empty value.</item>
            <item> Otherwise this is a struct, the attributes other than xml:lang, rdf:ID,
            or rdf:nodeID are fields.</item>
            </list>
            </remarks>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.AddChildNode(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,System.Xml.Linq.XElement,System.String,System.Boolean)">
            <summary>Adds a child node.</summary>
            <param name="xmp">the xmp metadata object that is generated</param>
            <param name="xmpParent">the parent xmp node</param>
            <param name="xmlNode">the currently processed XML node</param>
            <param name="value">Node value</param>
            <param name="isTopLevel">Flag if the node is a top-level node</param>
            <returns>Returns the newly created child node.</returns>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.AddQualifierNode(XmpCore.Impl.XmpNode,System.String,System.String)">
            <summary>Adds a qualifier node.</summary>
            <param name="xmpParent">the parent xmp node</param>
            <param name="name">
            the name of the qualifier which has to be
            QName including the <b>default prefix</b>
            </param>
            <param name="value">the value of the qualifier</param>
            <returns>Returns the newly created child node.</returns>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.FixupQualifiedNode(XmpCore.Impl.XmpNode)">
            <summary>The parent is an RDF pseudo-struct containing an rdf:value field.</summary>
            <remarks>
            The parent is an RDF pseudo-struct containing an rdf:value field. Fix the
            XMP data model. The rdf:value node must be the first child, the other
            children are qualifiers. The form, value, and children of the rdf:value
            node are the real ones. The rdf:value node's qualifiers must be added to
            the others.
            </remarks>
            <param name="xmpParent">the parent xmp node</param>
            <exception cref="T:XmpCore.XmpException">thrown on parsing errors</exception>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.IsWhitespaceNode(System.Xml.Linq.XNode)">
            <summary>Checks if the node is a white space.</summary>
            <param name="node">an XML-node</param>
            <returns>
            Returns whether the node is a whitespace node,
            i.e. a text node that contains only whitespaces.
            </returns>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.IsPropertyElementName(XmpCore.Impl.RdfTerm)">
            <summary>
            7.2.6 propertyElementURIs
            anyURI - ( coreSyntaxTerms | rdf:Description | oldTerms )
            </summary>
            <param name="term">the term id</param>
            <returns>Return true if the term is a property element name.</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.IsOldTerm(XmpCore.Impl.RdfTerm)">
            <summary>
            7.2.4 oldTerms
            <para />
            rdf:aboutEach | rdf:aboutEachPrefix | rdf:bagID
            </summary>
            <param name="term">the term id</param>
            <returns>Returns true if the term is an old term.</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.IsCoreSyntaxTerm(XmpCore.Impl.RdfTerm)">
            <summary>
            7.2.2 coreSyntaxTerms
            <para />
            rdf:RDF | rdf:ID | rdf:about | rdf:parseType | rdf:resource | rdf:nodeID |
            rdf:datatype
            </summary>
            <param name="term">the term id</param>
            <returns>Return true if the term is a core syntax term</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.GetRdfTermKind(System.Xml.Linq.XElement)">
            <summary>Determines the ID for a certain RDF Term.</summary>
            <remarks>Arranged to hopefully minimize the parse time for large XMP.</remarks>
            <param name="node">an XML node</param>
            <returns>Returns the term ID.</returns>
        </member>
        <member name="M:XmpCore.Impl.ParseRdf.IsNumberedArrayItemName(System.String)">
            <summary>Check if the child name</summary>
            <param name="nodeName">an XML node</param>
            <returns>Returns bool</returns>
        </member>
        <member name="T:XmpCore.Impl.QName">
            <author>Stefan Makswit</author>
            <since>09.11.2006</since>
        </member>
        <member name="M:XmpCore.Impl.QName.#ctor(System.String)">
            <summary>Splits a qname into prefix and localname.</summary>
            <param name="qname">a QName</param>
        </member>
        <member name="M:XmpCore.Impl.QName.#ctor(System.String,System.String)">
            <summary>Constructor that initializes the fields</summary>
            <param name="prefix">the prefix</param>
            <param name="localName">the name</param>
        </member>
        <member name="P:XmpCore.Impl.QName.HasPrefix">
            <value>Returns whether the QName has a prefix.</value>
        </member>
        <member name="P:XmpCore.Impl.QName.LocalName">
            <summary>XML localname</summary>
            <value>the localName</value>
        </member>
        <member name="P:XmpCore.Impl.QName.Prefix">
            <summary>XML namespace prefix</summary>
            <value>the prefix</value>
        </member>
        <member name="T:XmpCore.Impl.Utils">
            <summary>Utility functions for the XMPToolkit implementation.</summary>
            <author>Stefan Makswit</author>
            <since>06.06.2006</since>
        </member>
        <member name="F:XmpCore.Impl.Utils.UuidSegmentCount">
            <summary>segments of a UUID</summary>
        </member>
        <member name="F:XmpCore.Impl.Utils.UuidLength">
            <summary>length of a UUID</summary>
        </member>
        <member name="F:XmpCore.Impl.Utils._xmlNameStartChars">
            <summary>table of XML name start chars (&lt;= 0xFF)</summary>
        </member>
        <member name="F:XmpCore.Impl.Utils._xmlNameChars">
            <summary>table of XML name chars (&lt;= 0xFF)</summary>
        </member>
        <member name="M:XmpCore.Impl.Utils.NormalizeLangValue(System.String)">
            <summary>
            Normalize an xml:lang value so that comparisons are effectively case
            insensitive as required by RFC 3066 (which supersedes RFC 1766).
            </summary>
            <remarks>
            The normalization rules:
            <list type="bullet">
              <item>The primary subtag is lower case, the suggested practice of ISO 639.</item>
              <item>All 2 letter secondary subtags are upper case, the suggested practice of ISO 3166.</item>
              <item>All other subtags are lower case.</item>
            </list>
            </remarks>
            <param name="value">raw value</param>
            <returns>Returns the normalized value.</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.SplitNameAndValue(System.String,System.String@,System.String@)">
            <summary>
            Split the name and value parts for field and qualifier selectors.
            </summary>
            <remarks>
            <list type="bullet">
              <item><c>[qualName="value"]</c> - An element in an array of structs, chosen by a field value.</item>
              <item><c>[?qualName="value"]</c> - An element in an array, chosen by a qualifier value.</item>
            </list>
            The value portion is a string quoted by <c>'</c> or <c>"</c>. The value may contain
            any character including a doubled quoting character. The value may be
            empty.
            <para />
            <em>Note:</em> It is assumed that the expression is formal correct.
            </remarks>
            <param name="selector">The selector</param>
            <param name="name">The name string</param>
            <param name="value">The value string</param>
        </member>
        <member name="M:XmpCore.Impl.Utils.IsInternalProperty(System.String,System.String)">
            <param name="schema">a schema namespace</param>
            <param name="prop">an XMP Property</param>
            <returns>
            Returns true if the property is defined as &quot;Internal
            Property&quot;, see XMP Specification.
            </returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.CheckUuidFormat(System.String)">
            <summary>
            Check some requirements for an UUID:
            <list type="bullet">
              <item>Length of the UUID is 32</item>
              <item>The Delimiter count is 4 and all the 4 delimiter are on their right position (8,13,18,23)</item>
            </list>
            </summary>
            <param name="uuid">uuid to test</param>
            <returns>true - this is a well formed UUID, false - UUID has not the expected format</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.IsXmlName(System.String)">
            <summary>Simple check for valid XML names.</summary>
            <remarks>
            Within ASCII range
            <para />
            ":" | [A-Z] | "_" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6]
            <para />
            are accepted, above all characters (which is not entirely
            correct according to the XML Spec.
            </remarks>
            <param name="name">an XML Name</param>
            <returns>Return <c>true</c> if the name is correct.</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.IsXmlNameNs(System.String)">
            <summary>
            Checks if the value is a legal "unqualified" XML name, as
            defined in the XML Namespaces proposed recommendation.
            </summary>
            <remarks>
            These are XML names, except that they must not contain a colon.
            </remarks>
            <param name="name">the value to check</param>
            <returns>Returns true if the name is a valid "unqualified" XML name.</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.IsControlChar(System.Char)">
            <param name="c">a char</param>
            <returns>Returns true if the char is an ASCII control char.</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.EscapeXml(System.String,System.Boolean,System.Boolean)">
            <summary>Serializes the node value in XML encoding.</summary>
            <remarks>
            Its used for tag bodies and attributes.
            <para />
            <em>Note:</em> The attribute is always limited by quotes,
            thats why <c>&amp;apos;</c> is never serialized.
            <para />
            <em>Note:</em> Control chars are written unescaped, but if the user uses others than tab, LF
            and CR the resulting XML will become invalid.
            </remarks>
            <param name="value">a string</param>
            <param name="forAttribute">flag if string is attribute value (need to additional escape quotes)</param>
            <param name="escapeWhitespaces">Decides if LF, CR and TAB are escaped.</param>
            <returns>Returns the value ready for XML output.</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.RemoveControlChars(System.String)">
            <summary>Replaces the ASCII control chars with a space.</summary>
            <param name="value">a node value</param>
            <returns>Returns the cleaned up value</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.IsNameStartChar(System.Char)">
            <summary>Simple check if a character is a valid XML start name char.</summary>
            <remarks>
            Simple check if a character is a valid XML start name char.
            All characters according to the XML Spec 1.1 are accepted:
            http://www.w3.org/TR/xml11/#NT-NameStartChar
            </remarks>
            <param name="ch">a character</param>
            <returns>Returns true if the character is a valid first char of an XML name.</returns>
        </member>
        <member name="M:XmpCore.Impl.Utils.IsNameChar(System.Char)">
            <summary>
            Simple check if a character is a valid XML name char
            (every char except the first one), according to the XML Spec 1.1:
            http://www.w3.org/TR/xml11/#NT-NameChar
            </summary>
            <param name="ch">a character</param>
            <returns>Returns true if the character is a valid char of an XML name.</returns>
        </member>
        <member name="T:XmpCore.Impl.XmpDateTime">
            <summary>The default implementation of <see cref="T:XmpCore.IXmpDateTime" />.</summary>
            <author>Stefan Makswit</author>
            <since>16.02.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpDateTime._timeZone">
            <summary>Use NO time zone as default</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpDateTime._nanoseconds">
            <summary>The nanoseconds take micro and nano seconds, while the milliseconds are in the calendar.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.#ctor">
            <summary>
            Creates an <c>XMPDateTime</c>-instance with the current time in the default time zone.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.#ctor(Sharpen.Calendar)">
            <summary>Creates an <c>XMPDateTime</c>-instance from a calendar.</summary>
            <param name="calendar">a <c>Calendar</c></param>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.#ctor(System.DateTime,System.TimeZoneInfo)">
            <summary>
            Creates an <c>XMPDateTime</c>-instance from
            a <c>Date</c> and a <c>TimeZone</c>.
            </summary>
            <param name="date">a date describing an absolute point in time</param>
            <param name="timeZone">a TimeZone how to interpret the date</param>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.#ctor(System.String)">
            <summary>Creates an <c>XMPDateTime</c>-instance from an ISO 8601 string.</summary>
            <param name="strValue">an ISO 8601 string</param>
            <exception cref="T:XmpCore.XmpException">If the string is a non-conform ISO 8601 string, an exception is thrown</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.ToString">
            <returns>Returns the ISO string representation.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.UnixTimeToDateTime(System.Int64)">
            <param name="unixTime">Number of milliseconds since the Unix epoch (1970-01-01 00:00:00).</param>
        </member>
        <member name="M:XmpCore.Impl.XmpDateTime.UnixTimeToDateTimeOffset(System.Int64)">
            <param name="unixTime">Number of milliseconds since the Unix epoch (1970-01-01 00:00:00).</param>
        </member>
        <member name="T:XmpCore.Impl.XmpIterator">
            <summary>The <c>XMPIterator</c> implementation.</summary>
            <remarks>
            The <c>XMPIterator</c> implementation.
            Iterates the XMP Tree according to a set of options.
            During the iteration the XMPMeta-object must not be changed.
            Calls to <c>skipSubtree()</c> / <c>skipSiblings()</c> will affect the iteration.
            </remarks>
            <author>Stefan Makswit</author>
            <since>29.06.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator._skipSiblings">
            <summary>flag to indicate that skipSiblings() has been called.</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator._nodeIterator">
            <summary>the node iterator doing the work</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.#ctor(XmpCore.Impl.XmpMeta,System.String,System.String,XmpCore.Options.IteratorOptions)">
            <summary>Constructor with optional initial values.</summary>
            <remarks>If <c>propName</c> is provided, <c>schemaNS</c> has also be provided.</remarks>
            <param name="xmp">the iterated metadata object.</param>
            <param name="schemaNs">the iteration is reduced to this schema (optional)</param>
            <param name="propPath">the iteration is reduced to this property within the <c>schemaNS</c></param>
            <param name="options">advanced iteration options, see <see cref="T:XmpCore.Options.IteratorOptions" /></param>
            <exception cref="T:XmpCore.XmpException">If the node defined by the parameters is not existing.</exception>
        </member>
        <member name="P:XmpCore.Impl.XmpIterator.BaseNamespace">
            <summary>the base namespace of the property path, will be changed during the iteration</summary>
        </member>
        <member name="T:XmpCore.Impl.XmpIterator.NodeIterator">
            <summary>The <c>XMPIterator</c> implementation.</summary>
            <remarks>
            The <c>XMPIterator</c> implementation.
            It first returns the node itself, then recursively the children and qualifier of the node.
            </remarks>
            <author>Stefan Makswit</author>
            <since>29.06.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator.IterateNode">
            <summary>iteration state</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator.IterateChildren">
            <summary>iteration state</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator.IterateQualifier">
            <summary>iteration state</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._state">
            <summary>the state of the iteration</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._visitedNode">
            <summary>the currently visited node</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._path">
            <summary>the recursively accumulated path</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._childrenIterator">
            <summary>the iterator that goes through the children and qualifier list</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._index">
            <summary>index of node with parent, only interesting for arrays</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._subIterator">
            <summary>the iterator for each child</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpIterator.NodeIterator._returnProperty">
            <summary>the cached <c>PropertyInfo</c> to return</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.#ctor(XmpCore.Impl.XmpIterator)">
            <summary>Default constructor</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.#ctor(XmpCore.Impl.XmpIterator,XmpCore.Impl.XmpNode,System.String,System.Int32)">
            <summary>Constructor for the node iterator.</summary>
            <param name="enclosing"></param>
            <param name="visitedNode">the currently visited node</param>
            <param name="parentPath">the accumulated path of the node</param>
            <param name="index">the index within the parent node (only for arrays)</param>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.HasNext">
            <summary>Prepares the next node to return if not already done.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.ReportNode">
            <summary>Sets the returnProperty as next item or recurses into <c>hasNext()</c>.</summary>
            <returns>Returns if there is a next item to return.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.IterateChildrenMethod(Sharpen.IIterator)">
            <summary>Handles the iteration of the children or qualfier</summary>
            <param name="iterator">an iterator</param>
            <returns>Returns if there are more elements available.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.Next">
            <summary>Calls hasNext() and returns the prepared node.</summary>
            <remarks>
            Calls hasNext() and returns the prepared node. Afterward it's set to null.
            The existence of returnProperty indicates if there is a next node, otherwise
            an exception is thrown.
            </remarks>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.Remove">
            <summary>Not supported.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.AccumulatePath(XmpCore.Impl.XmpNode,System.String,System.Int32)">
            <param name="currNode">the node that will be added to the path.</param>
            <param name="parentPath">the path up to this node.</param>
            <param name="currentIndex">the current array index if an array is traversed</param>
            <returns>Returns the updated path.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.CreatePropertyInfo(XmpCore.Impl.XmpNode,System.String,System.String)">
            <summary>Creates a property info object from an <c>XMPNode</c>.</summary>
            <param name="node">an <c>XMPNode</c></param>
            <param name="baseNs">the base namespace to report</param>
            <param name="path">the full property path</param>
            <returns>Returns a <c>XMPProperty</c>-object that serves representation of the node.</returns>
        </member>
        <member name="T:XmpCore.Impl.XmpIterator.NodeIterator.XmpPropertyInfo">
            <remarks>
            Originally called "XmpPropertyInfo450"
            "450" was the line number in XMPIteratorImpl.java of the Adobe Java 5.1.2 source file
            This class was anonymous, but that is unnecessary here
            </remarks>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.GetReturnProperty">
            <returns>Returns the returnProperty.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIterator.SetReturnProperty(XmpCore.IXmpPropertyInfo)">
            <param name="returnProperty">the returnProperty to set</param>
        </member>
        <member name="T:XmpCore.Impl.XmpIterator.NodeIteratorChildren">
            <summary>
            This iterator is derived from the default <c>NodeIterator</c>,
            and is only used for the option <see cref="F:XmpCore.Options.IteratorOptions.JustChildren"/>.
            </summary>
            <author>Stefan Makswit</author>
            <since>02.10.2006</since>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIteratorChildren.#ctor(XmpCore.Impl.XmpIterator,XmpCore.Impl.XmpNode,System.String)">
            <summary>Constructor</summary>
            <param name="enclosing"></param>
            <param name="parentNode">the node which children shall be iterated.</param>
            <param name="parentPath">the full path of the former node without the leaf node.</param>
        </member>
        <member name="M:XmpCore.Impl.XmpIterator.NodeIteratorChildren.HasNext">
            <summary>Prepares the next node to return if not already done.</summary>
        </member>
        <member name="T:XmpCore.Impl.XmpMeta">
            <summary>
            Implementation of <see cref="T:XmpCore.IXmpMeta"/>.
            </summary>
            <author>Stefan Makswit</author>
            <since>17.02.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpMeta.ValueType.String">
            <summary>Property values are Strings by default</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpMeta._tree">
            <summary>root of the metadata tree</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpMeta._packetHeader">
            <summary>the xpacket processing instructions content</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.#ctor">
            <summary>Constructor for an empty metadata object.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.#ctor(XmpCore.Impl.XmpNode)">
            <summary>Constructor for a cloned metadata tree.</summary>
            <param name="tree">
            an prefilled metadata tree which fulfills all
            <c>XMPNode</c> contracts.
            </param>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.AppendArrayItem(System.String,System.String,XmpCore.Options.PropertyOptions,System.String,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.AppendArrayItem(System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.CountArrayItems(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetArrayItem(System.String,System.String,System.Int32)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetLocalizedText(System.String,System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetLocalizedText(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetLocalizedText(System.String,System.String,System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetProperty(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetProperty(System.String,System.String,XmpCore.Impl.XmpMeta.ValueType)">
            <summary>Returns a property, but the result value can be requested.</summary>
            <remarks>
            Returns a property, but the result value can be requested.
            </remarks>
            <param name="schemaNs">a schema namespace</param>
            <param name="propName">a property name or path</param>
            <param name="valueType">the type of the value, see VALUE_...</param>
            <returns>Returns an <c>XMPProperty</c></returns>
            <exception cref="T:XmpCore.XmpException">Collects any exception that occurs.</exception>
        </member>
        <member name="T:XmpCore.Impl.XmpMeta.XmpProperty">
            <remarks>
            Combines the two ported classes XmpProperty407 and XmpProperty682
            "407" and "682" were the line numbers in XMPMetaImpl.java of the Adobe Java 5.1.2 source file
            These classes were anonymous, and that is unnecessary here
            </remarks>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyObject(System.String,System.String,XmpCore.Impl.XmpMeta.ValueType)">
            <summary>Returns a property, but the result value can be requested.</summary>
            <param name="schemaNs">a schema namespace</param>
            <param name="propName">a property name or path</param>
            <param name="valueType">the type of the value, see VALUE_...</param>
            <returns>
            Returns the node value as an object according to the
            <c>valueType</c>.
            </returns>
            <exception cref="T:XmpCore.XmpException">Collects any exception that occurs.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyBoolean(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyBoolean(System.String,System.String,System.Boolean,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyBoolean(System.String,System.String,System.Boolean)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyInteger(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyInteger(System.String,System.String,System.Int32,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyInteger(System.String,System.String,System.Int32)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyLong(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyLong(System.String,System.String,System.Int64,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyLong(System.String,System.String,System.Int64)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyDouble(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyDouble(System.String,System.String,System.Double,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyDouble(System.String,System.String,System.Double)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyDate(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyDate(System.String,System.String,XmpCore.IXmpDateTime,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyDate(System.String,System.String,XmpCore.IXmpDateTime)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyCalendar(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyCalendar(System.String,System.String,Sharpen.Calendar,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyCalendar(System.String,System.String,Sharpen.Calendar)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyBase64(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetPropertyString(System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyBase64(System.String,System.String,System.Byte[],XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPropertyBase64(System.String,System.String,System.Byte[])">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetQualifier(System.String,System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetStructField(System.String,System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetArrayItem(System.String,System.String,System.Int32,System.String,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetArrayItem(System.String,System.String,System.Int32,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.InsertArrayItem(System.String,System.String,System.Int32,System.String,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.InsertArrayItem(System.String,System.String,System.Int32,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetProperty(System.String,System.String,System.Object,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetProperty(System.String,System.String,System.Object)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetQualifier(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetQualifier(System.String,System.String,System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetStructField(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetStructField(System.String,System.String,System.String,System.String,System.String)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetPacketHeader(System.String)">
            <summary>Sets the packetHeader attributes, only used by the parser.</summary>
            <param name="packetHeader">the processing instruction content</param>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.Clone">
            <summary>Performs a deep clone of the XMPMeta-object</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.Normalize(XmpCore.Options.ParseOptions)">
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.GetRoot">
            <returns>Returns the root node of the XMP tree.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.DoSetArrayItem(XmpCore.Impl.XmpNode,System.Int32,System.String,XmpCore.Options.PropertyOptions,System.Boolean)">
            <summary>Locate or create the item node and set the value.</summary>
            <remarks>
            Locate or create the item node and set the value. Note the index
            parameter is one-based! The index can be in the range [1..size + 1] or
            "last()", normalize it and check the insert flags. The order of the
            normalization checks is important. If the array is empty we end up with
            an index and location to set item size + 1.
            </remarks>
            <param name="arrayNode">an array node</param>
            <param name="itemIndex">the index where to insert the item</param>
            <param name="itemValue">the item value</param>
            <param name="itemOptions">the options for the new item</param>
            <param name="insert">insert oder overwrite at index position?</param>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.SetNode(XmpCore.Impl.XmpNode,System.Object,XmpCore.Options.PropertyOptions,System.Boolean)">
            <summary>
            The internals for setProperty() and related calls, used after the node is
            found or created.
            </summary>
            <param name="node">the newly created node</param>
            <param name="value">the node value, can be <c>null</c></param>
            <param name="newOptions">options for the new node, must not be <c>null</c>.</param>
            <param name="deleteExisting">flag if the existing value is to be overwritten</param>
            <exception cref="T:XmpCore.XmpException">thrown if options and value do not correspond</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMeta.EvaluateNodeValue(XmpCore.Impl.XmpMeta.ValueType,XmpCore.Impl.XmpNode)">
            <summary>
            Evaluates a raw node value to the given value type, apply special
            conversions for defined types in XMP.
            </summary>
            <param name="valueType">an int indicating the value type</param>
            <param name="propNode">the node containing the value</param>
            <returns>Returns a literal value for the node.</returns>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="T:XmpCore.Impl.XmpMetaParser">
            <summary>
            This class replaces the <c>ExpatAdapter.cpp</c> and does the
            XML-parsing and fixes the prefix.
            </summary>
            <remarks>
            After the parsing several normalisations are applied to the XMP tree.
            </remarks>
            <author>Stefan Makswit</author>
            <since>01.02.2006</since>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.Parse(System.IO.Stream,XmpCore.Options.ParseOptions)">
            <summary>
            Parses an XMP metadata object from a stream, including de-aliasing and normalisation.
            </summary>
            <exception cref="T:XmpCore.XmpException">Thrown if parsing or normalisation fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.Parse(System.Byte[],XmpCore.Options.ParseOptions)">
            <summary>
            Parses an XMP metadata object from a stream, including de-aliasing and normalisation.
            </summary>
            <exception cref="T:XmpCore.XmpException">Thrown if parsing or normalisation fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.Parse(XmpCore.Impl.ByteBuffer,XmpCore.Options.ParseOptions)">
            <summary>
            Parses an XMP metadata object from a stream, including de-aliasing and normalisation.
            </summary>
            <exception cref="T:XmpCore.XmpException">Thrown if parsing or normalisation fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.Parse(System.String,XmpCore.Options.ParseOptions)">
            <summary>
            Parses an XMP metadata object from a stream, including de-aliasing and normalisation.
            </summary>
            <exception cref="T:XmpCore.XmpException">Thrown if parsing or normalisation fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.Parse(System.Xml.Linq.XDocument,XmpCore.Options.ParseOptions)">
            <summary>
            Parses an XMP metadata object from a XDocument, including de-aliasing and normalisation.
            </summary>
            <exception cref="T:XmpCore.XmpException">Thrown if parsing or normalisation fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.Extract(System.Byte[],XmpCore.Options.ParseOptions)">
            <summary>
            Parses XML from a byte buffer,
            fixing the encoding (Latin-1 to UTF-8) and illegal control character optionally.
            </summary>
            <param name="bytes">a byte buffer containing the XMP packet</param>
            <param name="options">the parsing options</param>
            <returns>Returns an XML DOM-Document.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown when the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.ParseXmlFromInputStream(System.IO.Stream,XmpCore.Options.ParseOptions)">
            <summary>
            Parses XML from an <see cref="T:System.IO.Stream"/>,
            fixing the encoding (Latin-1 to UTF-8) and illegal control character optionally.
            </summary>
            <param name="stream">an <c>Stream</c></param>
            <param name="options">the parsing options</param>
            <returns>Returns an XML DOM-Document.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown when the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.ParseXmlFromByteBuffer(XmpCore.Impl.ByteBuffer,XmpCore.Options.ParseOptions)">
            <summary>
            Parses XML from a byte buffer,
            fixing the encoding (Latin-1 to UTF-8) and illegal control character optionally.
            To improve the performance on legal files, it is first tried to parse normally,
            while the character fixing is only done when the first pass fails.
            </summary>
            <param name="buffer">a byte buffer containing the XMP packet</param>
            <param name="options">the parsing options</param>
            <returns>Returns an XML DOM-Document.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown when the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.ParseXmlString(System.String,XmpCore.Options.ParseOptions)">
            <summary>
            Parses XML from a <see cref="T:System.String"/>, fixing the illegal control character optionally.
            </summary>
            <param name="input">a <c>String</c> containing the XMP packet</param>
            <param name="options">the parsing options</param>
            <returns>Returns an XML DOM-Document.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown when the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.ParseStream(System.IO.Stream,XmpCore.Options.ParseOptions)">
            <exception cref="T:XmpCore.XmpException">Wraps parsing and I/O-exceptions into an XMPException.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.ParseTextReader(System.IO.TextReader,XmpCore.Options.ParseOptions)">
            <exception cref="T:XmpCore.XmpException">Wraps parsing and I/O-exceptions into an XMPException.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpMetaParser.FindRootNode(System.Collections.Generic.IEnumerable{System.Xml.Linq.XNode},System.Boolean,System.Object[])">
            <summary>Find the XML node that is the root of the XMP data tree.</summary>
            <remarks>
            Find the XML node that is the root of the XMP data tree. Generally this
            will be an outer node, but it could be anywhere if a general XML document
            is parsed (e.g. SVG). The XML parser counted all rdf:RDF and
            pxmp:XMP_Packet nodes, and kept a pointer to the last one. If there is
            more than one possible root use PickBestRoot to choose among them.
            <para />
            If there is a root node, try to extract the version of the previous XMP
            toolkit.
            <para />
            Pick the first x:xmpmeta among multiple root candidates. If there aren't
            any, pick the first bare rdf:RDF if that is allowed. The returned root is
            the rdf:RDF child if an x:xmpmeta element was chosen. The search is
            breadth first, so a higher level candidate is chosen over a lower level
            one that was textually earlier in the serialized XML.
            </remarks>
            <param name="nodes">initially, the root of the xml document as a list</param>
            <param name="xmpmetaRequired">
            flag if the xmpmeta-tag is still required, might be set
            initially to <c>true</c>, if the parse option "REQUIRE_XMP_META" is set
            </param>
            <param name="result">The result array that is filled during the recursive process.</param>
            <returns>
            Returns an array that contains the result or <c>null</c>.
            The array contains:
            <list type="bullet">
            <item>[0] - the rdf:RDF-node</item>
            <item>[1] - an object that is either XMP_RDF or XMP_PLAIN (the latter is deprecated)</item>
            <item>[2] - the body text of the xpacket-instruction.</item>
            </list>
            </returns>
        </member>
        <member name="T:XmpCore.Impl.XmpNode">
            <summary>
            A node in the internally XMP tree, which can be a schema node, a property node, an array node,
            an array item, a struct node or a qualifier node (without '?').
            </summary>
            <remarks>
            Possible improvements:
            1. The kind Node of node might be better represented by a class-hierarchy of different nodes.
            2. The array type should be an enum
            3. isImplicitNode should be removed completely and replaced by return values of fi.
            4. hasLanguage, hasType should be automatically maintained by XMPNode
            </remarks>
            <author>Stefan Makswit</author>
            <since>21.02.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpNode._children">
            <summary>list of child nodes, lazy initialized</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpNode._childrenLookup">
            <summary>list of child node references for faster lookup. Only initialized when the original _children list exceeds 9 entries</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpNode._qualifier">
            <summary>list of qualifier of the node, lazy initialized</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpNode._options">
            <summary>options describing the kind of the node</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.#ctor(System.String,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Creates an <c>XMPNode</c> with initial values.</summary>
            <param name="name">the name of the node</param>
            <param name="value">the value of the node</param>
            <param name="options">the options of the node</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.#ctor(System.String,XmpCore.Options.PropertyOptions)">
            <summary>Constructor for the node without value.</summary>
            <param name="name">the name of the node</param>
            <param name="options">the options of the node</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.Clear">
            <summary>Resets the node.</summary>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.Parent">
            <summary>
            Get the parent node.
            </summary>
            <remarks>
            Set internally by <see cref="M:XmpCore.Impl.XmpNode.AddChild(XmpCore.Impl.XmpNode)"/>, <see cref="M:XmpCore.Impl.XmpNode.AddChild(XmpCore.Impl.XmpNode)"/> and <see cref="M:XmpCore.Impl.XmpNode.AddQualifier(XmpCore.Impl.XmpNode)"/>.
            </remarks>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetChild(System.Int32)">
            <param name="index">an index [1..size]</param>
            <returns>Returns the child with the requested index.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.AddChild(XmpCore.Impl.XmpNode)">
            <summary>Adds a node as child to this node.</summary>
            <param name="node">an XMPNode</param>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.AddChild(System.Int32,XmpCore.Impl.XmpNode)">
            <summary>Adds a node as child to this node.</summary>
            <param name="index">
            the index of the node <em>before</em> which the new one is inserted.
            <em>Note:</em> The node children are indexed from [1..size]!
            An index of size + 1 appends a node.
            </param>
            <param name="node">an XMPNode</param>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.ReplaceChild(System.Int32,XmpCore.Impl.XmpNode)">
            <summary>Replaces a node with another one.</summary>
            <param name="index">
            the index of the node that will be replaced.
            <em>Note:</em> The node children are indexed from [1..size]!
            </param>
            <param name="node">the replacement XMPNode</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.RemoveChild(System.Int32)">
            <summary>Removes a child at the requested index.</summary>
            <param name="itemIndex">the index to remove [1..size]</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.RemoveChild(XmpCore.Impl.XmpNode)">
            <summary>Removes a child node.</summary>
            <remarks>
            Removes a child node.
            If its a schema node and doesn't have any children anymore, its deleted.
            </remarks>
            <param name="node">the child node to delete.</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.CleanupChildren">
            <summary>
            Removes the children list if this node has no children anymore;
            checks if the provided node is a schema node and doesn't have any children anymore,
            its deleted.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.RemoveChildren">
            <summary>Removes all children from the node.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetChildrenLength">
            <returns>Returns the number of children without necessarily creating a list.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.FindChildByName(System.String)">
            <param name="expr">child node name to look for</param>
            <returns>Returns an <c>XMPNode</c> if node has been found, <c>null</c> otherwise.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetQualifier(System.Int32)">
            <param name="index">an index [1..size]</param>
            <returns>Returns the qualifier with the requested index.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetQualifierLength">
            <returns>Returns the number of qualifier without necessarily creating a list.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.AddQualifier(XmpCore.Impl.XmpNode)">
            <summary>Appends a qualifier to the qualifier list and sets respective options.</summary>
            <param name="qualNode">a qualifier node.</param>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.RemoveQualifier(XmpCore.Impl.XmpNode)">
            <summary>Removes one qualifier node and fixes the options.</summary>
            <param name="qualNode">qualifier to remove</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.RemoveQualifiers">
            <summary>Removes all qualifiers from the node and sets the options appropriate.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.FindQualifierByName(System.String)">
            <param name="expr">qualifier node name to look for</param>
            <returns>
            Returns a qualifier <c>XMPNode</c> if node has been found,
            <c>null</c> otherwise.
            </returns>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.HasChildren">
            <summary>
            Get whether the node has children.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.IterateChildren">
            <returns>
            Returns an iterator for the children.
            <em>Note:</em> take care to use it.remove(), as the flag are not adjusted in that case.
            </returns>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.HasQualifier">
            <summary>
            Returns whether the node has qualifier attached.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.IterateQualifier">
            <returns>
            Returns an iterator for the qualifier.
            <em>Note:</em> take care to use it.remove(), as the flag are not adjusted in that case.
            </returns>
        </member>
        <member name="T:XmpCore.Impl.XmpNode.Iterator391">
            <summary>
            Iterator that disallows removal.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.Clone">
            <summary>Performs a <b>deep clone</b> of the node and the complete subtree.</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.Clone(System.Boolean)">
            <summary>Performs a <b>deep clone</b> of the node and the complete subtree.
            if <c>skipEmpty</c> is true, it will not clone node which has empty child and empty value.</summary>
            <param name="skipEmpty">If true, it will not clone those nodes with empty value and empty children</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.CloneSubtree(XmpCore.Impl.XmpNode,System.Boolean)">
            <summary>
            Performs a <b>deep clone</b> of the complete subtree (children and
            qualifier )into and add it to the destination node.
            if <c>skipEmpty</c> is true, it will not clone node which has empty child and empty value.
            </summary>
            <param name="destination">the node to add the cloned subtree</param>
            <param name="skipEmpty">If true, it will not clone those nodes with empty value and empty children</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.DumpNode(System.Boolean)">
            <summary>Renders this node and the tree under this node in a human readable form.</summary>
            <param name="recursive">Flag is qualifier and child nodes shall be rendered too</param>
            <returns>Returns a multiline string containing the dump.</returns>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.IsImplicit">
            <summary>
            Get and set the implicit node flag.
            </summary>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.HasAliases">
            <summary>
            Get and set whether the node contains aliases (applies only to schema nodes).
            </summary>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.IsAlias">
            <summary>
            Get and set whether this node is an alias (applies only to schema nodes).
            </summary>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.HasValueChild">
            <summary>
            Get and set whether this node has an <c>rdf:value</c> child node.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.Sort">
            <summary>
            Sorts the XMP node and its children, recursively.
            </summary>
            <remarks>
            Sorting occurs according to the following rules:
            <list type="bullet">
            <item>Nodes at one level are sorted by name, that is prefix + local name</item>
            <item>Starting at the root node the children and qualifier are sorted recursively,
            which the following exceptions.</item>
            <item>Sorting will not be used for arrays.</item>
            <item>Within qualifier "xml:lang" and/or "rdf:type" stay at the top in that order,
            all others are sorted.</item>
            </list>
            </remarks>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.DumpNode(System.Text.StringBuilder,System.Boolean,System.Int32,System.Int32)">
            <summary>Dumps this node and its qualifier and children recursively.</summary>
            <remarks>
            Dumps this node and its qualifier and children recursively.
            <em>Note:</em> It creates empty options on every node.
            FfF: sort schemas and properties on each level if and only if it would increase performance
            </remarks>
            <param name="result">the buffer to append the dump.</param>
            <param name="recursive">Flag is qualifier and child nodes shall be rendered too</param>
            <param name="indent">the current indent level.</param>
            <param name="index">the index within the parent node (important for arrays)</param>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.IsLanguageNode">
            <summary>
            Get whether this node is a language qualifier.
            </summary>
        </member>
        <member name="P:XmpCore.Impl.XmpNode.IsTypeNode">
            <summary>
            Get whether this node is a type qualifier.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetChildren">
            <summary>
            <em>Note:</em> This method should always be called when accessing 'children' to be sure
            that its initialized.
            </summary>
            <returns>Returns list of children that is lazy initialized.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetUnmodifiableChildren">
            <returns>Returns a read-only copy of child nodes list.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.GetQualifier">
            <returns>Returns list of qualifier that is lazy initialized.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.Find(System.Collections.Generic.IEnumerable{XmpCore.Impl.XmpNode},System.String)">
            <summary>Internal find.</summary>
            <param name="list">the list to search in</param>
            <param name="expr">the search expression</param>
            <returns>Returns the found node or <c>nulls</c>.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.FindChild(System.Collections.Generic.List{XmpCore.Impl.XmpNode},System.Collections.Generic.Dictionary{System.String,XmpCore.Impl.XmpNode}@,System.String)">
            <summary>Internal child find.</summary>
            <param name="children">the child list to search in</param>
            <param name="lookup">the child dictionary ref to initialize or search. Needs to be a ref parameter or it won't update the original Dictionary</param>
            <param name="expr">the search expression</param>
            <returns>Returns the found node or <c>null</c>.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.AssertChildNotExisting(System.String)">
            <summary>Checks that a node name is not existing on the same level, except for array items.</summary>
            <param name="childName">the node name to check</param>
            <exception cref="T:XmpCore.XmpException">Thrown if a node with the same name is existing.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNode.AssertQualifierNotExisting(System.String)">
            <summary>Checks that a qualifier name is not existing on the same level.</summary>
            <param name="qualifierName">the new qualifier name</param>
            <exception cref="T:XmpCore.XmpException">Thrown if a node with the same name is existing.</exception>
        </member>
        <member name="T:XmpCore.Impl.XmpNodeUtils">
            <summary>Utilities for <c>XMPNode</c>.</summary>
            <author>Stefan Makswit</author>
            <since>Aug 28, 2006</since>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FindSchemaNode(XmpCore.Impl.XmpNode,System.String,System.Boolean)">
            <summary>Find or create a schema node if <c>createNodes</c> is false and</summary>
            <param name="tree">the root of the xmp tree.</param>
            <param name="namespaceUri">a namespace</param>
            <param name="createNodes">
            a flag indicating if the node shall be created if not found.
            <em>Note:</em> The namespace must be registered prior to this call.
            </param>
            <returns>
            Returns the schema node if found, <c>null</c> otherwise.
            Note: If <c>createNodes</c> is <c>true</c>, it is <b>always</b>
            returned a valid node.
            </returns>
            <exception cref="T:XmpCore.XmpException">
            An exception is only thrown if an error occurred, not if a
            node was not found.
            </exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FindSchemaNode(XmpCore.Impl.XmpNode,System.String,System.String,System.Boolean)">
            <summary>Find or create a schema node if <c>createNodes</c> is true.</summary>
            <param name="tree">the root of the xmp tree.</param>
            <param name="namespaceUri">a namespace</param>
            <param name="suggestedPrefix">If a prefix is suggested, the namespace is allowed to be registered.</param>
            <param name="createNodes">
            a flag indicating if the node shall be created if not found.
            <em>Note:</em> The namespace must be registered prior to this call.
            </param>
            <returns>
            Returns the schema node if found, <c>null</c> otherwise.
            Note: If <c>createNodes</c> is <c>true</c>, it is <b>always</b>
            returned a valid node.
            </returns>
            <exception cref="T:XmpCore.XmpException">
            An exception is only thrown if an error occurred, not if a
            node was not found.
            </exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FindChildNode(XmpCore.Impl.XmpNode,System.String,System.Boolean)">
            <summary>Find or create a child node under a given parent node.</summary>
            <remarks>
            Find or create a child node under a given parent node. If the parent node is no
            Returns the found or created child node.
            </remarks>
            <param name="parent">the parent node</param>
            <param name="childName">the node name to find</param>
            <param name="createNodes">flag, if new nodes shall be created.</param>
            <returns>Returns the found or created node or <c>null</c>.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown if</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FindNode(XmpCore.Impl.XmpNode,XmpCore.Impl.XPath.XmpPath,System.Boolean,XmpCore.Options.PropertyOptions)">
            <summary>Follow an expanded path expression to find or create a node.</summary>
            <param name="xmpTree">the node to begin the search.</param>
            <param name="xpath">the complete xpath</param>
            <param name="createNodes">
            flag if nodes shall be created
            (when called by <c>setProperty()</c>)
            </param>
            <param name="leafOptions">
            the options for the created leaf nodes (only when
            <c>createNodes == true</c>).
            </param>
            <returns>Returns the node if found or created or <c>null</c>.</returns>
            <exception cref="T:XmpCore.XmpException">
            An exception is only thrown if an error occurred,
            not if a node was not found.
            </exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.DeleteNode(XmpCore.Impl.XmpNode)">
            <summary>Deletes the the given node and its children from its parent.</summary>
            <remarks>
            Deletes the the given node and its children from its parent.
            Takes care about adjusting the flags.
            FfF: think about moving is to XMPNode... (make removeChild/Qualifier private and
            FfF: publish just deleteNode(XMPNode)
            </remarks>
            <param name="node">the top-most node to delete.</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.SetNodeValue(XmpCore.Impl.XmpNode,System.Object)">
            <summary>This is setting the value of a leaf node.</summary>
            <param name="node">an XMPNode</param>
            <param name="value">a value</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.VerifySetOptions(XmpCore.Options.PropertyOptions,System.Object)">
            <summary>Verifies the PropertyOptions for consistency and updates them as needed.</summary>
            <remarks>
            Verifies the PropertyOptions for consistency and updates them as needed.
            If options are <c>null</c> they are created with default values.
            FfF: add an kind of autofix options to PropertyOptions and remove this method!!!
            </remarks>
            <param name="options">the <c>PropertyOptions</c></param>
            <param name="itemValue">the node value to set</param>
            <returns>Returns the updated options.</returns>
            <exception cref="T:XmpCore.XmpException">If the options are not consistant.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.SerializeNodeValue(System.Object)">
            <summary>
            Converts the node value to string, apply special conversions for defined
            types in XMP.
            </summary>
            <param name="value">the node value to set</param>
            <returns>Returns the String representation of the node value.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FollowXPathStep(XmpCore.Impl.XmpNode,XmpCore.Impl.XPath.XmpPathSegment,System.Boolean)">
            <summary>
            After processing by ExpandXPath, a step can be of these forms:
            </summary>
            <remarks>
            After processing by ExpandXPath, a step can be of these forms:
            <list type="bullet">
            <item>qualName - A top level property or struct field.</item>
            <item>[index] - An element of an array.</item>
            <item>[last()] - The last element of an array.</item>
            <item>[qualName="value"] - An element in an array of structs, chosen by a field value.</item>
            <item>[?qualName="value"] - An element in an array, chosen by a qualifier value.</item>
            <item>?qualName - A general qualifier.</item>
            </list>
            Find the appropriate child node, resolving aliases, and optionally creating nodes.
            </remarks>
            <param name="parentNode">the node to start to start from</param>
            <param name="nextStep">the xpath segment</param>
            <param name="createNodes"></param>
            <returns>returns the found or created <see cref="T:XmpCore.Impl.XPath.XmpPath"/> node</returns>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FindQualifierNode(XmpCore.Impl.XmpNode,System.String,System.Boolean)">
            <summary>Find or create a qualifier node under a given parent node.</summary>
            <remarks>
            Find or create a qualifier node under a given parent node. Returns a pointer to the
            qualifier node, and optionally an iterator for the node's position in
            the parent's vector of qualifiers. The iterator is unchanged if no qualifier node (null)
            is returned.
            <em>Note:</em> On entry, the qualName parameter must not have the leading '?' from the
            <see cref="T:XmpCore.Impl.XPath.XmpPath"/> step.
            </remarks>
            <param name="parent">the parent XMPNode</param>
            <param name="qualName">the qualifier name</param>
            <param name="createNodes">flag if nodes shall be created</param>
            <returns>Returns the qualifier node if found or created, <c>null</c> otherwise.</returns>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.FindIndexedItem(XmpCore.Impl.XmpNode,System.String,System.Boolean)">
            <param name="arrayNode">an array node</param>
            <param name="segment">the segment containing the array index</param>
            <param name="createNodes">flag if new nodes are allowed to be created.</param>
            <returns>Returns the index or index = -1 if not found</returns>
            <exception cref="T:XmpCore.XmpException">Throws Exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.LookupFieldSelector(XmpCore.Impl.XmpNode,System.String,System.String)">
            <summary>
            Searches for a field selector in a node:
            [fieldName="value] - an element in an array of structs, chosen by a field value.
            </summary>
            <remarks>
            Searches for a field selector in a node:
            [fieldName="value] - an element in an array of structs, chosen by a field value.
            No implicit nodes are created by field selectors.
            </remarks>
            <param name="arrayNode" />
            <param name="fieldName" />
            <param name="fieldValue" />
            <returns>Returns the index of the field if found, otherwise -1.</returns>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.LookupQualSelector(XmpCore.Impl.XmpNode,System.String,System.String,System.Int32)">
            <summary>
            Searches for a qualifier selector in a node:
            [?qualName="value"] - an element in an array, chosen by a qualifier value.
            </summary>
            <remarks>
            Searches for a qualifier selector in a node:
            [?qualName="value"] - an element in an array, chosen by a qualifier value.
            No implicit nodes are created for qualifier selectors,
            except for an alias to an x-default item.
            </remarks>
            <param name="arrayNode">an array node</param>
            <param name="qualName">the qualifier name</param>
            <param name="qualValue">the qualifier value</param>
            <param name="aliasForm">
            in case the qual selector results from an alias,
            an x-default node is created if there has not been one.
            </param>
            <returns>Returns the index of th</returns>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.NormalizeLangArray(XmpCore.Impl.XmpNode)">
            <summary>Make sure the x-default item is first.</summary>
            <remarks>
            Make sure the x-default item is first. Touch up &quot;single value&quot;
            arrays that have a default plus one real language. This case should have
            the same value for both items. Older Adobe apps were hardwired to only
            use the &quot;x-default&quot; item, so we copy that value to the other
            item.
            </remarks>
            <param name="arrayNode">an alt text array node</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.DetectAltText(XmpCore.Impl.XmpNode)">
            <summary>See if an array is an alt-text array.</summary>
            <remarks>
            See if an array is an alt-text array. If so, make sure the x-default item
            is first.
            </remarks>
            <param name="arrayNode">the array node to check if its an alt-text array</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.AppendLangItem(XmpCore.Impl.XmpNode,System.String,System.String)">
            <summary>Appends a language item to an alt text array.</summary>
            <param name="arrayNode">the language array</param>
            <param name="itemLang">the language of the item</param>
            <param name="itemValue">the content of the item</param>
            <exception cref="T:XmpCore.XmpException">Thrown if a duplicate property is added</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.ChooseLocalizedText(XmpCore.Impl.XmpNode,System.String,System.String)">
            <summary>
            </summary>
            <remarks>
            <list>
            <item>Look for an exact match with the specific language.</item>
            <item>If a generic language is given, look for partial matches.</item>
            <item>Look for an "x-default"-item.</item>
            <item>Choose the first item.</item>
            </list>
            </remarks>
            <param name="arrayNode">the alt text array node</param>
            <param name="genericLang">the generic language</param>
            <param name="specificLang">the specific language</param>
            <returns>
            Returns the kind of match as an Integer and the found node in an
            array.
            </returns>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpNodeUtils.LookupLanguageItem(XmpCore.Impl.XmpNode,System.String)">
            <summary>Looks for the appropriate language item in a text alternative array.item</summary>
            <param name="arrayNode">an array node</param>
            <param name="language">the requested language</param>
            <returns>Returns the index if the language has been found, -1 otherwise.</returns>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="T:XmpCore.Impl.XmpNormalizer">
            <author>Stefan Makswit</author>
            <since>Aug 18, 2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpNormalizer._dcArrayForms">
            <summary>caches the correct dc-property array forms</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.Process(XmpCore.Impl.XmpMeta,XmpCore.Options.ParseOptions)">
            <summary>Normalizes a raw parsed XMPMeta-Object</summary>
            <param name="xmp">the raw metadata object</param>
            <param name="options">the parsing options</param>
            <returns>Returns the normalized metadata object</returns>
            <exception cref="T:XmpCore.XmpException">Collects all severe processing errors.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.TweakOldXmp(XmpCore.Impl.XmpNode)">
            <summary>
            Tweak old XMP: Move an instance ID from rdf:about to the
            <em>xmpMM:InstanceID</em> property.
            </summary>
            <remarks>
            Tweak old XMP: Move an instance ID from rdf:about to the
            <em>xmpMM:InstanceID</em> property. An old instance ID usually looks
            like &quot;uuid:bac965c4-9d87-11d9-9a30-000d936b79c4&quot;, plus InDesign
            3.0 wrote them like &quot;bac965c4-9d87-11d9-9a30-000d936b79c4&quot;. If
            the name looks like a UUID simply move it to <em>xmpMM:InstanceID</em>,
            don't worry about any existing <em>xmpMM:InstanceID</em>. Both will
            only be present when a newer file with the <em>xmpMM:InstanceID</em>
            property is updated by an old app that uses <em>rdf:about</em>.
            </remarks>
            <param name="tree">the root of the metadata tree</param>
            <exception cref="T:XmpCore.XmpException">Thrown if tweaking fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.TouchUpDataModel(XmpCore.Impl.XmpMeta)">
            <summary>Visit all schemas to do general fixes and handle special cases.</summary>
            <param name="xmp">the metadata object implementation</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the normalisation fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.NormalizeDcArrays(XmpCore.Impl.XmpNode)">
            <summary>
            Undo the denormalization performed by the XMP used in Acrobat 5.
            </summary>
            <remarks>
            If a Dublin Core array had only one item, it was serialized as a simple
            property.
            <para />
            The <c>xml:lang</c> attribute was dropped from an
            <c>alt-text</c> item if the language was <c>x-default</c>.
            </remarks>
            <param name="dcSchema">the DC schema node</param>
            <exception cref="T:XmpCore.XmpException">Thrown if normalization fails</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.RepairAltText(XmpCore.Impl.XmpNode)">
            <summary>Make sure that the array is well-formed AltText.</summary>
            <remarks>
            Make sure that the array is well-formed AltText. Each item must be simple
            and have an "xml:lang" qualifier. If repairs are needed, keep simple
            non-empty items by adding the "xml:lang" with value "x-repair".
            </remarks>
            <param name="arrayNode">the property node of the array to repair.</param>
            <exception cref="T:XmpCore.XmpException">Forwards unexpected exceptions.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.MoveExplicitAliases(XmpCore.Impl.XmpNode,XmpCore.Options.ParseOptions)">
            <summary>Visit all of the top level nodes looking for aliases.</summary>
            <remarks>
            Visit all of the top level nodes looking for aliases. If there is
            no base, transplant the alias subtree. If there is a base and strict
            aliasing is on, make sure the alias and base subtrees match.
            </remarks>
            <param name="tree">the root of the metadata tree</param>
            <param name="options">th parsing options</param>
            <exception cref="T:XmpCore.XmpException">Forwards XMP errors</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.TransplantArrayItemAlias(Sharpen.IIterator,XmpCore.Impl.XmpNode,XmpCore.Impl.XmpNode)">
            <summary>Moves an alias node of array form to another schema into an array</summary>
            <param name="propertyIt">the property iterator of the old schema (used to delete the property)</param>
            <param name="childNode">the node to be moved</param>
            <param name="baseArray">the base array for the array item</param>
            <exception cref="T:XmpCore.XmpException">Forwards XMP errors</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.FixGpsTimeStamp(XmpCore.Impl.XmpNode)">
            <summary>Fixes the GPS Timestamp in EXIF.</summary>
            <param name="exifSchema">the EXIF schema node</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the date conversion fails.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.DeleteEmptySchemas(XmpCore.Impl.XmpNode)">
            <summary>Remove all empty schemas from the metadata tree that were generated during the rdf parsing.</summary>
            <param name="tree">the root of the metadata tree</param>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.CompareAliasedSubtrees(XmpCore.Impl.XmpNode,XmpCore.Impl.XmpNode,System.Boolean)">
            <summary>The outermost call is special.</summary>
            <remarks>
            The outermost call is special. The names almost certainly differ. The
            qualifiers (and hence options) will differ for an alias to the x-default
            item of a langAlt array.
            </remarks>
            <param name="aliasNode">the alias node</param>
            <param name="baseNode">the base node of the alias</param>
            <param name="outerCall">marks the outer call of the recursion</param>
            <exception cref="T:XmpCore.XmpException">Forwards XMP errors</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpNormalizer.MigrateAudioCopyright(XmpCore.IXmpMeta,XmpCore.Impl.XmpNode)">
            <summary>
            The initial support for WAV files mapped a legacy ID3 audio copyright
            into a new xmpDM:copyright property.
            </summary>
            <remarks>
            The initial support for WAV files mapped a legacy ID3 audio copyright
            into a new xmpDM:copyright property. This is special case code to migrate
            that into dc:rights['x-default']. The rules:
            <list type="number">
              <item>
                If there is no dc:rights array, or an empty array -
                Create one with dc:rights['x-default'] set from double linefeed and xmpDM:copyright.
              </item>
              <item>
                If there is a dc:rights array but it has no x-default item -
                Create an x-default item as a copy of the first item then apply rule #3.
              </item>
              <item>
                If there is a dc:rights array with an x-default item,
                Look for a double linefeed in the value.
                <list type="bullet">
                  <item>If no double linefeed, compare the x-default value to the xmpDM:copyright value.
                    <list type="bullet">
                      <item>If they match then leave the x-default value alone.</item>
                      <item>Otherwise, append a double linefeed and the xmpDM:copyright value to the x-default value.</item>
                    </list>
                  </item>
                  <item>If there is a double linefeed, compare the trailing text to the xmpDM:copyright value.
                    <list type="bullet">
                      <item>If they match then leave the x-default value alone.</item>
                      <item>Otherwise, replace the trailing x-default text with the xmpDM:copyright value.</item>
                    </list>
                  </item>
                </list>
              </item>
              <item>In all cases, delete the xmpDM:copyright property.</item>
            </list>
            </remarks>
            <param name="xmp">the metadata object</param>
            <param name="dmCopyright">the "dm:copyright"-property</param>
        </member>
        <member name="T:XmpCore.Impl.XmpSchemaRegistry">
            <summary>The schema registry handles the namespaces, aliases and global options for the XMP Toolkit.</summary>
            <remarks>
            There is only one singleton instance used by the toolkit, accessed via <see cref="P:XmpCore.XmpMetaFactory.SchemaRegistry"/>.
            </remarks>
            <author>Stefan Makswit</author>
            <since>27.01.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpSchemaRegistry._namespaceToPrefixMap">
            <summary>a map from a namespace URI to its registered prefix.</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSchemaRegistry._prefixToNamespaceMap">
            <summary>a map from a prefix to the associated namespace URI.</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSchemaRegistry._aliasMap">
            <summary>A map of all registered aliases, from qname to IXmpAliasInfo.</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSchemaRegistry._p">
            <summary>The pattern that must not be contained in simple properties</summary>
        </member>
        <member name="M:XmpCore.Impl.XmpSchemaRegistry.#ctor">
            <summary>
            Performs the initialisation of the registry with the default namespaces, aliases and global
            options.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpSchemaRegistry.RegisterStandardNamespaces">
            <summary>
            Register the standard namespaces of schemas and types that are included in the XMP
            Specification and some other Adobe private namespaces.
            </summary>
            <remarks>
            Register the standard namespaces of schemas and types that are included in the XMP
            Specification and some other Adobe private namespaces.
            Note: This method is not lock because only called by the constructor.
            </remarks>
            <exception cref="T:XmpCore.XmpException">Forwards processing exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSchemaRegistry.RegisterAlias(System.String,System.String,System.String,System.String,XmpCore.Options.AliasOptions)">
            <summary>Associates an alias name with an actual name.</summary>
            <remarks>
            Associates an alias name with an actual name.
            <para />
            Define a alias mapping from one namespace/property to another. Both
            property names must be simple names. An alias can be a direct mapping,
            where the alias and actual have the same data type. It is also possible
            to map a simple alias to an item in an array. This can either be to the
            first item in the array, or to the 'x-default' item in an alt-text array.
            Multiple alias names may map to the same actual, as long as the forms
            match. It is a no-op to reregister an alias in an identical fashion.
            Note: This method is not locking because only called by registerStandardAliases
            which is only called by the constructor.
            Note2: The method is only package-private so that it can be tested with unittests
            </remarks>
            <param name="aliasNs">The namespace URI for the alias. Must not be null or the empty string.</param>
            <param name="aliasProp">The name of the alias. Must be a simple name, not null or the empty string and not a general path expression.</param>
            <param name="actualNs">The namespace URI for the actual. Must not be null or the empty string.</param>
            <param name="actualProp">The name of the actual. Must be a simple name, not null or the empty string and not a general path expression.</param>
            <param name="aliasForm">Provides options for aliases for simple aliases to array items. This is needed to know what kind of array to create if
            set for the first time via the simple alias. Pass <c>XMP_NoOptions</c>, the default value, for all direct aliases regardless of whether the actual
            data type is an array or not (see <see cref="T:XmpCore.Options.AliasOptions"/>).</param>
            <exception cref="T:XmpCore.XmpException">for inconsistant aliases.</exception>
        </member>
        <member name="T:XmpCore.Impl.XmpSerializerHelper">
            <summary>
            Serializes the <c>XMPMeta</c>-object to an <c>OutputStream</c> according to the
            <c>SerializeOptions</c>.
            </summary>
            <author>Stefan Makswit</author>
            <since>11.07.2006</since>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerHelper.Serialize(XmpCore.Impl.XmpMeta,System.IO.Stream,XmpCore.Options.SerializeOptions)">
            <summary>Static method to serialize the metadata object.</summary>
            <remarks>
            For each serialisation, a new XMPSerializer
            instance is created, either XMPSerializerRDF or XMPSerializerPlain so that its possible to
            serialize the same XMPMeta objects in two threads.
            </remarks>
            <param name="xmp">a metadata implementation object</param>
            <param name="stream">the output stream to serialize to</param>
            <param name="options">serialization options, can be <c>null</c> for default.</param>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerHelper.SerializeToString(XmpCore.Impl.XmpMeta,XmpCore.Options.SerializeOptions)">
            <summary>Serializes an <c>XMPMeta</c>-object as RDF into a string.</summary>
            <remarks>
            <em>Note:</em> Encoding is forced to UTF-16 when serializing to a
            string to ensure the correctness of &quot;exact packet size&quot;.
            </remarks>
            <param name="xmp">a metadata implementation object</param>
            <param name="options">Options to control the serialization (see <see cref="T:XmpCore.Options.SerializeOptions"/>).</param>
            <returns>Returns a string containing the serialized RDF.</returns>
            <exception cref="T:XmpCore.XmpException">on serialization errors.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerHelper.SerializeToBuffer(XmpCore.Impl.XmpMeta,XmpCore.Options.SerializeOptions)">
            <summary>Serializes an <c>XMPMeta</c>-object as RDF into a byte buffer.</summary>
            <param name="xmp">a metadata implementation object</param>
            <param name="options">Options to control the serialization (see <see cref="T:XmpCore.Options.SerializeOptions"/>).</param>
            <returns>Returns a byte buffer containing the serialized RDF.</returns>
            <exception cref="T:XmpCore.XmpException">on serialization errors.</exception>
        </member>
        <member name="T:XmpCore.Impl.XmpSerializerRdf">
            <summary>Serializes the <c>XMPMeta</c>-object using the standard RDF serialization format.</summary>
            <remarks>
            Serializes the <c>XMPMeta</c>-object using the standard RDF serialization format.
            The output is written to an <c>OutputStream</c>
            according to the <c>SerializeOptions</c>.
            FfF: Move to XMLStreamWriter (a lot of test would break due to slight format change).
            </remarks>
            <author>Stefan Makswit</author>
            <since>11.07.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf.DefaultPad">
            <summary>default padding</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf.PacketTrailer">
            <summary>The w/r is missing inbetween</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf.RdfAttrQualifier">
            <summary>a set of all rdf attribute qualifier</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf._xmp">
            <summary>the metadata object to be serialized.</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf._stream">
            <summary>the output stream to serialize to</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf._writer">
            <summary>this writer is used to do the actual serialization</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf._options">
            <summary>the stored serialization options</summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf._unicodeSize">
            <summary>
            the size of one unicode char, for UTF-8 set to 1
            (Note: only valid for ASCII chars lower than 0x80),
            set to 2 in case of UTF-16
            </summary>
        </member>
        <member name="F:XmpCore.Impl.XmpSerializerRdf._padding">
            <summary>
            the padding in the XMP Packet, or the length of the complete packet in
            case of option <em>exactPacketLength</em>.
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.Serialize(XmpCore.IXmpMeta,System.IO.Stream,XmpCore.Options.SerializeOptions)">
            <summary>The actual serialization.</summary>
            <param name="xmp">the metadata object to be serialized</param>
            <param name="stream">outputStream the output stream to serialize to</param>
            <param name="options">the serialization options</param>
            <exception cref="T:XmpCore.XmpException">If case of wrong options or any other serialization error.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.AddPadding(System.Int32)">
            <summary>Calculates the padding according to the options and write it to the stream.</summary>
            <param name="tailLength">the length of the tail string</param>
            <exception cref="T:XmpCore.XmpException">thrown if packet size is to small to fit the padding</exception>
            <exception cref="T:System.IO.IOException">forwards writer errors</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.CheckOptionsConsistence">
            <summary>Checks if the supplied options are consistent.</summary>
            <exception cref="T:XmpCore.XmpException">Thrown if options are conflicting</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeAsRdf">
            <summary>Writes the (optional) packet header and the outer rdf-tags.</summary>
            <returns>Returns the packet end processing instraction to be written after the padding.</returns>
            <exception cref="T:System.IO.IOException">Forwarded writer exceptions.</exception>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCanonicalRdfSchemas(System.Int32)">
            <summary>Serializes the metadata in pretty-printed manner.</summary>
            <param name="level">indent level</param>
            <exception cref="T:System.IO.IOException">Forwarded writer exceptions</exception>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.WriteTreeName">
            <exception cref="T:System.IO.IOException" />
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfSchemas(System.Int32)">
            <summary>Serializes the metadata in compact manner.</summary>
            <param name="level">indent level to start with</param>
            <exception cref="T:System.IO.IOException">Forwarded writer exceptions</exception>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfAttrProps(XmpCore.Impl.XmpNode,System.Int32)">
            <summary>Write each of the parent's simple unqualified properties as an attribute.</summary>
            <remarks>
            Write each of the parent's simple unqualified properties as an attribute. Returns true if all
            of the properties are written as attributes.
            </remarks>
            <param name="parentNode">the parent property node</param>
            <param name="indent">the current indent level</param>
            <returns>Returns true if all properties can be rendered as RDF attribute.</returns>
            <exception cref="T:System.IO.IOException" />
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfElementProps(XmpCore.Impl.XmpNode,System.Int32)">
            <summary>
            Recursively handles the "value" for a node that must be written as an RDF
            property element.
            </summary>
            <remarks>
            Recursively handles the "value" for a node that must be written as an RDF
            property element. It does not matter if it is a top level property, a
            field of a struct, or an item of an array. The indent is that for the
            property element. The patterns below ignore attribute qualifiers such as
            xml:lang, they don't affect the output form.
            <code>
            &lt;ns:UnqualifiedStructProperty-1
            ... The fields as attributes, if all are simple and unqualified
            /&gt;
            &lt;ns:UnqualifiedStructProperty-2 rdf:parseType=&quot;Resource&quot;&gt;
            ... The fields as elements, if none are simple and unqualified
            &lt;/ns:UnqualifiedStructProperty-2&gt;
            &lt;ns:UnqualifiedStructProperty-3&gt;
            &lt;rdf:Description
            ... The simple and unqualified fields as attributes
            &gt;
            ... The compound or qualified fields as elements
            &lt;/rdf:Description&gt;
            &lt;/ns:UnqualifiedStructProperty-3&gt;
            &lt;ns:UnqualifiedArrayProperty&gt;
            &lt;rdf:Bag&gt; or Seq or Alt
            ... Array items as rdf:li elements, same forms as top level properties
            &lt;/rdf:Bag&gt;
            &lt;/ns:UnqualifiedArrayProperty&gt;
            &lt;ns:QualifiedProperty rdf:parseType=&quot;Resource&quot;&gt;
            &lt;rdf:value&gt; ... Property &quot;value&quot;
            following the unqualified forms ... &lt;/rdf:value&gt;
            ... Qualifiers looking like named struct fields
            &lt;/ns:QualifiedProperty&gt;
            </code>
            *** Consider numbered array items, but has compatibility problems.
            Consider qualified form with rdf:Description and attributes.
            </remarks>
            <param name="parentNode">the parent node</param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">Forwards writer exceptions</exception>
            <exception cref="T:XmpCore.XmpException">If qualifier and element fields are mixed.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfSimpleProp(XmpCore.Impl.XmpNode)">
            <summary>Serializes a simple property.</summary>
            <param name="node">an XMPNode</param>
            <returns>Returns an array containing the flags emitEndTag and indentEndTag.</returns>
            <exception cref="T:System.IO.IOException">Forwards the writer exceptions.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfArrayProp(XmpCore.Impl.XmpNode,System.Int32)">
            <summary>Serializes an array property.</summary>
            <param name="node">an XMPNode</param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">Forwards the writer exceptions.</exception>
            <exception cref="T:XmpCore.XmpException">If qualifier and element fields are mixed.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfStructProp(XmpCore.Impl.XmpNode,System.Int32,System.Boolean)">
            <summary>Serializes a struct property.</summary>
            <param name="node">an XMPNode</param>
            <param name="indent">the current indent level</param>
            <param name="hasRdfResourceQual">Flag if the element has resource qualifier</param>
            <returns>Returns true if an end flag shall be emitted.</returns>
            <exception cref="T:System.IO.IOException">Forwards the writer exceptions.</exception>
            <exception cref="T:XmpCore.XmpException">If qualifier and element fields are mixed.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCompactRdfGeneralQualifier(System.Int32,XmpCore.Impl.XmpNode)">
            <summary>Serializes the general qualifier.</summary>
            <param name="node">the root node of the subtree</param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">Forwards all writer exceptions.</exception>
            <exception cref="T:XmpCore.XmpException">If qualifier and element fields are mixed.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCanonicalRdfSchema(XmpCore.Impl.XmpNode,System.Int32)">
            <summary>
            Serializes one schema with all contained properties in pretty-printed manner.
            </summary>
            <remarks>
            Each schema's properties are written to a single
            rdf:Description element. All of the necessary namespaces are declared in
            the rdf:Description element. The baseIndent is the base level for the
            entire serialization, that of the x:xmpmeta element. An xml:lang
            qualifier is written as an attribute of the property start tag, not by
            itself forcing the qualified property form.
            <code>
            &lt;rdf:Description rdf:about=&quot;TreeName&quot; xmlns:ns=&quot;URI&quot; ... &gt;
            ... The actual properties of the schema, see SerializePrettyRDFProperty
            &lt;!-- ns1:Alias is aliased to ns2:Actual --&gt;  ... If alias comments are wanted
            &lt;/rdf:Description&gt;
            </code>
            </remarks>
            <param name="schemaNode">a schema node</param>
            <param name="level"></param>
            <exception cref="T:System.IO.IOException">Forwarded writer exceptions</exception>
            <exception cref="T:XmpCore.XmpException"></exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.DeclareUsedNamespaces(XmpCore.Impl.XmpNode,System.Collections.Generic.ICollection{System.Object},System.Int32)">
            <summary>Writes all used namespaces of the subtree in node to the output.</summary>
            <remarks>
            Writes all used namespaces of the subtree in node to the output.
            The subtree is recursively traversed.
            </remarks>
            <param name="node">the root node of the subtree</param>
            <param name="usedPrefixes">a set containing currently used prefixes</param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">Forwards all writer exceptions.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.DeclareNamespace(System.String,System.String,System.Collections.Generic.ICollection{System.Object},System.Int32)">
            <summary>Writes one namespace declaration to the output.</summary>
            <param name="prefix">a namespace prefix (without colon) or a complete qname (when namespace == null)</param>
            <param name="ns">the a namespace</param>
            <param name="usedPrefixes">a set containing currently used prefixes</param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">Forwards all writer exceptions.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.StartOuterRdfDescription(XmpCore.Impl.XmpNode,System.Int32)">
            <summary>Start the outer rdf:Description element, including all needed xmlns attributes.</summary>
            <remarks>
            Start the outer rdf:Description element, including all needed xmlns attributes.
            Leave the element open so that the compact form can add property attributes.
            </remarks>
            <exception cref="T:System.IO.IOException">If the writing to</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.EndOuterRdfDescription(System.Int32)">
            <summary>Write the <c>&lt;/rdf:Description&gt;</c> end tag.</summary>
            <exception cref="T:System.IO.IOException" />
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.SerializeCanonicalRdfProperty(XmpCore.Impl.XmpNode,System.Boolean,System.Boolean,System.Int32)">
            <summary>Recursively handles the "value" for a node.</summary>
            <remarks>
            Recursively handles the "value" for a node. It does not matter if it is a
            top level property, a field of a struct, or an item of an array. The
            indent is that for the property element. An xml:lang qualifier is written
            as an attribute of the property start tag, not by itself forcing the
            qualified property form. The patterns below mostly ignore attribute
            qualifiers like xml:lang. Except for the one struct case, attribute
            qualifiers don't affect the output form.
            <code>
            &lt;ns:UnqualifiedSimpleProperty&gt;value&lt;/ns:UnqualifiedSimpleProperty&gt;
            &lt;ns:UnqualifiedStructProperty&gt; (If no rdf:resource qualifier)
            &lt;rdf:Description&gt;
            ... Fields, same forms as top level properties
            &lt;/rdf:Description&gt;
            &lt;/ns:UnqualifiedStructProperty&gt;
            &lt;ns:ResourceStructProperty rdf:resource=&quot;URI&quot;
            ... Fields as attributes
            &gt;
            &lt;ns:UnqualifiedArrayProperty&gt;
            &lt;rdf:Bag&gt; or Seq or Alt
            ... Array items as rdf:li elements, same forms as top level properties
            &lt;/rdf:Bag&gt;
            &lt;/ns:UnqualifiedArrayProperty&gt;
            &lt;ns:QualifiedProperty&gt;
            &lt;rdf:Description&gt;
            &lt;rdf:value&gt; ... Property &quot;value&quot; following the unqualified
            forms ... &lt;/rdf:value&gt;
            ... Qualifiers looking like named struct fields
            &lt;/rdf:Description&gt;
            &lt;/ns:QualifiedProperty&gt;
            </code>
            </remarks>
            <param name="node">the property node</param>
            <param name="emitAsRdfValue">property shall be rendered as attribute rather than tag</param>
            <param name="useCanonicalRdf">
            use canonical form with inner description tag or
            the compact form with rdf:ParseType=&quot;resource&quot; attribute.
            </param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">Forwards all writer exceptions.</exception>
            <exception cref="T:XmpCore.XmpException">If &quot;rdf:resource&quot; and general qualifiers are mixed.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.EmitRdfArrayTag(XmpCore.Impl.XmpNode,System.Boolean,System.Int32)">
            <summary>Writes the array start and end tags.</summary>
            <param name="arrayNode">an array node</param>
            <param name="isStartTag">flag if its the start or end tag</param>
            <param name="indent">the current indent level</param>
            <exception cref="T:System.IO.IOException">forwards writer exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.AppendNodeValue(System.String,System.Boolean)">
            <summary>Serializes the node value in XML encoding.</summary>
            <remarks>
            Serializes the node value in XML encoding. Its used for tag bodies and
            attributes. <em>Note:</em> The attribute is always limited by quotes,
            thats why <c>&amp;apos;</c> is never serialized. <em>Note:</em>
            Control chars are written unescaped, but if the user uses others than tab, LF
            and CR the resulting XML will become invalid.
            </remarks>
            <param name="value">the value of the node</param>
            <param name="forAttribute">flag if value is an attribute value</param>
            <exception cref="T:System.IO.IOException" />
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.CanBeRdfAttrProp(XmpCore.Impl.XmpNode)">
            <summary>
            A node can be serialized as RDF-Attribute, if it meets the following conditions:
            <list type="bullet">
            <item>is not array item</item>
            <item>don't has qualifier</item>
            <item>is no URI</item>
            <item>is no composite property</item>
            </list>
            </summary>
            <param name="node">an XMPNode</param>
            <returns>Returns true if the node serialized as RDF-Attribute</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.WriteIndent(System.Int32)">
            <summary>Writes indents and automatically includes the base indent from the options.</summary>
            <param name="times">number of indents to write</param>
            <exception cref="T:System.IO.IOException">forwards exception</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.Write(System.Int32)">
            <summary>Writes an int to the output.</summary>
            <param name="c">an int</param>
            <exception cref="T:System.IO.IOException">forwards writer exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.Write(System.Char)">
            <summary>Writes a char to the output.</summary>
            <param name="c">a char</param>
            <exception cref="T:System.IO.IOException">forwards writer exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.Write(System.String)">
            <summary>Writes a String to the output.</summary>
            <param name="str">a String</param>
            <exception cref="T:System.IO.IOException">forwards writer exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.WriteChars(System.Int32,System.Char)">
            <summary>Writes an amount of chars, mostly spaces</summary>
            <param name="number">number of chars</param>
            <param name="c">a char</param>
            <exception cref="T:System.IO.IOException" />
        </member>
        <member name="M:XmpCore.Impl.XmpSerializerRdf.WriteNewline">
            <summary>Writes a newline according to the options.</summary>
            <exception cref="T:System.IO.IOException">Forwards exception</exception>
        </member>
        <member name="T:XmpCore.Impl.XmpUtils">
            <author>Stefan Makswit</author>
            <since>11.08.2006</since>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.CatenateArrayItems(XmpCore.IXmpMeta,System.String,System.String,System.String,System.String,System.Boolean)">
            <param name="xmp">The XMP object containing the array to be catenated.</param>
            <param name="schemaNs">
            The schema namespace URI for the array. Must not be null or
            the empty string.
            </param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must
            not be null or the empty string. Each item in the array must
            be a simple string value.
            </param>
            <param name="separator">
            The string to be used to separate the items in the catenated
            string. Defaults to &quot;; &quot;, ASCII semicolon and space
            (U+003B, U+0020).
            </param>
            <param name="quotes">
            The characters to be used as quotes around array items that
            contain a separator. Defaults to &apos;&quot;&apos;
            </param>
            <param name="allowCommas">Option flag to control the catenation.</param>
            <returns>Returns the string containing the catenated array items.</returns>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.SeparateArrayItems(XmpCore.IXmpMeta,System.String,System.String,System.String,XmpCore.Options.PropertyOptions,System.Boolean)">
            <summary>
            See <see cref="M:XmpCore.XmpUtils.SeparateArrayItems(XmpCore.IXmpMeta,System.String,System.String,System.String,XmpCore.Options.PropertyOptions,System.Boolean)"/>.
            </summary>
            <param name="xmp">The XMP object containing the array to be updated.</param>
            <param name="schemaNs">
            The schema namespace URI for the array. Must not be null or the empty string.
            </param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must
            not be null or the empty string. Each item in the array must
            be a simple string value.
            </param>
            <param name="catedStr">The string to be separated into the array items.</param>
            <param name="arrayOptions">Option flags to control the separation.</param>
            <param name="preserveCommas">Flag if commas shall be preserved</param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.SeparateFindCreateArray(System.String,System.String,XmpCore.Options.PropertyOptions,XmpCore.Impl.XmpMeta)">
            <summary>Utility to find or create the array used by <c>separateArrayItems()</c>.</summary>
            <param name="schemaNs">a the namespace fo the array</param>
            <param name="arrayName">the name of the array</param>
            <param name="arrayOptions">the options for the array if newly created</param>
            <param name="xmp">the xmp object</param>
            <returns>Returns the array node.</returns>
            <exception cref="T:XmpCore.XmpException">Forwards exceptions</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.RemoveProperties(XmpCore.IXmpMeta,System.String,System.String,System.Boolean,System.Boolean)">
            <param name="xmp">The XMP object containing the properties to be removed.</param>
            <param name="schemaNs">
            Optional schema namespace URI for the properties to be
            removed.
            </param>
            <param name="propName">Optional path expression for the property to be removed.</param>
            <param name="doAllProperties">
            Option flag to control the deletion: do internal properties in
            addition to external properties.
            </param>
            <param name="includeAliases">
            Option flag to control the deletion: Include aliases in the
            "named schema" case above.
            </param>
            <exception cref="T:XmpCore.XmpException">If metadata processing fails</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.AppendProperties(XmpCore.IXmpMeta,XmpCore.IXmpMeta,System.Boolean,System.Boolean,System.Boolean)">
            <param name="source">The source XMP object.</param>
            <param name="destination">The destination XMP object.</param>
            <param name="doAllProperties">Do internal properties in addition to external properties.</param>
            <param name="replaceOldValues">Replace the values of existing properties.</param>
            <param name="deleteEmptyValues">Delete destination values if source property is empty.</param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.RemoveSchemaChildren(XmpCore.Impl.XmpNode,System.Boolean)">
            <summary>Remove all schema children according to the flag <c>doAllProperties</c>.</summary>
            <remarks>Empty schemas are automatically remove by <c>XMPNode</c>.</remarks>
            <param name="schemaNode">a schema node</param>
            <param name="doAllProperties">flag if all properties or only externals shall be removed.</param>
            <returns>Returns true if the schema is empty after the operation.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.AppendSubtree(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpNode,XmpCore.Impl.XmpNode,System.Boolean,System.Boolean,System.Boolean)">
            <param name="destXmp">The destination XMP object.</param>
            <param name="sourceNode">the source node</param>
            <param name="destParent">the parent of the destination node</param>
            <param name="mergeCompound"></param>
            <param name="replaceOldValues">Replace the values of existing properties.</param>
            <param name="deleteEmptyValues">flag if properties with empty values should be deleted in the destination object.</param>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.ItemValuesMatch(XmpCore.Impl.XmpNode,XmpCore.Impl.XmpNode)">
            <summary>Compares two nodes including its children and qualifier.</summary>
            <param name="leftNode">an <c>XMPNode</c></param>
            <param name="rightNode">an <c>XMPNode</c></param>
            <returns>Returns true if the nodes are equal, false otherwise.</returns>
            <exception cref="T:XmpCore.XmpException">Forwards exceptions to the calling method.</exception>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.CheckSeparator(System.String)">
            <summary>Make sure the separator is OK.</summary>
            <remarks>
            Separators must be one semicolon surrounded by zero or more spaces. Any of the recognized semicolons or spaces are allowed.
            </remarks>
            <param name="separator" />
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.CheckQuotes(System.String,System.Char)">
            <summary>
            Make sure the open and close quotes are a legitimate pair and return the
            correct closing quote or an exception.
            </summary>
            <param name="quotes">opened and closing quote in a string</param>
            <param name="openQuote">the open quote</param>
            <returns>Returns a corresponding closing quote.</returns>
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.ClassifyCharacter(System.Char)">
            <summary>
            Classifies the character into normal chars, spaces, semicola, quotes,
            control chars.
            </summary>
            <param name="ch">a char</param>
            <returns>Return the character kind.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.GetClosingQuote(System.Char)">
            <param name="openQuote">the open quote char</param>
            <returns>Returns the matching closing quote for an open quote.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.ApplyQuotes(System.String,System.Char,System.Char,System.Boolean)">
            <summary>Add quotes to the item.</summary>
            <param name="item">the array item</param>
            <param name="openQuote">the open quote character</param>
            <param name="closeQuote">the closing quote character</param>
            <param name="allowCommas">flag if commas are allowed</param>
            <returns>Returns the value in quotes.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.IsSurroundingQuote(System.Char,System.Char,System.Char)">
            <param name="ch">a character</param>
            <param name="openQuote">the opening quote char</param>
            <param name="closeQuote">the closing quote char</param>
            <returns>Return it the character is a surrounding quote.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.IsClosingQuote(System.Char,System.Char,System.Char)">
            <param name="ch">a character</param>
            <param name="openQuote">the opening quote char</param>
            <param name="closeQuote">the closing quote char</param>
            <returns>Returns true if the character is a closing quote.</returns>
        </member>
        <member name="F:XmpCore.Impl.XmpUtils.Spaces">
            <summary>
            <list type="bullet">
              <item>U+0022 ASCII space</item>
              <item>U+3000, ideographic space</item>
              <item>U+303F, ideographic half fill space</item>
              <item>U+2000..U+200B, en quad through zero width space</item>
            </list>
            </summary>
        </member>
        <member name="F:XmpCore.Impl.XmpUtils.Commas">
            <summary>
            <list type="bullet">
              <item>U+002C, ASCII comma</item>
              <item>U+FF0C, full width comma</item>
              <item>U+FF64, half width ideographic comma</item>
              <item>U+FE50, small comma</item>
              <item>U+FE51, small ideographic comma</item>
              <item>U+3001, ideographic comma</item>
              <item>U+060C, Arabic comma</item>
              <item>U+055D, Armenian comma</item>
            </list>
            </summary>
        </member>
        <member name="F:XmpCore.Impl.XmpUtils.Semicola">
            <summary>
            <list type="bullet">
              <item>U+003B, ASCII semicolon</item>
              <item>U+FF1B, full width semicolon</item>
              <item>U+FE54, small semicolon</item>
              <item>U+061B, Arabic semicolon</item>
              <item>U+037E, Greek "semicolon" (really a question mark)</item>
            </list>
            </summary>
        </member>
        <member name="F:XmpCore.Impl.XmpUtils.Quotes">
            <summary>
            <list type="bullet">
              <item>U+0022 ASCII quote</item>
              <item>U+00AB and U+00BB, guillemet quotes</item>
              <item>U+3008..U+300F, various quotes</item>
              <item>U+301D..U+301F, double prime quotes</item>
              <item>U+2015, dash quote</item>
              <item>U+2018..U+201F, various quotes</item>
              <item>U+2039 and U+203A, guillemet quotes</item>
            </list>
            </summary>
            <remarks>
            The square brackets are not interpreted as quotes anymore (bug #2674672)
            (ASCII '[' (0x5B) and ']' (0x5D) are used as quotes in Chinese and
            Korean.)<br />
            </remarks>
        </member>
        <member name="F:XmpCore.Impl.XmpUtils.Controls">
            <summary>
            <list type="bullet">
              <item>U+0000..U+001F ASCII controls</item>
              <item>U+2028, line separator</item>
              <item>U+2029, paragraph separator</item>
            </list>
            </summary>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.MoveOneProperty(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpMeta,System.String,System.String)">
            <summary>Moves the specified Property from one Meta to another.</summary>
            <param name="stdXMP">Meta Object from where the property needs to move</param>
            <param name="extXMP">Meta Object to where the property needs to move</param>
            <param name="schemaURI">Schema of the specified property</param>
            <param name="propName">Name of the property</param>
            <returns>true in case of success otherwise false.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.EstimateSizeForJPEG(XmpCore.Impl.XmpNode)">
            <summary>estimates the size of an xmp node</summary>
            <param name="xmpNode">XMP Node Object</param>
            <returns>the estimated size of the node</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.PutObjectsInMultiMap(System.Collections.Generic.SortedDictionary{System.Int32,System.Collections.Generic.List{System.Collections.Generic.List{System.String}}},System.Int32,System.Collections.Generic.List{System.String})">
            <summary>Utility function for placing objects in a Map. It behaves like a multi map.</summary>
            <param name="multiMap">A Map object which takes int as a key and list of list of string as value</param>
            <param name="key">A key for the map</param>
            <param name="stringPair">A value for the map</param>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.GetBiggestEntryInMultiMap(System.Collections.Generic.SortedDictionary{System.Int32,System.Collections.Generic.List{System.Collections.Generic.List{System.String}}})">
            <summary>Utility function for retrieving biggest entry in the multimap</summary>
            <remarks>see EstimateSizeForJPEG for size calculation</remarks>
            <param name="multiMap">A Map object which takes int as a key and list of list of string as value</param>
            <returns>the list with the maximum size.</returns>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.CreateEstimatedSizeMap(XmpCore.Impl.XmpMeta,System.Collections.Generic.SortedDictionary{System.Int32,System.Collections.Generic.List{System.Collections.Generic.List{System.String}}})">
            <summary>Utility function for creating esimated size map for different properties of XMP Packet.</summary>
            <remarks>see PackageForJPEG</remarks>
            <param name="stdXMP">Meta Object whose property sizes needs to calculate.</param>
            <param name="propSizes">A treeMap Object which takes int as a key and list of list of string as values</param>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.MoveLargestProperty(XmpCore.Impl.XmpMeta,XmpCore.Impl.XmpMeta,System.Collections.Generic.SortedDictionary{System.Int32,System.Collections.Generic.List{System.Collections.Generic.List{System.String}}})">
            <summary>Utility function for moving the largest property from One XMP Packet to another.</summary>
            <remarks>see MoveOneProperty and PackageForJPEG</remarks>
            <param name="stdXMP">Meta Object from where property moves.</param>
            <param name="extXMP">Meta Object to where property moves.</param>
            <param name="propSizes">A treeMap Object which holds the estimated sizes of the property of stdXMP as a key and their string representation as map values.</param>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.PackageForJPEG(XmpCore.IXmpMeta,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>creates XMP serializations appropriate for a JPEG file.</summary>
            <remarks>
            The standard XMP in a JPEG file is limited to 64K bytes. This function
            serializes the XMP metadata in an XMP object into a string of RDF.If
            the data does not fit into the 64K byte limit, it creates a second packet
            string with the extended data.
            </remarks>
            <param name="origXMPImpl">The XMP object containing the metadata.</param>
            <param name="stdStr">A string object in which to return the full standard XMP packet.</param>
            <param name="extStr">A string object in which to return the serialized extended XMP, empty if not needed.</param>
            <param name="digestStr">A string object in which to return an MD5 digest of the serialized extended XMP, empty if not needed.</param>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.MergeFromJPEG(XmpCore.IXmpMeta,XmpCore.IXmpMeta)">
            <summary>merges standard and extended XMP retrieved from a JPEG file.</summary>
            <remarks>
            When an extended partition stores properties that do not fit into the
            JPEG file limitation of 64K bytes, this function integrates those
            properties back into the same XMP object with those from the standard XMP
            packet.
            </remarks>
            <param name="fullXMP">An XMP object which the caller has initialized from the standard XMP packet in a JPEG file. The extended XMP is added to this object.</param>
            <param name="extendedXMP">An XMP object which the caller has initialized from the extended XMP packet in a JPEG file.</param>
        </member>
        <member name="M:XmpCore.Impl.XmpUtils.ApplyTemplate(XmpCore.IXmpMeta,XmpCore.IXmpMeta,XmpCore.Options.TemplateOptions)">
            <summary>modifies a working XMP object according to a template object.</summary>
            <remarks>
            The XMP template can be used to add, replace or delete properties from
            the working XMP object. The actions that you specify determine how the
            template is applied.Each action can be applied individually or combined;
            if you do not specify any actions, the properties and values in the
            working XMP object do not change.
            </remarks>
            <param name="origXMP">The destination XMP object.</param>
            <param name="tempXMP">The template to apply to the destination XMP object.</param>
            <param name="actions">Option flags to control the copying. If none are specified,
               the properties and values in the working XMP do not change. A logical OR of these bit-flag constants:
               <ul>
               <li><code> CLEAR_UNNAMED_PROPERTIES</code> Delete anything that is not in the template.</li>
               <li><code> ADD_NEW_PROPERTIES</code> Add properties; see detailed description.</li>
               <li><code> REPLACE_EXISTING_PROPERTIES</code> Replace the values of existing properties.</li>
               <li><code> REPLACE_WITH_DELETE_EMPTY</code> Replace the values of existing properties and delete properties if the new value is empty.</li>
               <li><code> INCLUDE_INTERNAL_PROPERTIES</code> Operate on internal properties as well as external properties.</li>
               </ul>
            </param>
        </member>
        <member name="F:XmpCore.Impl.XPath.XmpPathStepType.StructFieldStep">
            <summary>Marks a struct field step, also for top level nodes (schema "fields").</summary>
        </member>
        <member name="F:XmpCore.Impl.XPath.XmpPathStepType.QualifierStep">
            <summary>Marks a qualifier step.</summary>
            <remarks>
            Marks a qualifier step.
            Note: Order is significant to separate struct/qual from array kinds!
            </remarks>
        </member>
        <member name="F:XmpCore.Impl.XPath.XmpPathStepType.ArrayIndexStep">
            <summary>Marks an array index step</summary>
        </member>
        <member name="T:XmpCore.Impl.XPath.XmpPath">
            <summary>Represents an XMP XmpPath with segment accessor methods.</summary>
            <since>28.02.2006</since>
        </member>
        <member name="F:XmpCore.Impl.XPath.XmpPath._segments">
            <summary>stores the segments of an <see cref="T:XmpCore.Impl.XPath.XmpPath"/></summary>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPath.Add(XmpCore.Impl.XPath.XmpPathSegment)">
            <summary>Append a path segment</summary>
            <param name="segment">the segment to add</param>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPath.GetSegment(System.Int32)">
            <param name="index">the index of the segment to return</param>
            <returns>Returns a path segment.</returns>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPath.Size">
            <returns>Returns the size of the xmp path.</returns>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPath.ToString">
            <summary>Serializes the normalized XMP-path.</summary>
        </member>
        <member name="T:XmpCore.Impl.XPath.XmpPathParser">
            <summary>Parser for XMP XPaths.</summary>
            <since>01.03.2006</since>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.ExpandXPath(System.String,System.String)">
            <summary>
            Split an <see cref="T:XmpCore.Impl.XPath.XmpPath"/> expression apart at the conceptual steps, adding the
            root namespace prefix to the first property component.
            </summary>
            <remarks>
            The schema URI is put in the first (0th) slot in the expanded <see cref="T:XmpCore.Impl.XPath.XmpPath"/>.
            Check if the top level component is an alias, but don't resolve it.
            <para />
            In the most verbose case steps are separated by '/', and each step can be
            of these forms:
            <list>
              <item>
                <term>prefix:name</term>
                <description>A top level property or struct field.</description>
              </item>
              <item>
                <term>[index]</term>
                <description>An element of an array.</description>
              </item>
              <item>
                <term>[last()]</term>
                <description>The last element of an array.</description>
              </item>
              <item>
                <term>[fieldName=&quot;value&quot;]</term>
                <description>An element in an array of structs, chosen by a field value.</description>
              </item>
              <item>
                <term>[@xml:lang=&quot;value&quot;]</term>
                <description>An element in an alt-text array, chosen by the xml:lang qualifier.</description>
              </item>
              <item>
                <term>[?qualName=&quot;value&quot;]</term>
                <description>An element in an array, chosen by a qualifier value.</description>
              </item>
              <item>
                <term>@xml:lang</term>
                <description>An xml:lang qualifier.</description>
              </item>
              <item>
                <term>?qualName</term>
                <description>A general qualifier.</description>
              </item>
            </list>
            <para />
            The logic is complicated though by shorthand for arrays, the separating
            '/' and leading '*' are optional. These are all equivalent: array/*[2]
            array/[2] array*[2] array[2] All of these are broken into the 2 steps
            "array" and "[2]".
            <para />
            The value portion in the array selector forms is a string quoted by '''
            or '"'. The value may contain any character including a doubled quoting
            character. The value may be empty.
            <para />
            The syntax isn't checked, but an XML name begins with a letter or '_',
            and contains letters, digits, '.', '-', '_', and a bunch of special
            non-ASCII Unicode characters. An XML qualified name is a pair of names
            separated by a colon.
            </remarks>
            <param name="schemaNs">schema namespace</param>
            <param name="path">property name</param>
            <returns>Returns the expanded <see cref="T:XmpCore.Impl.XPath.XmpPath"/>.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown if the format is not correct somehow.</exception>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.SkipPathDelimiter(System.String,XmpCore.Impl.XPath.PathPosition)">
            <param name="path" />
            <param name="pos" />
            <exception cref="T:XmpCore.XmpException" />
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.ParseStructSegment(XmpCore.Impl.XPath.PathPosition)">
            <summary>Parses a struct segment</summary>
            <param name="pos">the current position in the path</param>
            <returns>The segment or an error</returns>
            <exception cref="T:XmpCore.XmpException">If the segment is empty</exception>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.ParseIndexSegment(XmpCore.Impl.XPath.PathPosition)">
            <summary>Parses an array index segment.</summary>
            <param name="pos">the xmp path</param>
            <returns>Returns the segment or an error</returns>
            <exception cref="T:XmpCore.XmpException">thrown on xmp path errors</exception>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.ParseRootNode(System.String,XmpCore.Impl.XPath.PathPosition,XmpCore.Impl.XPath.XmpPath)">
            <summary>
            Parses the root node of an XMP Path, checks if namespace and prefix fit together
            and resolve the property to the base property if it is an alias.
            </summary>
            <param name="schemaNs">the root namespace</param>
            <param name="pos">the parsing position helper</param>
            <param name="expandedXPath">the path to contribute to</param>
            <exception cref="T:XmpCore.XmpException">If the path is not valid.</exception>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.VerifyQualName(System.String)">
            <summary>
            Verifies whether the qualifier name is not XML conformant or the
            namespace prefix has not been registered.
            </summary>
            <param name="qualName">a qualifier name</param>
            <exception cref="T:XmpCore.XmpException">If the name is not conformant</exception>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.VerifySimpleXmlName(System.String)">
            <summary>Verify if an XML name is conformant.</summary>
            <param name="name">an XML name</param>
            <exception cref="T:XmpCore.XmpException">When the name is not XML conformant</exception>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathParser.VerifyXPathRoot(System.String,System.String)">
            <summary>Set up the first 2 components of the expanded <see cref="T:XmpCore.Impl.XPath.XmpPath"/>.</summary>
            <remarks>
            Normalizes the various cases of using
            the full schema URI and/or a qualified root property name. Returns true for normal
            processing. If allowUnknownSchemaNS is true and the schema namespace is not registered, false
            is returned. If allowUnknownSchemaNS is false and the schema namespace is not registered, an
            exception is thrown
            <para />
            (Should someday check the full syntax:)
            </remarks>
            <param name="schemaNs">schema namespace</param>
            <param name="rootProp">the root xpath segment</param>
            <returns>Returns root QName.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown if the format is not correct somehow.</exception>
        </member>
        <member name="T:XmpCore.Impl.XPath.PathPosition">
            <summary>This objects contains all needed char positions to parse.</summary>
        </member>
        <member name="F:XmpCore.Impl.XPath.PathPosition.Path">
            <summary>the complete path</summary>
        </member>
        <member name="F:XmpCore.Impl.XPath.PathPosition.NameStart">
            <summary>the start of a segment name</summary>
        </member>
        <member name="F:XmpCore.Impl.XPath.PathPosition.NameEnd">
            <summary>the end of a segment name</summary>
        </member>
        <member name="F:XmpCore.Impl.XPath.PathPosition.StepBegin">
            <summary>the begin of a step</summary>
        </member>
        <member name="F:XmpCore.Impl.XPath.PathPosition.StepEnd">
            <summary>the end of a step</summary>
        </member>
        <member name="T:XmpCore.Impl.XPath.XmpPathSegment">
            <summary>A segment of a parsed <see cref="T:XmpCore.Impl.XPath.XmpPath"/>.</summary>
            <since>23.06.2006</since>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathSegment.#ctor(System.String)">
            <summary>Constructor with initial values.</summary>
            <param name="name">the name of the segment</param>
        </member>
        <member name="M:XmpCore.Impl.XPath.XmpPathSegment.#ctor(System.String,XmpCore.Impl.XPath.XmpPathStepType)">
            <summary>Constructor with initial values.</summary>
            <param name="name">the name of the segment</param>
            <param name="kind">the kind of the segment</param>
        </member>
        <member name="P:XmpCore.Impl.XPath.XmpPathSegment.Kind">
            <value>Get and set the kind of the path segment.</value>
        </member>
        <member name="P:XmpCore.Impl.XPath.XmpPathSegment.Name">
            <value>Get and set the name of the path segment.</value>
        </member>
        <member name="P:XmpCore.Impl.XPath.XmpPathSegment.IsAlias">
            <value>Get and set whether the segment is an alias.</value>
        </member>
        <member name="P:XmpCore.Impl.XPath.XmpPathSegment.AliasForm">
            <value>Get and set the alias form, if this segment has been created by an alias.</value>
        </member>
        <member name="T:XmpCore.IXmpAliasInfo">
            <summary>This interface is used to return info about an alias.</summary>
            <author>Stefan Makswit</author>
            <since>27.01.2006</since>
        </member>
        <member name="P:XmpCore.IXmpAliasInfo.Namespace">
            <value>Gets the namespace URI for the base property.</value>
        </member>
        <member name="P:XmpCore.IXmpAliasInfo.Prefix">
            <value>Gets the default prefix for the given base property.</value>
        </member>
        <member name="P:XmpCore.IXmpAliasInfo.PropName">
            <value>Gets the path of the base property.</value>
        </member>
        <member name="P:XmpCore.IXmpAliasInfo.AliasForm">
            <value>
            Gets the kind of the alias. This can be a direct alias
            (ARRAY), a simple property to an ordered array
            (ARRAY_ORDERED), to an alternate array
            (ARRAY_ALTERNATE) or to an alternate text array
            (ARRAY_ALT_TEXT).
            </value>
        </member>
        <member name="T:XmpCore.IXmpDateTime">
            <summary>
            The <c>XMPDateTime</c>-class represents a point in time up to a resolution of nanoseconds.
            </summary>
            <remarks>
            Dates and time in the serialized XMP are ISO 8601 strings. There are utility functions
            to convert to the ISO format, a <c>Calendar</c> or get the Timezone. The fields of
            <c>XMPDateTime</c> are:
            <list type="bullet">
            <item>month - The month in the range 1..12.</item>
            <item>day - The day of the month in the range 1..31.</item>
            <item>minute - The minute in the range 0..59.</item>
            <item>hour - The time zone hour in the range 0..23.</item>
            <item>minute - The time zone minute in the range 0..59.</item>
            <item>nanosecond - The nanoseconds within a second. <em>Note:</em> if the XMPDateTime is
            converted into a calendar, the resolution is reduced to milliseconds.</item>
            <item>timeZone - a <c>TimeZone</c>-object.</item>
            </list>
            DateTime values are occasionally used in cases with only a date or only a time component. A date
            without a time has zeros for all the time fields. A time without a date has zeros for all date
            fields (year, month, and day).
            </remarks>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Year">
            <value>Get and set the year value. Can be negative.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Month">
            <value>Get and set the month, within range 1..12.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Day">
            <value>Get and set the day of the month, within range 1..31.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Hour">
            <value>Returns hour - The hour in the range 0..23.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Minute">
            <value>Get and set the minute, within range 0..59.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Second">
            <value>Get and set the second, within range 0..59.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Nanosecond">
            <value>Get and set the sub-second period, in nanoseconds.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.TimeZone">
            <value>Get and set the time zone.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Offset">
            <value>Get and set the offset, primarily for ISO8601 converter.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.HasDate">
            <summary>This flag is set either by parsing or by setting year, month or day.</summary>
            <value>Returns true if the XMPDateTime object has a date portion.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.HasTime">
            <summary>This flag is set either by parsing or by setting hours, minutes, seconds or milliseconds.</summary>
            <value>Returns true if the XMPDateTime object has a time portion.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.HasTimeZone">
            <summary>This flag is set either by parsing or by setting hours, minutes, seconds or milliseconds.</summary>
            <value>Returns true if the XMPDateTime object has a defined timezone.</value>
        </member>
        <member name="P:XmpCore.IXmpDateTime.Calendar">
            <summary>
            Returns a <c>Calendar</c> (only with millisecond precision).
            </summary>
            <remarks>
            Dates before Oct 15th 1585 (which normally fall into validity of
            the Julian calendar) are also rendered internally as Gregorian dates.
            </remarks>
        </member>
        <member name="M:XmpCore.IXmpDateTime.ToIso8601String">
            <returns>Returns the ISO 8601 string representation of the date and time.</returns>
        </member>
        <member name="T:XmpCore.IXmpIterator">
            <summary>Interface for the <c>XMPMeta</c> iteration services.</summary>
            <remarks>
            <c>XMPIterator</c> provides a uniform means to iterate over the
            schema and properties within an XMP object.
            <para />
            The iteration over the schema and properties within an XMP object is very
            complex. It is helpful to have a thorough understanding of the XMP data tree.
            One way to learn this is to create some complex XMP and examine the output of
            <c>XMPMeta#toString</c>. This is also described in the XMP
            Specification, in the XMP Data Model chapter.
            <para />
            The top of the XMP data tree is a single root node. This does not explicitly
            appear in the dump and is never visited by an iterator (that is, it is never
            returned from <c>XMPIterator#next()</c>). Beneath the root are
            schema nodes. These are just collectors for top level properties in the same
            namespace. They are created and destroyed implicitly. Beneath the schema
            nodes are the property nodes. The nodes below a property node depend on its
            type (simple, struct, or array) and whether it has qualifiers.
            <para />
            An <c>XMPIterator</c> is created by XMPMeta#iterator() constructor
            defines a starting point for the iteration and options that control how it
            proceeds. By default the iteration starts at the root and visits all nodes
            beneath it in a depth first manner. The root node is not visited, the first
            visited node is a schema node. You can provide a schema name or property path
            to select a different starting node. By default this visits the named root
            node first then all nodes beneath it in a depth first manner.
            <para />
            The <c>XMPIterator#next()</c> method delivers the schema URI, path,
            and option flags for the node being visited. If the node is simple it also
            delivers the value. Qualifiers for this node are visited next. The fields of
            a struct or items of an array are visited after the qualifiers of the parent.
            <para />
            The options to control the iteration are:
            <list type="bullet">
            <item>JUST_CHILDREN - Visit just the immediate children of the root. Skip
            the root itself and all nodes below the immediate children. This omits the
            qualifiers of the immediate children, the qualifier nodes being below what
            they qualify, default is to visit the complete subtree.</item>
            <item>JUST_LEAFNODES - Visit just the leaf property nodes and their
            qualifiers.</item>
            <item>JUST_LEAFNAME - Return just the leaf component of the node names.
            The default is to return the full xmp path.</item>
            <item>OMIT_QUALIFIERS - Do not visit the qualifiers.</item>
            <item>INCLUDE_ALIASES - Adds known alias properties to the properties in the iteration.
            <em>Note:</em> Not supported in Java or .NET XMPCore!</item>
            </list>
            <para />
            <c>next()</c> returns <c>XMPPropertyInfo</c>-objects and throws
            a <c>NoSuchElementException</c> if there are no more properties to
            return.
            </remarks>
            <author>Stefan Makswit</author>
            <since>25.01.2006</since>
        </member>
        <member name="M:XmpCore.IXmpIterator.SkipSubtree">
            <summary>
            Skip the subtree below the current node when <c>next()</c> is
            called.
            </summary>
        </member>
        <member name="M:XmpCore.IXmpIterator.SkipSiblings">
            <summary>
            Skip the subtree below and remaining siblings of the current node when
            <c>next()</c> is called.
            </summary>
        </member>
        <member name="T:XmpCore.IXmpMeta">
            <summary>This class represents the set of XMP metadata as a DOM representation.</summary>
            <remarks>
            It has methods to read and modify all kinds of properties, create an iterator over all properties
            and serialize the metadata to a string, byte array or stream.
            </remarks>
            <author>Stefan Makswit</author>
            <since>20.01.2006</since>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)">
            <summary>
            The property value getter-methods all take a property specification: the first two parameters
            are always the top level namespace URI (the &quot;schema&quot; namespace) and the basic name
            of the property being referenced.
            </summary>
            <remarks>
            See the introductory discussion of path expression usage for more information.
            <para />
            All of the functions return an object inherited from <c>PropertyBase</c> or
            <c>null</c> if the property does not exists. The result object contains the value of
            the property and option flags describing the property. Arrays and the non-leaf levels of
            nodes do not have values.
            <para />
            See <see cref="T:XmpCore.Options.PropertyOptions"/> for detailed information about the options.
            <para />
            This is the simplest property getter, mainly for top level simple properties or after using
            the path composition functions in <see cref="T:XmpCore.XmpPathFactory"/>.
            </remarks>
            <param name="schemaNs">
            The namespace URI for the property. May be <c>null</c> or the empty
            string if the first component of the propName path contains a namespace prefix. The
            URI must be for a registered namespace.
            </param>
            <param name="propName">
            The name of the property. May be a general path expression, must not be
            <c>null</c> or the empty string. Using a namespace prefix on the first
            component is optional. If present without a schemaNS value then the prefix specifies
            the namespace. The prefix must be for a registered namespace. If both a schemaNS URI
            and propName prefix are present, they must be corresponding parts of a registered
            namespace.
            </param>
            <returns>
            Returns an <see cref="T:XmpCore.IXmpProperty"/> containing the value and the options, or
            <c>null</c> if the property does not exist.
            </returns>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetArrayItem(System.String,System.String,System.Int32)">
            <summary>Provides access to items within an array.</summary>
            <remarks>
            The index is passed as an integer, you need not
            worry about the path string syntax for array items, convert a loop index to a string, etc.
            </remarks>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="itemIndex">
            The index of the desired item. Arrays in XMP are indexed from 1. The constant
            <see cref="F:XmpCore.XmpConstants.ArrayLastItem"/> always refers to the last existing array item.
            </param>
            <returns>
            Returns an <see cref="T:XmpCore.IXmpProperty"/> containing the value and the options or
            <c>null</c> if the property does not exist.
            </returns>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.CountArrayItems(System.String,System.String)">
            <summary>Returns the number of items in the array.</summary>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <returns>Returns the number of items in the array.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetStructField(System.String,System.String,System.String,System.String)">
            <summary>Provides access to fields within a nested structure.</summary>
            <remarks>
            The namespace for the field is passed as a URI, you need not worry about the path string syntax.
            <para />
            The names of fields should be XML qualified names, that is within an XML namespace. The path
            syntax for a qualified name uses the namespace prefix. This is unreliable since the prefix is
            never guaranteed. The URI is the formal name, the prefix is just a local shorthand in a given
            sequence of XML text.
            </remarks>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="structName">
            The name of the struct. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="fieldNs">
            The namespace URI for the field. Has the same URI and prefix usage as the
            schemaNS parameter.
            </param>
            <param name="fieldName">
            The name of the field. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            structName parameter.
            </param>
            <returns>
            Returns an <see cref="T:XmpCore.IXmpProperty"/> containing the value and the options or
            <c>null</c> if the property does not exist. Arrays and non-leaf levels of
            structs do not have values.
            </returns>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetQualifier(System.String,System.String,System.String,System.String)">
            <summary>Provides access to a qualifier attached to a property.</summary>
            <remarks>
            The namespace for the qualifier is passed as a URI, you need not worry about the path string syntax.
            In many regards qualifiers are like struct fields. See the introductory discussion of qualified
            properties for more information.
            <para />
            The names of qualifiers should be XML qualified names, that is within an XML namespace. The
            path syntax for a qualified name uses the namespace prefix. This is unreliable since the
            prefix is never guaranteed. The URI is the formal name, the prefix is just a local shorthand
            in a given sequence of XML text.
            <para />
            <em>Note:</em> Qualifiers are only supported for simple leaf properties at this time.
            </remarks>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">
            The name of the property to which the qualifier is attached. May be a general
            path expression, must not be <c>null</c> or the empty string. Has the same
            namespace prefix usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="qualNs">
            The namespace URI for the qualifier. Has the same URI and prefix usage as the
            schemaNS parameter.
            </param>
            <param name="qualName">
            The name of the qualifier. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            propName parameter.
            </param>
            <returns>
            Returns an <see cref="T:XmpCore.IXmpProperty"/> containing the value and the options of the
            qualifier or <c>null</c> if the property does not exist. The name of the
            qualifier must be a single XML name, must not be <c>null</c> or the empty
            string. Has the same namespace prefix usage as the propName parameter.
            <para />
            The value of the qualifier is only set if it has one (Arrays and non-leaf levels of
            structs do not have values).
            </returns>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetProperty(System.String,System.String,System.Object,XmpCore.Options.PropertyOptions)">
            <summary>
            The property value setters all take a property specification, their
            differences are in the form of this.
            </summary>
            <remarks>
            The first two parameters are always the top level namespace URI (the <c>schema</c> namespace) and
            the basic name of the property being referenced. See the introductory discussion of path expression
            usage for more information.
            <para />
            All of the functions take a string value for the property and option flags describing the
            property. The value must be Unicode in UTF-8 encoding. Arrays and non-leaf levels of structs
            do not have values. Empty arrays and structs may be created using appropriate option flags.
            All levels of structs that is assigned implicitly are created if necessary. appendArayItem
            implicitly creates the named array if necessary.
            <para />
            See <see cref="T:XmpCore.Options.PropertyOptions"/> for detailed information about the options.
            <para />
            This is the simplest property setter, mainly for top level simple properties or after using
            the path composition functions in <see cref="T:XmpCore.XmpPathFactory"/>.
            </remarks>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">
            The name of the property.
            Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="propValue">
            the value for the property (only leaf properties have a value).
            Arrays and non-leaf levels of structs do not have values.
            Must be <c>null</c> if the value is not relevant.<br/>
            The value is automatically detected: Boolean, Integer, Long, Double, <see cref="T:XmpCore.IXmpDateTime"/> and
            byte[] are handled, on all other <see cref="M:System.Object.ToString"/> is called.
            </param>
            <param name="options">Option flags describing the property. See the earlier description.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetProperty(System.String,System.String,System.Object)">
            <seealso cref="M:XmpCore.IXmpMeta.SetProperty(System.String,System.String,System.Object,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the value for the property</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetArrayItem(System.String,System.String,System.Int32,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Replaces an item within an array.</summary>
            <remarks>
            The index is passed as an integer, you need not worry about
            the path string syntax for array items, convert a loop index to a string, etc. The array
            passed must already exist. In normal usage the selected array item is modified. A new item is
            automatically appended if the index is the array size plus 1.
            </remarks>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="itemIndex">
            The index of the desired item. Arrays in XMP are indexed from 1. To address
            the last existing item, use
            <see cref="M:XmpCore.IXmpMeta.CountArrayItems(System.String,System.String)"/>
            to find
            out the length of the array.
            </param>
            <param name="itemValue">
            the new value of the array item. Has the same usage as propValue in
            <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="options">the set options for the item.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetArrayItem(System.String,System.String,System.Int32,System.String)">
            <seealso cref="M:XmpCore.IXmpMeta.SetArrayItem(System.String,System.String,System.Int32,System.String,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI</param>
            <param name="arrayName">The name of the array</param>
            <param name="itemIndex">The index to insert the new item</param>
            <param name="itemValue">the new value of the array item</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.InsertArrayItem(System.String,System.String,System.Int32,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Inserts an item into an array previous to the given index.</summary>
            <remarks>
            The index is passed as an integer,
            you need not worry about the path string syntax for array items, convert a loop index to a
            string, etc. The array passed must already exist. In normal usage the selected array item is
            modified. A new item is automatically appended if the index is the array size plus 1.
            </remarks>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="itemIndex">
            The index to insert the new item. Arrays in XMP are indexed from 1. Use
            <see cref="F:XmpCore.XmpConstants.ArrayLastItem"/> to append items.
            </param>
            <param name="itemValue">
            the new value of the array item. Has the same usage as
            propValue in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="options">the set options that decide about the kind of the node.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.InsertArrayItem(System.String,System.String,System.Int32,System.String)">
            <seealso cref="M:XmpCore.IXmpMeta.InsertArrayItem(System.String,System.String,System.Int32,System.String,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the array</param>
            <param name="arrayName">The name of the array</param>
            <param name="itemIndex">The index to insert the new item</param>
            <param name="itemValue">the value of the array item</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.AppendArrayItem(System.String,System.String,XmpCore.Options.PropertyOptions,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Simplifies the construction of an array by not requiring that you pre-create an empty array.</summary>
            <remarks>
            The array that is assigned is created automatically if it does not yet exist. Each call to
            appendArrayItem() appends an item to the array. The corresponding parameters have the same
            use as setArrayItem(). The arrayOptions parameter is used to specify what kind of array. If
            the array exists, it must have the specified form.
            </remarks>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be null or
            the empty string. Has the same namespace prefix usage as propPath in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="arrayOptions">
            Option flags describing the array form. The only valid options are
            <list type="bullet">
            <item><see cref="F:XmpCore.Options.PropertyOptions.ArrayFlag"/>,</item>
            <item><see cref="F:XmpCore.Options.PropertyOptions.ArrayOrderedFlag"/>,</item>
            <item><see cref="F:XmpCore.Options.PropertyOptions.ArrayAlternateFlag"/> or</item>
            <item><see cref="F:XmpCore.Options.PropertyOptions.ArrayAltTextFlag"/>.</item>
            </list>
            <em>Note:</em> the array options only need to be provided if the array is not
            already existing, otherwise you can set them to <c>null</c> or use
            <see cref="M:XmpCore.IXmpMeta.AppendArrayItem(System.String,System.String,System.String)"/>.
            </param>
            <param name="itemValue">the value of the array item. Has the same usage as propValue in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="itemOptions">Option flags describing the item to append (<see cref="T:XmpCore.Options.PropertyOptions"/>)</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.AppendArrayItem(System.String,System.String,System.String)">
            <seealso cref="M:XmpCore.IXmpMeta.AppendArrayItem(System.String,System.String,XmpCore.Options.PropertyOptions,System.String,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the array</param>
            <param name="arrayName">The name of the array</param>
            <param name="itemValue">the value of the array item</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetStructField(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Provides access to fields within a nested structure.</summary>
            <remarks>
            The namespace for the field is passed as
            a URI, you need not worry about the path string syntax. The names of fields should be XML
            qualified names, that is within an XML namespace. The path syntax for a qualified name uses
            the namespace prefix, which is unreliable because the prefix is never guaranteed. The URI is
            the formal name, the prefix is just a local shorthand in a given sequence of XML text.
            </remarks>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="structName">
            The name of the struct. May be a general path expression, must not be null
            or the empty string. Has the same namespace prefix usage as propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="fieldNs">
            The namespace URI for the field. Has the same URI and prefix usage as the
            schemaNS parameter.
            </param>
            <param name="fieldName">
            The name of the field. Must be a single XML name, must not be null or the
            empty string. Has the same namespace prefix usage as the structName parameter.
            </param>
            <param name="fieldValue">
            the value of thefield, if the field has a value.
            Has the same usage as propValue in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="options">Option flags describing the field. See the earlier description.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetStructField(System.String,System.String,System.String,System.String,System.String)">
            <seealso cref="M:XmpCore.IXmpMeta.SetStructField(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the struct</param>
            <param name="structName">The name of the struct</param>
            <param name="fieldNs">The namespace URI for the field</param>
            <param name="fieldName">The name of the field</param>
            <param name="fieldValue">the value of the field</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetQualifier(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Provides access to a qualifier attached to a property.</summary>
            <remarks>
            The namespace for the qualifier is passed as a URI, you need not worry about the path string syntax.
            In many regards qualifiers are like struct fields. See the introductory discussion of qualified properties
            for more information. The names of qualifiers should be XML qualified names, that is within an XML
            namespace. The path syntax for a qualified name uses the namespace prefix, which is
            unreliable because the prefix is never guaranteed. The URI is the formal name, the prefix is
            just a local shorthand in a given sequence of XML text. The property the qualifier
            will be attached has to exist.
            </remarks>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property to which the qualifier is attached. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="qualNs">The namespace URI for the qualifier. Has the same URI and prefix usage as the schemaNS parameter.</param>
            <param name="qualName">
            The name of the qualifier. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            propName parameter.
            </param>
            <param name="qualValue">
            A pointer to the <c>null</c> terminated UTF-8 string that is the
            value of the qualifier, if the qualifier has a value. Has the same usage as propValue
            in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="options">Option flags describing the qualifier. See the earlier description.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetQualifier(System.String,System.String,System.String,System.String,System.String)">
            <seealso cref="M:XmpCore.IXmpMeta.SetQualifier(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the struct</param>
            <param name="propName">The name of the property to which the qualifier is attached</param>
            <param name="qualNs">The namespace URI for the qualifier</param>
            <param name="qualName">The name of the qualifier</param>
            <param name="qualValue">the value of the qualifier</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.DeleteProperty(System.String,System.String)">
            <summary>Deletes the given XMP subtree rooted at the given property.</summary>
            <remarks>It is not an error if the property does not exist.</remarks>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
        </member>
        <member name="M:XmpCore.IXmpMeta.DeleteArrayItem(System.String,System.String,System.Int32)">
            <summary>Deletes the given XMP subtree rooted at the given array item.</summary>
            <remarks>It is not an error if the array item does not exist.</remarks>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="itemIndex">
            The index of the desired item. Arrays in XMP are indexed from 1. The
            constant <see cref="F:XmpCore.XmpConstants.ArrayLastItem"/> always refers to the last
            existing array item.
            </param>
        </member>
        <member name="M:XmpCore.IXmpMeta.DeleteStructField(System.String,System.String,System.String,System.String)">
            <summary>Deletes the given XMP subtree rooted at the given struct field.</summary>
            <remarks>It is not an error if the field does not exist.</remarks>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="structName">
            The name of the struct. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="fieldNs">The namespace URI for the field. Has the same URI and prefix usage as the schemaNS parameter.</param>
            <param name="fieldName">
            The name of the field. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            structName parameter.
            </param>
        </member>
        <member name="M:XmpCore.IXmpMeta.DeleteQualifier(System.String,System.String,System.String,System.String)">
            <summary>Deletes the given XMP subtree rooted at the given qualifier.</summary>
            <remarks>
            Deletes the given XMP subtree rooted at the given qualifier. It is not an error if the
            qualifier does not exist.
            </remarks>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property to which the qualifier is attached. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="qualNs">The namespace URI for the qualifier. Has the same URI and prefix usage as the schemaNS parameter.</param>
            <param name="qualName">
            The name of the qualifier. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            propName parameter.
            </param>
        </member>
        <member name="M:XmpCore.IXmpMeta.DoesPropertyExist(System.String,System.String)">
            <summary>Returns whether the property exists.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns true if the property exists.</returns>
        </member>
        <member name="M:XmpCore.IXmpMeta.DoesArrayItemExist(System.String,System.String,System.Int32)">
            <summary>Tells if the array item exists.</summary>
            <param name="schemaNs">The namespace URI for the array. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="itemIndex">
            The index of the desired item. Arrays in XMP are indexed from 1. The
            constant <see cref="F:XmpCore.XmpConstants.ArrayLastItem"/> always refers to the last
            existing array item.
            </param>
            <returns>Returns <c>true</c> if the array exists, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:XmpCore.IXmpMeta.DoesStructFieldExist(System.String,System.String,System.String,System.String)">
            <summary>DoesStructFieldExist tells if the struct field exists.</summary>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="structName">
            The name of the struct. May be a general path expression, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="fieldNs">The namespace URI for the field. Has the same URI and prefix usage as the schemaNS parameter.</param>
            <param name="fieldName">
            The name of the field. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            structName parameter.
            </param>
            <returns>Returns true if the field exists.</returns>
        </member>
        <member name="M:XmpCore.IXmpMeta.DoesQualifierExist(System.String,System.String,System.String,System.String)">
            <summary>DoesQualifierExist tells if the qualifier exists.</summary>
            <param name="schemaNs">The namespace URI for the struct. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property to which the qualifier is attached. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="qualNs">The namespace URI for the qualifier. Has the same URI and prefix usage as the schemaNS parameter.</param>
            <param name="qualName">
            The name of the qualifier. Must be a single XML name, must not be
            <c>null</c> or the empty string. Has the same namespace prefix usage as the
            propName parameter.
            </param>
            <returns>Returns true if the qualifier exists.</returns>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetLocalizedText(System.String,System.String,System.String,System.String)">
            <summary>
            These functions provide convenient support for localized text properties, including a number
            of special and obscure aspects.
            </summary>
            <remarks>
            Localized text properties are stored in alt-text arrays. They
            allow multiple concurrent localizations of a property value, for example a document title or
            copyright in several languages. The most important aspect of these functions is that they
            select an appropriate array item based on one or two RFC 3066 language tags. One of these
            languages, the "specific" language, is preferred and selected if there is an exact match. For
            many languages it is also possible to define a "generic" language that may be used if there
            is no specific language match. The generic language must be a valid RFC 3066 primary subtag,
            or the empty string. For example, a specific language of "en-US" should be used in the US,
            and a specific language of "en-UK" should be used in England. It is also appropriate to use
            "en" as the generic language in each case. If a US document goes to England, the "en-US"
            title is selected by using the "en" generic language and the "en-UK" specific language. It is
            considered poor practice, but allowed, to pass a specific language that is just an RFC 3066
            primary tag. For example "en" is not a good specific language, it should only be used as a
            generic language. Passing "i" or "x" as the generic language is also considered poor practice
            but allowed. Advice from the W3C about the use of RFC 3066 language tags can be found at:
            http://www.w3.org/International/articles/language-tags/
            <para />
            <em>Note:</em> RFC 3066 language tags must be treated in a case insensitive manner. The XMP
            Toolkit does this by normalizing their capitalization:
            <list type="bullet">
            <item> The primary subtag is lower case, the suggested practice of ISO 639.</item>
            <item> All 2 letter secondary subtags are upper case, the suggested practice of ISO 3166.</item>
            <item> All other subtags are lower case. The XMP specification defines an artificial language,</item>
            <item>"x-default", that is used to explicitly denote a default item in an alt-text array.</item>
            </list>
            The XMP toolkit normalizes alt-text arrays such that the x-default item is the first item.
            The SetLocalizedText function has several special features related to the x-default item, see
            its description for details. The selection of the array item is the same for GetLocalizedText
            and SetLocalizedText:
            <list type="bullet">
            <item> Look for an exact match with the specific language.</item>
            <item> If a generic language is given, look for a partial match.</item>
            <item> Look for an x-default item.</item>
            <item> Choose the first item.</item>
            </list>
            A partial match with the generic language is where the start of the item's language matches
            the generic string and the next character is '-'. An exact match is also recognized as a
            degenerate case. It is fine to pass x-default as the specific language. In this case,
            selection of an x-default item is an exact match by the first rule, not a selection by the
            3rd rule. The last 2 rules are fallbacks used when the specific and generic languages fail to
            produce a match. <c>getLocalizedText</c> returns information about a selected item in
            an alt-text array. The array item is selected according to the rules given above.
            </remarks>
            <param name="schemaNs">
            The namespace URI for the alt-text array. Has the same usage as in
            <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="altTextName">
            The name of the alt-text array. May be a general path expression, must not
            be <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="genericLang">
            The name of the generic language as an RFC 3066 primary subtag. May be
            <c>null</c> or the empty string if no generic language is wanted.
            </param>
            <param name="specificLang">
            The name of the specific language as an RFC 3066 tag. Must not be
            <c>null</c> or the empty string.
            </param>
            <returns>
            Returns an <see cref="T:XmpCore.IXmpProperty"/> containing the value, the actual language and
            the options if an appropriate alternate collection item exists, <c>null</c>
            if the property.
            does not exist.
            </returns>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetLocalizedText(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Modifies the value of a selected item in an alt-text array.</summary>
            <remarks>
            Creates an appropriate array item
            if necessary, and handles special cases for the x-default item. If the selected item is from
            a match with the specific language, the value of that item is modified. If the existing value
            of that item matches the existing value of the x-default item, the x-default item is also
            modified. If the array only has 1 existing item (which is not x-default), an x-default item
            is added with the given value. If the selected item is from a match with the generic language
            and there are no other generic matches, the value of that item is modified. If the existing
            value of that item matches the existing value of the x-default item, the x-default item is
            also modified. If the array only has 1 existing item (which is not x-default), an x-default
            item is added with the given value. If the selected item is from a partial match with the
            generic language and there are other partial matches, a new item is created for the specific
            language. The x-default item is not modified. If the selected item is from the last 2 rules
            then a new item is created for the specific language. If the array only had an x-default
            item, the x-default item is also modified. If the array was empty, items are created for the
            specific language and x-default.
            </remarks>
            <param name="schemaNs">
            The namespace URI for the alt-text array. Has the same usage as in
            <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="altTextName">
            The name of the alt-text array. May be a general path expression, must not
            be <c>null</c> or the empty string. Has the same namespace prefix usage as
            propName in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.
            </param>
            <param name="genericLang">
            The name of the generic language as an RFC 3066 primary subtag. May be
            <c>null</c> or the empty string if no generic language is wanted.
            </param>
            <param name="specificLang">
            The name of the specific language as an RFC 3066 tag. Must not be
            <c>null</c> or the empty string.
            </param>
            <param name="itemValue">
            A pointer to the <c>null</c> terminated UTF-8 string that is the new
            value for the appropriate array item.
            </param>
            <param name="options">Option flags, none are defined at present.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetLocalizedText(System.String,System.String,System.String,System.String,System.String)">
            <seealso cref="M:XmpCore.IXmpMeta.SetLocalizedText(System.String,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the alt-text array</param>
            <param name="altTextName">The name of the alt-text array</param>
            <param name="genericLang">The name of the generic language</param>
            <param name="specificLang">The name of the specific language</param>
            <param name="itemValue">the new value for the appropriate array item</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyBoolean(System.String,System.String)">
            <summary>
            These are very similar to <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/> and <see cref="M:XmpCore.IXmpMeta.SetProperty(System.String,System.String,System.Object)"/> above,
            but the value is returned or provided in a literal form instead of as a UTF-8 string.
            </summary>
            <remarks>
            The path composition functions in <see cref="T:XmpCore.XmpPathFactory"/> may be used to compose an path
            expression for fields in nested structures, items in arrays, or qualifiers.
            </remarks>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>bool</c> value or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyInteger(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns an <c>int</c> value or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyLong(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>long</c> value or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyDouble(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>double</c> value or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyDate(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>IXmpDateTime</c> object or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyCalendar(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>Calendar</c>-object or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyBase64(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>byte[]</c>-array contained the decoded base64 value or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPropertyString(System.String,System.String)">
            <summary>Convenience method to retrieve the literal value of a property.</summary>
            <remarks>Note that there is no <c>setPropertyString()</c>z, because <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/> sets a string value.</remarks>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <returns>Returns a <c>string</c> value or <c>null</c> if the property does not exist.</returns>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur, especially conversion errors.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyBoolean(System.String,System.String,System.Boolean,XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property to a literal <c>boolean</c> value.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the literal property value as <c>boolean</c>.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyBoolean(System.String,System.String,System.Boolean)">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyBoolean(System.String,System.String,System.Boolean,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the literal property value as <c>boolean</c></param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyInteger(System.String,System.String,System.Int32,XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property to a literal <c>int</c> value.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the literal property value as <c>int</c>.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyInteger(System.String,System.String,System.Int32)">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyInteger(System.String,System.String,System.Int32,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the literal property value as <c>int</c></param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyLong(System.String,System.String,System.Int64,XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property to a literal <c>long</c> value.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the literal property value as <c>long</c>.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyLong(System.String,System.String,System.Int64)">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyLong(System.String,System.String,System.Int64,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the literal property value as <c>long</c></param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyDouble(System.String,System.String,System.Double,XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property to a literal <c>double</c> value.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the literal property value as <c>double</c>.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyDouble(System.String,System.String,System.Double)">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyDouble(System.String,System.String,System.Double,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the literal property value as <c>double</c></param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyDate(System.String,System.String,XmpCore.IXmpDateTime,XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property with an XMPDateTime-object, which is serialized to an ISO8601 date.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in<see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the property value as <c>XMPDateTime</c>.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyDate(System.String,System.String,XmpCore.IXmpDateTime)">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyDate(System.String,System.String,XmpCore.IXmpDateTime,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the property value as <c>XMPDateTime</c></param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyCalendar(System.String,System.String,Sharpen.Calendar,XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property with a Calendar-object, which is serialized to an ISO8601 date.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the property value as <c>Calendar</c>.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyCalendar(System.String,System.String,Sharpen.Calendar)">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyCalendar(System.String,System.String,Sharpen.Calendar,XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the property value as <c>Calendar</c></param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyBase64(System.String,System.String,System.Byte[],XmpCore.Options.PropertyOptions)">
            <summary>Convenience method to set a property from a binary <c>byte[]</c>-array, which is serialized as base64-string.</summary>
            <param name="schemaNs">The namespace URI for the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propName">The name of the property. Has the same usage as in <see cref="M:XmpCore.IXmpMeta.GetProperty(System.String,System.String)"/>.</param>
            <param name="propValue">the literal property value as byte array.</param>
            <param name="options">options of the property to set (optional).</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetPropertyBase64(System.String,System.String,System.Byte[])">
            <seealso cref="M:XmpCore.IXmpMeta.SetPropertyBase64(System.String,System.String,System.Byte[],XmpCore.Options.PropertyOptions)"/>
            <param name="schemaNs">The namespace URI for the property</param>
            <param name="propName">The name of the property</param>
            <param name="propValue">the literal property value as byte array</param>
            <exception cref="T:XmpCore.XmpException">Wraps all exceptions</exception>
        </member>
        <member name="P:XmpCore.IXmpMeta.Properties">
            <summary>Constructs an enumerable for the properties within this XMP object.</summary>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetObjectName">
            <summary>This correlates to the about-attribute, returns the empty String if no name is set.</summary>
            <returns>Returns the name of the XMP object.</returns>
        </member>
        <member name="M:XmpCore.IXmpMeta.SetObjectName(System.String)">
            <param name="name">Sets the name of the XMP object.</param>
        </member>
        <member name="M:XmpCore.IXmpMeta.GetPacketHeader">
            <returns>
            Returns the unparsed content of the &lt;?xpacket&gt; processing instruction.
            This contains normally the attribute-like elements 'begin="&lt;BOM&gt;"
            id="W5M0MpCehiHzreSzNTczkc9d"' and possibly the deprecated elements 'bytes="1234"' or
            'encoding="XXX"'. If the parsed packet has not been wrapped into an xpacket,
            <c>null</c> is returned.
            </returns>
        </member>
        <member name="M:XmpCore.IXmpMeta.Sort">
            <remarks>
            Sorts the complete datamodel according to the following rules:
            <list type="bullet">
            <item>Schema nodes are sorted by prefix.</item>
            <item>Properties at top level and within structs are sorted by full name, that is prefix + local name.</item>
            <item>Array items are not sorted, even if they have no certain order such as bags.</item>
            <item>Qualifier are sorted, with the exception of "xml:lang" and/or "rdf:type" that stay at the top of the list in that order.</item>
            </list>
            </remarks>
        </member>
        <member name="M:XmpCore.IXmpMeta.Normalize(XmpCore.Options.ParseOptions)">
            <summary>Perform the normalization as a separate parsing step.</summary>
            <remarks>
            Normally it is done during parsing, unless <see cref="P:XmpCore.Options.ParseOptions.OmitNormalization"/> is set to <c>true</c>.
            <para />
            Note: It does no harm to call this method to an already normalized xmp object.
            It was a PDF/A requirement to get hand on the unnormalized <c>XMPMeta</c> object.
            </remarks>
            <param name="options">optional parsing options.</param>
            <exception cref="T:XmpCore.XmpException">Wraps all errors and exceptions that may occur.</exception>
        </member>
        <member name="M:XmpCore.IXmpMeta.DumpObject">
            <summary>Renders this node and the tree under this node in a human readable form.</summary>
            <returns>Returns a multiline string containing the dump.</returns>
        </member>
        <member name="T:XmpCore.IXmpProperty">
            <summary>Models a a text property together with its language and options.</summary>
            <author>Stefan Makswit</author>
            <since>23.01.2006</since>
        </member>
        <member name="P:XmpCore.IXmpProperty.Value">
            <value>Returns the value of the property.</value>
        </member>
        <member name="P:XmpCore.IXmpProperty.Options">
            <value>Returns the options of the property.</value>
        </member>
        <member name="P:XmpCore.IXmpProperty.Language">
            <summary>
            Only set by <see cref="M:XmpCore.IXmpMeta.GetLocalizedText(System.String,System.String,System.String,System.String)"/>.
            </summary>
            <value>Returns the language of the alt-text item.</value>
        </member>
        <member name="T:XmpCore.IXmpPropertyInfo">
            <summary>Models a property together with its path and namespace.</summary>
            <remarks>Instances of this type are are iterated via <see cref="T:XmpCore.IXmpIterator"/>.</remarks>
            <author>Stefan Makswit</author>
            <since>06.07.2006</since>
        </member>
        <member name="P:XmpCore.IXmpPropertyInfo.Namespace">
            <value>Returns the namespace of the property</value>
        </member>
        <member name="P:XmpCore.IXmpPropertyInfo.Path">
            <value>Returns the path of the property, but only if returned by the iterator.</value>
        </member>
        <member name="T:XmpCore.IXmpSchemaRegistry">
            <summary>
            The schema registry keeps track of all namespaces and aliases used in the XMP
            metadata.
            </summary>
            <remarks>
            At initialisation time, the default namespaces and default aliases
            are automatically registered. <b>Namespaces</b> must be registered before
            used in namespace URI parameters or path expressions. Within the XMP Toolkit
            the registered namespace URIs and prefixes must be unique. Additional
            namespaces encountered when parsing RDF are automatically registered. The
            namespace URI should always end in an XML name separator such as '/' or '#'.
            This is because some forms of RDF shorthand catenate a namespace URI with an
            element name to form a new URI.
            <para />
            <b>Aliases</b> in XMP serve the same purpose as Windows file shortcuts,
            Macintosh file aliases, or UNIX file symbolic links. The aliases are simply
            multiple names for the same property. One distinction of XMP aliases is that
            they are ordered, there is an alias name pointing to an actual name. The
            primary significance of the actual name is that it is the preferred name for
            output, generally the most widely recognized name.
            <para />
            The names that can be aliased in XMP are restricted. The alias must be a top
            level property name, not a field within a structure or an element within an
            array. The actual may be a top level property name, the first element within
            a top level array, or the default element in an alt-text array. This does not
            mean the alias can only be a simple property. It is OK to alias a top level
            structure or array to an identical top level structure or array, or to the
            first item of an array of structures.
            </remarks>
            <author>Stefan Makswit</author>
            <since>27.01.2006</since>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.RegisterNamespace(System.String,System.String)">
            <summary>Register a namespace URI with a suggested prefix.</summary>
            <remarks>
            It is not an error if the URI is already registered, no matter what the prefix is.
            If the URI is not registered but the suggested prefix is in use, a unique prefix is
            created from the suggested one. The actual registered prefix is always
            returned. The function result tells if the registered prefix is the
            suggested one.
            <para />
            Note: No checking is presently done on either the URI or the prefix.
            </remarks>
            <param name="namespaceUri">The URI for the namespace. Must be a valid XML URI.</param>
            <param name="suggestedPrefix">
            The suggested prefix to be used if the URI is not yet
            registered. Must be a valid XML name.
            </param>
            <returns>
            Returns the registered prefix for this URI, is equal to the
            suggestedPrefix if the namespace hasn't been registered before,
            otherwise the existing prefix.
            </returns>
            <exception cref="T:XmpCore.XmpException">If the parameters are not accordingly set</exception>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.GetNamespacePrefix(System.String)">
            <summary>Obtain the prefix for a registered namespace URI.</summary>
            <remarks>
            It is not an error if the namespace URI is not registered.
            </remarks>
            <param name="namespaceUri">
            The URI for the namespace. Must not be null or the empty
            string.
            </param>
            <returns>Returns the prefix registered for this namespace URI or null.</returns>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.GetNamespaceUri(System.String)">
            <summary>Obtain the URI for a registered namespace prefix.</summary>
            <remarks>
            It is not an error if the namespace prefix is not registered.
            </remarks>
            <param name="namespacePrefix">
            The prefix for the namespace. Must not be null or the empty
            string.
            </param>
            <returns>Returns the URI registered for this prefix or null.</returns>
        </member>
        <member name="P:XmpCore.IXmpSchemaRegistry.Namespaces">
            <summary>
            Returns the registered prefix/namespace-pairs as map, where the keys are the
            namespaces and the values are the prefixes.
            </summary>
        </member>
        <member name="P:XmpCore.IXmpSchemaRegistry.Prefixes">
            <summary>
            Returns the registered namespace/prefix-pairs as map, where the keys are the
            prefixes and the values are the namespaces.
            </summary>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.DeleteNamespace(System.String)">
            <summary>Deletes a namespace from the registry.</summary>
            <remarks>
            Does nothing if the URI is not registered, or if the namespaceURI
            parameter is null or the empty string.
            <para />
            Note: Not yet implemented.
            </remarks>
            <param name="namespaceUri">The URI for the namespace.</param>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.ResolveAlias(System.String,System.String)">
            <summary>Determines if a name is an alias, and what it is aliased to.</summary>
            <param name="aliasNs">
            The namespace URI of the alias. Must not be <c>null</c> or the empty string.
            </param>
            <param name="aliasProp">
            The name of the alias. May be an arbitrary path expression
            path, must not be <c>null</c> or the empty string.
            </param>
            <returns>
            Returns the <c>XMPAliasInfo</c> for the given alias namespace and property or
            <c>null</c> if there is no such alias.
            </returns>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.FindAliases(System.String)">
            <summary>Collects all aliases that are contained in the provided namespace.</summary>
            <remarks>
            Collects all aliases that are contained in the provided namespace.
            If nothing is found, an empty array is returned.
            </remarks>
            <param name="aliasNs">a schema namespace URI</param>
            <returns>Returns all alias infos from aliases that are contained in the provided namespace.</returns>
        </member>
        <member name="M:XmpCore.IXmpSchemaRegistry.FindAlias(System.String)">
            <summary>Searches for registered aliases.</summary>
            <param name="qname">an XML conform qname</param>
            <returns>
            Returns if an alias definition for the given qname to another
            schema and property is registered.
            </returns>
        </member>
        <member name="P:XmpCore.IXmpSchemaRegistry.Aliases">
            <summary>
            Returns the registered aliases as map, where the key is the "qname" (prefix and name)
            and the value an <c>XMPAliasInfo</c>-object.
            </summary>
        </member>
        <member name="T:XmpCore.IXmpVersionInfo">
            <summary>XMP Toolkit Version Information.</summary>
            <remarks>
            Version information for the XMP toolkit is available at runtime via <see cref="P:XmpCore.XmpMetaFactory.VersionInfo"/>.
            </remarks>
            <author>Stefan Makswit</author>
            <since>23.01.2006</since>
        </member>
        <member name="P:XmpCore.IXmpVersionInfo.Major">
            <value>Returns the primary release number, the "1" in version "1.2.3".</value>
        </member>
        <member name="P:XmpCore.IXmpVersionInfo.Minor">
            <value>Returns the secondary release number, the "2" in version "1.2.3".</value>
        </member>
        <member name="P:XmpCore.IXmpVersionInfo.Micro">
            <value>Returns the tertiary release number, the "3" in version "1.2.3".</value>
        </member>
        <member name="P:XmpCore.IXmpVersionInfo.Build">
            <value>Returns a rolling build number, monotonically increasing in a release.</value>
        </member>
        <member name="P:XmpCore.IXmpVersionInfo.IsDebug">
            <value>Returns true if this is a debug build.</value>
        </member>
        <member name="P:XmpCore.IXmpVersionInfo.Message">
            <value>Returns a comprehensive version information string.</value>
        </member>
        <member name="T:XmpCore.Options.AliasOptions">
            <summary>Options for XMPSchemaRegistryImpl#registerAlias.</summary>
            <author>Stefan Makswit</author>
            <since>20.02.2006</since>
        </member>
        <member name="F:XmpCore.Options.AliasOptions.PropDirect">
            <summary>This is a direct mapping.</summary>
            <remarks>This is a direct mapping. The actual data type does not matter.</remarks>
        </member>
        <member name="F:XmpCore.Options.AliasOptions.PropArray">
            <summary>The actual is an unordered array, the alias is to the first element of the array.</summary>
        </member>
        <member name="F:XmpCore.Options.AliasOptions.PropArrayOrdered">
            <summary>The actual is an ordered array, the alias is to the first element of the array.</summary>
        </member>
        <member name="F:XmpCore.Options.AliasOptions.PropArrayAlternate">
            <summary>The actual is an alternate array, the alias is to the first element of the array.</summary>
        </member>
        <member name="F:XmpCore.Options.AliasOptions.PropArrayAltText">
            <summary>The actual is an alternate text array, the alias is to the 'x-default' element of the array.</summary>
        </member>
        <member name="M:XmpCore.Options.AliasOptions.#ctor(System.Int32)">
            <param name="options">the options to init with</param>
            <exception cref="T:XmpCore.XmpException">If options are not consistant</exception>
        </member>
        <member name="M:XmpCore.Options.AliasOptions.IsSimple">
            <returns>Returns if the alias is of the simple form.</returns>
        </member>
        <member name="M:XmpCore.Options.AliasOptions.ToPropertyOptions">
            <returns>
            Returns a <see cref="T:XmpCore.Options.PropertyOptions"/> object
            </returns>
            <exception cref="T:XmpCore.XmpException">If the options are not consistant.</exception>
        </member>
        <member name="T:XmpCore.Options.IteratorOptions">
            <summary>Options for <c>XMPIterator</c> construction.</summary>
            <author>Stefan Makswit</author>
            <since>24.01.2006</since>
        </member>
        <member name="F:XmpCore.Options.IteratorOptions.JustChildren">
            <summary>Just do the immediate children of the root, default is subtree.</summary>
        </member>
        <member name="F:XmpCore.Options.IteratorOptions.JustLeafNodes">
            <summary>Just do the leaf nodes, default is all nodes in the subtree.</summary>
            <remarks>
            Just do the leaf nodes, default is all nodes in the subtree.
            Bugfix #2658965: If this option is set the Iterator returns the namespace
            of the leaf instead of the namespace of the base property.
            </remarks>
        </member>
        <member name="F:XmpCore.Options.IteratorOptions.JustLeafName">
            <summary>Return just the leaf part of the path, default is the full path.</summary>
        </member>
        <member name="F:XmpCore.Options.IteratorOptions.OmitQualifiers">
            <summary>Omit all qualifiers.</summary>
        </member>
        <member name="T:XmpCore.Options.Options">
            <summary>The base class for a collection of 32 flag bits.</summary>
            <remarks>
            The base class for a collection of 32 flag bits. Individual flags are defined as enum value bit
            masks. Inheriting classes add convenience accessor methods.
            </remarks>
            <author>Stefan Makswit</author>
            <since>24.01.2006</since>
        </member>
        <member name="F:XmpCore.Options.Options._options">
            <summary>the internal int containing all options</summary>
        </member>
        <member name="F:XmpCore.Options.Options._optionNames">
            <summary>a map containing the bit names</summary>
        </member>
        <member name="M:XmpCore.Options.Options.#ctor">
            <summary>The default constructor.</summary>
        </member>
        <member name="M:XmpCore.Options.Options.#ctor(System.Int32)">
            <summary>Constructor with the options bit mask.</summary>
            <param name="options">the options bit mask</param>
            <exception cref="T:XmpCore.XmpException">If the options are not correct</exception>
        </member>
        <member name="M:XmpCore.Options.Options.Clear">
            <summary>Resets the options.</summary>
        </member>
        <member name="M:XmpCore.Options.Options.IsExactly(System.Int32)">
            <param name="optionBits">an option bitmask</param>
            <returns>Returns true, if this object is equal to the given options.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.ContainsAllOptions(System.Int32)">
            <param name="optionBits">an option bitmask</param>
            <returns>Returns true, if this object contains all given options.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.ContainsOneOf(System.Int32)">
            <param name="optionBits">an option bitmask</param>
            <returns>Returns true, if this object contain at least one of the given options.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.GetOption(System.Int32)">
            <param name="optionBit">the binary bit or bits that are requested</param>
            <returns>Returns if <emp>all</emp> of the requested bits are set or not.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.SetOption(System.Int32,System.Boolean)">
            <param name="optionBits">the binary bit or bits that shall be set to the given value</param>
            <param name="value">the boolean value to set</param>
        </member>
        <member name="M:XmpCore.Options.Options.GetOptions">
            <summary>Is friendly to access it during the tests.</summary>
            <returns>Returns the options.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.SetOptions(System.Int32)">
            <param name="options">The options to set.</param>
            <exception cref="T:XmpCore.XmpException">If the options are not correct</exception>
        </member>
        <member name="M:XmpCore.Options.Options.GetOptionsString">
            <summary>Creates a human readable string from the set options.</summary>
            <remarks>
            <em>Note:</em> This method is quite expensive and should only be used within tests or as
            </remarks>
            <returns>
            Returns a string listing all options that are set to <c>true</c> by their name,
            like "option1 | option4".
            </returns>
        </member>
        <member name="M:XmpCore.Options.Options.ToString">
            <returns>Returns the options as hex bitmask.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.GetValidOptions">
            <summary>To be implemented by inheritants.</summary>
            <returns>Returns a bit mask where all valid option bits are set.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.DefineOptionName(System.Int32)">
            <summary>To be implemented by inheritants.</summary>
            <param name="option">a single, valid option bit.</param>
            <returns>Returns a human readable name for an option bit.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.AssertConsistency(System.Int32)">
            <summary>The inheriting option class can do additional checks on the options.</summary>
            <remarks>
            The inheriting option class can do additional checks on the options.
            <em>Note:</em> For performance reasons this method is only called
            when setting bitmasks directly.
            When get- and set-methods are used, this method must be called manually,
            normally only when the Options-object has been created from a client
            (it has to be made public therefore).
            </remarks>
            <param name="options">the bitmask to check.</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the options are not consistent.</exception>
        </member>
        <member name="M:XmpCore.Options.Options.AssertOptionsValid(System.Int32)">
            <summary>Checks options before they are set.</summary>
            <remarks>
            First it is checked if only defined options are used, second the additional
            <see cref="M:XmpCore.Options.Options.AssertConsistency(System.Int32)"/>-method is called.
            </remarks>
            <param name="options">the options to check</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the options are invalid.</exception>
        </member>
        <member name="M:XmpCore.Options.Options.GetOptionName(System.Int32)">
            <summary>Looks up or asks the inherited class for the name of an option bit.</summary>
            <remarks>
            Looks up or asks the inherited class for the name of an option bit.
            Its save that there is only one valid option handed into the method.
            </remarks>
            <param name="option">a single option bit</param>
            <returns>Returns the option name or undefined.</returns>
        </member>
        <member name="M:XmpCore.Options.Options.ProcureOptionNames">
            <returns>Returns the optionNames map and creates it if required.</returns>
        </member>
        <member name="T:XmpCore.Options.ParseOptions">
            <summary>
            Options for <see cref="M:XmpCore.XmpMetaFactory.Parse(System.IO.Stream,XmpCore.Options.ParseOptions)"/>.
            </summary>
            <author>Stefan Makswit</author>
            <since>24.01.2006</since>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.RequireXmpMetaFlag">
            <summary>Require a surrounding &quot;x:xmpmeta&quot; element in the xml-document.</summary>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.StrictAliasingFlag">
            <summary>Do not reconcile alias differences, throw an exception instead.</summary>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.FixControlCharsFlag">
            <summary>Convert ASCII control characters 0x01 - 0x1F (except tab, cr, and lf) to spaces.</summary>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.AcceptLatin1Flag">
            <summary>If the input is not unicode, try to parse it as ISO-8859-1.</summary>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.OmitNormalizationFlag">
            <summary>Do not carry run the XMPNormalizer on a packet, leave it as it is.</summary>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.DisallowDoctypeFlag">
            <summary>Disallow DOCTYPE declarations to prevent entity expansion attacks.</summary>
        </member>
        <member name="F:XmpCore.Options.ParseOptions.mXMPNodesToLimit">
            <summary>Map of nodes whose children are to be limited.</summary>
        </member>
        <member name="M:XmpCore.Options.ParseOptions.#ctor">
            <summary>Sets the options to the default values.</summary>
        </member>
        <member name="P:XmpCore.Options.ParseOptions.AreXMPNodesLimited">
            <summary>Returns true if some XMP nodes have been limited.</summary>
        </member>
        <member name="M:XmpCore.Options.ParseOptions.SetXMPNodesToLimit(System.Collections.Generic.Dictionary{System.String,System.Int32})">
            <param name="nodeMap">the Map with name of nodes and number-of-items to limit them to</param>
            <summary>Returns the instance to call more set-methods.</summary>
        </member>
        <member name="M:XmpCore.Options.ParseOptions.GetXMPNodesToLimit">
            <summary>Returns map containing names oF XMP nodes to limit and number-of-items limit corresponding to the XMP nodes.</summary>
        </member>
        <member name="T:XmpCore.Options.PropertyOptions">
            <summary>
            The property flags are used when properties are fetched from the <c>XMPMeta</c>-object
            and provide more detailed information about the property.
            </summary>
            <author>Stefan Makswit</author>
            <since>03.07.2006</since>
        </member>
        <member name="F:XmpCore.Options.PropertyOptions.DeleteExisting">
            <summary>may be used in the future</summary>
        </member>
        <member name="M:XmpCore.Options.PropertyOptions.#ctor">
            <summary>Default constructor</summary>
        </member>
        <member name="M:XmpCore.Options.PropertyOptions.#ctor(System.Int32)">
            <summary>Initialization constructor</summary>
            <param name="options">the initialization options</param>
            <exception cref="T:XmpCore.XmpException">If the options are not valid</exception>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsUri">
            <summary>
            Get and set whether the property value is a URI. It is serialized to RDF using the
            <c>rdf:resource</c> attribute. Not mandatory for URIs, but considered RDF-savvy.
            </summary>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.HasQualifiers">
            <value>
              Return whether the property has qualifiers. These could be an <tt>xml:lang</tt>
              attribute, an <tt>rdf:type</tt> property, or a general qualifier. See the
              introductory discussion of qualified properties for more information.
            </value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsQualifier">
            <value>
              Return whether this property is a qualifier for some other property. Note that if the
              qualifier itself has a structured value, this flag is only set for the top node of
              the qualifier's subtree. Qualifiers may have arbitrary structure, and may even have
              qualifiers.
            </value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.HasLanguage">
            <value>Return whether this property has an <tt>xml:lang</tt> qualifier.</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.HasType">
            <value>Return whether this property has an <tt>rdf:type</tt> qualifier.</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsStruct">
            <value>Return whether this property contains nested fields.</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsArray">
            <value>
              Return whether this property is an array. By itself this indicates a general
              unordered array. It is serialized using an <tt>rdf:Bag</tt> container.
            </value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsArrayOrdered">
            <value>
              Return whether this property is an ordered array. Appears in conjunction with
              getPropValueIsArray(). It is serialized using an <tt>rdf:Seq</tt> container.
            </value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsArrayAlternate">
            <value>
              Return whether this property is an alternative array. Appears in conjunction with
              getPropValueIsArray(). It is serialized using an <tt>rdf:Alt</tt> container.
            </value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsArrayAltText">
            <value>
              Return whether this property is an alt-text array. Appears in conjunction with
              getPropArrayIsAlternate(). It is serialized using an <tt>rdf:Alt</tt> container.
              Each array element is a simple property with an <tt>xml:lang</tt> attribute.
            </value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsArrayLimited">
            <value>Return whether this property is an array with a limit on number-of-elements.</value>
        </member>
        <member name="M:XmpCore.Options.PropertyOptions.SetArrayElementLimit(System.Int32)">
            <param name="arrayLimit">the limit to set on number-of-elements</param>
            <value>Returns this to enable cascaded options.</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.ArrayElementsLimit">
            <value>Returns the current limit on number-of-elements</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsSchemaNode">
            <value>Returns whether the SCHEMA_NODE option is set.</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsCompositeProperty">
            <value>Returns whether the property is of composite type - an array or a struct.</value>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsSimple">
            <value>Returns whether the property is of composite type - an array or a struct.</value>
        </member>
        <member name="M:XmpCore.Options.PropertyOptions.EqualArrayTypes(XmpCore.Options.PropertyOptions)">
            <summary>Compares two options set for array compatibility.</summary>
            <param name="options">other options</param>
            <returns>Returns true if the array options of the sets are equal.</returns>
        </member>
        <member name="M:XmpCore.Options.PropertyOptions.MergeWith(XmpCore.Options.PropertyOptions)">
            <summary>Merges the set options of a another options object with this.</summary>
            <remarks>
            Merges the set options of a another options object with this.
            If the other options set is null, this objects stays the same.
            </remarks>
            <param name="options">other options</param>
            <exception cref="T:XmpCore.XmpException">If illegal options are provided</exception>
        </member>
        <member name="P:XmpCore.Options.PropertyOptions.IsOnlyArrayOptions">
            <value>Returns true if only array options are set.</value>
        </member>
        <member name="M:XmpCore.Options.PropertyOptions.AssertConsistency(System.Int32)">
            <summary>
            Checks that a node not a struct and array at the same time;
            and URI cannot be a struct.
            </summary>
            <param name="options">the bitmask to check.</param>
            <exception cref="T:XmpCore.XmpException">Thrown if the options are not consistent.</exception>
        </member>
        <member name="T:XmpCore.Options.SerializeOptions">
            <summary>
            Options for <see cref="M:XmpCore.XmpMetaFactory.SerializeToBuffer(XmpCore.IXmpMeta,XmpCore.Options.SerializeOptions)"/>.
            </summary>
            /// <author>Stefan Makswit</author>
            <since>24.01.2006</since>
        </member>
        <member name="F:XmpCore.Options.SerializeOptions.LittleEndianBit">
            <summary>Bit indicating little endian encoding, unset is big endian</summary>
        </member>
        <member name="F:XmpCore.Options.SerializeOptions.Utf16Bit">
            <summary>Bit indication UTF16 encoding.</summary>
        </member>
        <member name="F:XmpCore.Options.SerializeOptions.EncodeUtf8">
            <summary>UTF8 encoding; this is the default</summary>
        </member>
        <member name="M:XmpCore.Options.SerializeOptions.#ctor">
            <summary>Default constructor.</summary>
        </member>
        <member name="M:XmpCore.Options.SerializeOptions.#ctor(System.Int32)">
            <summary>Constructor using initial options</summary>
            <param name="options">the initial options</param>
            <exception cref="T:XmpCore.XmpException">Thrown if options are not consistent.</exception>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.OmitPacketWrapper">
            <summary>Omit the XML packet wrapper.</summary>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.OmitXmpMetaElement">
            <summary>Omit the &lt;x:xmpmeta&gt; tag.</summary>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.ReadOnlyPacket">
            <summary>Mark packet as read-only.</summary>
            <remarks>Default is a writeable packet.</remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.UseCompactFormat">
            <summary>Use a compact form of RDF.</summary>
            <remarks>
            Use a compact form of RDF.
            The compact form is the default serialization format (this flag is technically ignored).
            To serialize to the canonical form, set the flag USE_CANONICAL_FORMAT.
            If both flags &quot;compact&quot; and &quot;canonical&quot; are set, canonical is used.
            </remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.UseCanonicalFormat">
            <summary>Use the canonical form of RDF if set.</summary>
            <remarks>By default the compact form is used.</remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.UsePlainXmp">
            <summary>Serialize as &quot;Plain XMP&quot;, not RDF.</summary>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.IncludeThumbnailPad">
            <summary>Include a padding allowance for a thumbnail image.</summary>
            <remarks>
            Include a padding allowance for a thumbnail image. If no <tt>xmp:Thumbnails</tt> property
            is present, the typical space for a JPEG thumbnail is used.
            </remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.ExactPacketLength">
            <summary>The padding parameter provides the overall packet length.</summary>
            <remarks>
            The padding parameter provides the overall packet length. The actual amount of padding is
            computed. An exception is thrown if the packet exceeds this length with no padding.
            </remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.Sort">
            <summary>Sort the struct properties and qualifier before serializing</summary>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.EncodeUtf16Be">
            <summary>UTF16BE encoding</summary>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.EncodeUtf16Le">
            <summary>UTF16LE encoding</summary>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.BaseIndent">
            <summary>
            The number of levels of indentation to be used for the outermost XML element in the
            serialized RDF.
            </summary>
            <remarks>
            The number of levels of indentation to be used for the outermost XML element in the
            serialized RDF. This is convenient when embedding the RDF in other text, defaults to 0.
            </remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.Indent">
            <summary>
            The string to be used for each level of indentation in the serialized
            RDF.
            </summary>
            <remarks>
            The string to be used for each level of indentation in the serialized
            RDF. If empty it defaults to two ASCII spaces, U+0020.
            </remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.Newline">
            <summary>The string to be used as a line terminator.</summary>
            <remarks>
            The string to be used as a line terminator. If empty it defaults to; linefeed, U+000A, the
            standard XML newline.
            </remarks>
        </member>
        <member name="P:XmpCore.Options.SerializeOptions.Padding">
            <summary>The amount of padding to be added if a writeable XML packet is created.</summary>
            <remarks>
            The amount of padding to be added if a writeable XML packet is created. If zero is passed
            (the default) an appropriate amount of padding is computed.
            </remarks>
        </member>
        <member name="M:XmpCore.Options.SerializeOptions.GetEncoding">
            <returns>Returns the text encoding to use.</returns>
        </member>
        <member name="M:XmpCore.Options.SerializeOptions.Clone">
            <returns>Returns clone of this SerializeOptions-object with the same options set.</returns>
        </member>
        <member name="T:XmpCore.Options.TemplateOptions">
            <summary>
            Options for <see cref="M:XmpCore.XmpMetaFactory.SerializeToBuffer(XmpCore.IXmpMeta,XmpCore.Options.SerializeOptions)"/>.
            </summary>
            <author>Stefan Makswit</author>
            <since>24.01.2006</since>
        </member>
        <member name="M:XmpCore.Options.TemplateOptions.#ctor">
            <summary>Default constructor.</summary>
        </member>
        <member name="M:XmpCore.Options.TemplateOptions.#ctor(System.Int32)">
            <summary>Constructor using initial options</summary>
            <param name="options">the initial options</param>
            <exception cref="T:XmpCore.XmpException">Thrown if options are not consistent.</exception>
        </member>
        <member name="P:XmpCore.Options.TemplateOptions.ClearUnnamedProperties">
            <summary></summary>
        </member>
        <member name="P:XmpCore.Options.TemplateOptions.ReplaceExistingProperties">
            <summary></summary>
        </member>
        <member name="P:XmpCore.Options.TemplateOptions.IncludeInternalProperties">
            <summary></summary>
        </member>
        <member name="P:XmpCore.Options.TemplateOptions.AddNewProperties">
            <summary></summary>
        </member>
        <member name="P:XmpCore.Options.TemplateOptions.ReplaceWithDeleteEmpty">
            <summary></summary>
        </member>
        <member name="M:XmpCore.Options.TemplateOptions.Clone">
            <returns>Returns clone of this TemplateOptions-object with the same options set.</returns>
        </member>
        <member name="T:XmpCore.XmpConstants">
            <author>Stefan Makswit</author>
        </member>
        <member name="F:XmpCore.XmpConstants.NsXml">
            <summary>The XML namespace for XML.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsRdf">
            <summary>The XML namespace for RDF.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsDC">
            <summary>The XML namespace for the Dublin Core schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsIptccore">
            <summary>The XML namespace for the IPTC Core schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsIptcext">
            <summary>The XML namespace for the IPTC Extension schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsDicom">
            <summary>The XML namespace for the DICOM medical schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsPlus">
            <summary>The XML namespace for the PLUS (Picture Licensing Universal System, http://www.useplus.org)</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsX">
            <summary>The XML namespace Adobe XMP Metadata.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsXmp">
            <summary>The XML namespace for the XMP "basic" schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsXmpRights">
            <summary>The XML namespace for the XMP copyright schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsXmpMm">
            <summary>The XML namespace for the XMP digital asset management schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsXmpBj">
            <summary>The XML namespace for the job management schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsXmpNote">
            <summary>The XML namespace for the job management schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsPdf">
            <summary>The XML namespace for the PDF schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsPdfx">
            <summary>The XML namespace for the PDF schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsPhotoshop">
            <summary>The XML namespace for the Photoshop custom schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsPsalbum">
            <summary>The XML namespace for the Photoshop Album schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsExif">
            <summary>The XML namespace for Adobe's EXIF schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsExifx">
            <summary>NS for the CIPA XMP for Exif document v1.1</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsTiff">
            <summary>The XML namespace for Adobe's TIFF schema.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsBwf">
            <summary>BExt Schema</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsRiffinfo">
            <summary>RIFF Info Schema</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsTxmp">
            <summary>Transform XMP</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsSwf">
            <summary>Adobe Flash SWF</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsCcv">
            <summary>Adobe Creative Cloud Video</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.NsDcDeprecated">
            <summary>legacy Dublin Core NS, will be converted to NS_DC</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeIdentifierqual">
            <summary>The XML namespace for qualifiers of the xmp:Identifier property.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeDimensions">
            <summary>The XML namespace for fields of the Dimensions type.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeImage">
            <summary>The XML namespace for fields of a graphical image.</summary>
            <remarks>The XML namespace for fields of a graphical image. Used for the Thumbnail type.</remarks>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeResourceevent">
            <summary>The XML namespace for fields of the ResourceEvent type.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeResourceref">
            <summary>The XML namespace for fields of the ResourceRef type.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeStVersion">
            <summary>The XML namespace for fields of the Version type.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TypeStJob">
            <summary>The XML namespace for fields of the JobRef type.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TrueString">
            <summary>The canonical true string value for Booleans in serialized XMP.</summary>
            <remarks>
            The canonical true string value for Booleans in serialized XMP. Code that converts from the
            string to a bool should be case insensitive, and even allow "1".
            </remarks>
        </member>
        <member name="F:XmpCore.XmpConstants.FalseString">
            <summary>The canonical false string value for Booleans in serialized XMP.</summary>
            <remarks>
            The canonical false string value for Booleans in serialized XMP. Code that converts from the
            string to a bool should be case insensitive, and even allow "0".
            </remarks>
        </member>
        <member name="F:XmpCore.XmpConstants.ArrayLastItem">
            <summary>Index that has the meaning to be always the last item in an array.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.ArrayItemName">
            <summary>Node name of an array item.</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.XDefault">
            <summary>The x-default string for localized properties</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.XmlLang">
            <summary>xml:lang qualifier</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.RdfLi">
            <summary>rdf:li syntaxTerm</summary>
            <remarks>
            Does not appear in the original Java version. Added because of its usage in 
            ParseRdf.cs and XmpNode.cs when string-comparing for array items.
            </remarks>
        </member>
        <member name="F:XmpCore.XmpConstants.RdfType">
            <summary>rdf:type qualifier</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.XmpPi">
            <summary>Processing Instruction (PI) for xmp packet</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TagXmpmeta">
            <summary>XMP meta tag version new</summary>
        </member>
        <member name="F:XmpCore.XmpConstants.TagXapmeta">
            <summary>XMP meta tag version old</summary>
        </member>
        <member name="T:XmpCore.XmpDateTimeFactory">
            <summary>
            A factory to create <see cref="T:XmpCore.Impl.XmpDateTime"/> instances from a <see cref="T:Sharpen.Calendar"/> or an
            ISO 8601 string or for the current time.
            </summary>
            <author>Stefan Makswit</author>
            <since>16.02.2006</since>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.CreateFromCalendar(Sharpen.Calendar)">
            <summary>Creates an <see cref="T:XmpCore.Impl.XmpDateTime"/> from a <see cref="T:Sharpen.Calendar"/>-object.</summary>
            <param name="calendar">a <see cref="T:Sharpen.Calendar"/>-object.</param>
            <returns>An <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.Create">
            <summary>Creates an empty <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</summary>
            <returns>Returns an <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.Create(System.Int32,System.Int32,System.Int32)">
            <summary>Creates an <see cref="T:XmpCore.Impl.XmpDateTime"/>-object from initial values.</summary>
            <param name="year">years</param>
            <param name="month">months from 1 to 12 (Remember that the month in <see cref="T:Sharpen.Calendar"/> is defined from 0 to 11)</param>
            <param name="day">days</param>
            <returns>Returns an <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.Create(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Creates an <see cref="T:XmpCore.Impl.XmpDateTime"/>-object from initial values.</summary>
            <param name="year">years</param>
            <param name="month">months from 1 to 12 (Remember that the month in <see cref="T:Sharpen.Calendar"/> is defined from 0 to 11)</param>
            <param name="day">days</param>
            <param name="hour">hours</param>
            <param name="minute">minutes</param>
            <param name="second">seconds</param>
            <param name="nanoSecond">nanoseconds</param>
            <returns>Returns an <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.CreateFromIso8601(System.String)">
            <summary>Creates an <see cref="T:XmpCore.Impl.XmpDateTime"/> from an ISO 8601 string.</summary>
            <param name="strValue">The ISO 8601 string representation of the date/time.</param>
            <returns>An <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
            <exception cref="T:XmpCore.XmpException">When the ISO 8601 string is non-conform</exception>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.GetCurrentDateTime">
            <summary>Obtain the current date and time.</summary>
            <returns>
            Returns The returned time is UTC, properly adjusted for the local time zone. The
            resolution of the time is not guaranteed to be finer than seconds.
            </returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.SetLocalTimeZone(XmpCore.IXmpDateTime)">
            <summary>
            Sets the local time zone without touching any other Any existing time zone value is replaced,
            the other date/time fields are not adjusted in any way.
            </summary>
            <param name="dateTime">the <see cref="T:XmpCore.Impl.XmpDateTime"/> variable containing the value to be modified.</param>
            <returns>Returns an updated <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.ConvertToUtcTime(XmpCore.IXmpDateTime)">
            <summary>Make sure a time is UTC.</summary>
            <remarks>
            Make sure a time is UTC. If the time zone is not UTC, the time is
            adjusted and the time zone set to be UTC.
            </remarks>
            <param name="dateTime">
            the <see cref="T:XmpCore.Impl.XmpDateTime"/> variable containing the time to
            be modified.
            </param>
            <returns>Returns an updated <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="M:XmpCore.XmpDateTimeFactory.ConvertToLocalTime(XmpCore.IXmpDateTime)">
            <summary>Make sure a time is local.</summary>
            <remarks>
            Make sure a time is local. If the time zone is not the local zone, the time is adjusted and
            the time zone set to be local.
            </remarks>
            <param name="dateTime">the <see cref="T:XmpCore.Impl.XmpDateTime"/> variable containing the time to be modified.</param>
            <returns>Returns an updated <see cref="T:XmpCore.Impl.XmpDateTime"/>-object.</returns>
        </member>
        <member name="T:XmpCore.XmpErrorCode">
            <author>Stefan Makswit</author>
        </member>
        <member name="F:XmpCore.XmpErrorCode.BadStream">
            <summary>This code is introduced by Java.</summary>
        </member>
        <member name="T:XmpCore.XmpException">
            <summary>This exception wraps all errors that occur in the XMP Toolkit.</summary>
            <author>Stefan Makswit</author>
            <since>16.02.2006</since>
        </member>
        <member name="P:XmpCore.XmpException.ErrorCode">
            <value>Gets the error code of the XMP toolkit.</value>
        </member>
        <member name="M:XmpCore.XmpException.#ctor(System.String,XmpCore.XmpErrorCode)">
            <summary>Constructs an exception with a message and an error code.</summary>
            <param name="message">the message</param>
            <param name="errorCode">the error code</param>
        </member>
        <member name="M:XmpCore.XmpException.#ctor(System.String,XmpCore.XmpErrorCode,System.Exception)">
            <summary>Constructs an exception with a message, an error code and an inner exception.</summary>
            <param name="message">the error message.</param>
            <param name="errorCode">the error code</param>
            <param name="innerException">the exception source</param>
        </member>
        <member name="T:XmpCore.XmpMetaFactory">
            <summary>Parses and serialises <see cref="T:XmpCore.IXmpMeta"/> instances.</summary>
            <author>Stefan Makswit</author>
            <since>30.01.2006</since>
        </member>
        <member name="P:XmpCore.XmpMetaFactory.SchemaRegistry">
            <value>Returns the singleton instance of the <see cref="T:XmpCore.Impl.XmpSchemaRegistry"/>.</value>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.Create">
            <returns>Returns an empty <see cref="T:XmpCore.IXmpMeta"/> instance.</returns>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.Parse(System.IO.Stream,XmpCore.Options.ParseOptions)">
            <summary>
            These functions support parsing serialized RDF into an XMP object, and serializing an XMP
            object into RDF.
            </summary>
            <remarks>
            These functions support parsing serialized RDF into an XMP object, and serializing an XMP
            object into RDF. The input for parsing may be any valid Unicode
            encoding. ISO Latin-1 is also recognized, but its use is strongly discouraged. Serialization
            is always as UTF-8.
            <para />
            <c>parseFromBuffer()</c> parses RDF from an <c>Stream</c>. The encoding
            is recognized automatically.
            </remarks>
            <param name="stream">an <c>Stream</c></param>
            <param name="options">Options controlling the parsing.
            The available options are:
            <list type="bullet">
              <item>XMP_REQUIRE_XMPMETA - The &lt;x:xmpmeta&gt; XML element is required around <tt>&lt;rdf:RDF&gt;</tt>.</item>
              <item>XMP_STRICT_ALIASING - Do not reconcile alias differences, throw an exception.</item>
            </list>
            Note: The XMP_STRICT_ALIASING option is not yet implemented.
            </param>
            <returns>Returns the <c>XMPMeta</c>-object created from the input.</returns>
            <exception cref="T:XmpCore.XmpException">If the file is not well-formed XML or if the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.ParseFromString(System.String,XmpCore.Options.ParseOptions)">
            <summary>Creates an <c>XMPMeta</c>-object from a string.</summary>
            <seealso cref="M:XmpCore.XmpMetaFactory.ParseFromString(System.String,XmpCore.Options.ParseOptions)"/>
            <param name="packet">a String contain an XMP-file.</param>
            <param name="options">Options controlling the parsing.</param>
            <returns>Returns the <c>XMPMeta</c>-object created from the input.</returns>
            <exception cref="T:XmpCore.XmpException">If the file is not well-formed XML or if the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.ParseFromBuffer(System.Byte[],XmpCore.Options.ParseOptions)">
            <summary>Creates an <c>XMPMeta</c>-object from a byte-buffer.</summary>
            <seealso cref="M:XmpCore.XmpMetaFactory.Parse(System.IO.Stream,XmpCore.Options.ParseOptions)"/>
            <param name="buffer">a String contain an XMP-file.</param>
            <param name="options">Options controlling the parsing.</param>
            <returns>Returns the <c>XMPMeta</c>-object created from the input.</returns>
            <exception cref="T:XmpCore.XmpException">If the file is not well-formed XML or if the parsing fails.</exception>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.Serialize(XmpCore.IXmpMeta,System.IO.Stream,XmpCore.Options.SerializeOptions)">
            <summary>Serializes an <c>XMPMeta</c>-object as RDF into an <c>OutputStream</c>.</summary>
            <param name="xmp">a metadata object</param>
            <param name="options">Options to control the serialization (see <see cref="T:XmpCore.Options.SerializeOptions"/>).</param>
            <param name="stream">an <c>OutputStream</c> to write the serialized RDF to.</param>
            <exception cref="T:XmpCore.XmpException">on serialization errors.</exception>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.SerializeToBuffer(XmpCore.IXmpMeta,XmpCore.Options.SerializeOptions)">
            <summary>Serializes an <c>XMPMeta</c>-object as RDF into a byte buffer.</summary>
            <param name="xmp">a metadata object</param>
            <param name="options">Options to control the serialization (see <see cref="T:XmpCore.Options.SerializeOptions"/>).</param>
            <returns>Returns a byte buffer containing the serialized RDF.</returns>
            <exception cref="T:XmpCore.XmpException">on serialization errors.</exception>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.SerializeToString(XmpCore.IXmpMeta,XmpCore.Options.SerializeOptions)">
            <summary>Serializes an <c>XMPMeta</c>-object as RDF into a string.</summary>
            <remarks>
            Serializes an <c>XMPMeta</c>-object as RDF into a string. <em>Note:</em> Encoding
            is ignored when serializing to a string.
            </remarks>
            <param name="xmp">a metadata object</param>
            <param name="options">Options to control the serialization (see <see cref="T:XmpCore.Options.SerializeOptions"/>).</param>
            <returns>Returns a string containing the serialized RDF.</returns>
            <exception cref="T:XmpCore.XmpException">on serialization errors.</exception>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.AssertImplementation(XmpCore.IXmpMeta)">
            <param name="xmp">Asserts that xmp is compatible to <c>XMPMetaImpl</c>.s</param>
        </member>
        <member name="M:XmpCore.XmpMetaFactory.Reset">
            <summary>Resets the schema registry to its original state (creates a new one).</summary>
            <remarks>
            Resets the schema registry to its original state (creates a new one).
            Be careful this might break all existing XMPMeta-objects and should be used
            only for testing purposes.
            </remarks>
        </member>
        <member name="P:XmpCore.XmpMetaFactory.VersionInfo">
            <summary>Obtain version information.</summary>
        </member>
        <member name="T:XmpCore.XmpPathFactory">
            <summary>Utility services for the metadata object.</summary>
            <remarks>
            It has only public static functions, you cannot create
            an object. These are all functions that layer cleanly on top of the core XMP toolkit.
            <para />
            These functions provide support for composing path expressions to deeply nested properties. The
            functions <c>XMPMeta</c> such as <c>GetProperty()</c>,
            <c>getArrayItem()</c> and <c>getStructField()</c> provide easy access to top
            level simple properties, items in top level arrays, and fields of top level structs. They do not
            provide convenient access to more complex things like fields several levels deep in a complex
            struct, or fields within an array of structs, or items of an array that is a field of a struct.
            These functions can also be used to compose paths to top level array items or struct fields so
            that you can use the binary accessors like <c>getPropertyAsInteger()</c>.
            <para />
            You can use these functions is to compose a complete path expression, or all but the last
            component. Suppose you have a property that is an array of integers within a struct. You can
            access one of the array items like this:
            <para />
            <code>
            string path = XmpPathFactory.ComposeStructFieldPath(schemaNS, "Struct", fieldNS, "Array");
            string path += XmpPathFactory.ComposeArrayItemPath(schemaNS, "Array", index);
            PropertyInteger result = xmpObj.GetPropertyAsInteger(schemaNS, path);
            </code>
            You could also use this code if you want the string form of the integer:
            <code>
            String path = XmpPathFactory.ComposeStructFieldPath (schemaNS, &quot;Struct&quot;, fieldNS,
            &quot;Array&quot;);
            PropertyText xmpObj.GetArrayItem (schemaNS, path, index);
            </code>
            <para />
            <em>Note:</em> It might look confusing that the schemaNS is passed in all of the calls above.
            This is because the XMP toolkit keeps the top level &quot;schema&quot; namespace separate from
            the rest of the path expression.
            <em>Note:</em> These methods are much simpler than in the C++-API, they don't check the given
            path or array indices.
            </remarks>
            <author>Stefan Makswit</author>
            <since>25.01.2006</since>
        </member>
        <member name="M:XmpCore.XmpPathFactory.ComposeArrayItemPath(System.String,System.Int32)">
            <summary>Compose the path expression for an item in an array.</summary>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string.
            </param>
            <param name="itemIndex">
            The index of the desired item. Arrays in XMP are indexed from 1.
            0 and below means last array item and renders as <c>[last()]</c>.
            </param>
            <returns>
            Returns the composed path basing on fullPath. This will be of the form
            <tt>ns:arrayName[i]</tt>, where &quot;ns&quot; is the prefix for schemaNS and
            &quot;i&quot; is the decimal representation of itemIndex.
            </returns>
            <exception cref="T:XmpCore.XmpException">Throws exception if index zero is used.</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.ComposeStructFieldPath(System.String,System.String)">
            <summary>Compose the path expression for a field in a struct.</summary>
            <param name="fieldNs">The namespace URI for the field. Must not be <c>null</c> or the empty string.</param>
            <param name="fieldName">The name of the field. Must be a simple XML name, must not be <c>null</c> or the empty string.</param>
            <returns>
            Returns the composed path. This will be of the form
            <tt>ns:structName/fNS:fieldName</tt>, where &quot;ns&quot; is the prefix for
            schemaNS and &quot;fNS&quot; is the prefix for fieldNS.
            </returns>
            <exception cref="T:XmpCore.XmpException">Thrown if the path to create is not valid.</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.ComposeQualifierPath(System.String,System.String)">
            <summary>Compose the path expression for a qualifier.</summary>
            <param name="qualNs">
            The namespace URI for the qualifier. May be <c>null</c> or the empty
            string if the qualifier is in the XML empty namespace.
            </param>
            <param name="qualName">
            The name of the qualifier. Must be a simple XML name, must not be
            <c>null</c> or the empty string.
            </param>
            <returns>
            Returns the composed path. This will be of the form
            <tt>ns:propName/?qNS:qualName</tt>, where &quot;ns&quot; is the prefix for
            schemaNS and &quot;qNS&quot; is the prefix for qualNS.
            </returns>
            <exception cref="T:XmpCore.XmpException">Thrown if the path to create is not valid.</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.ComposeLangSelector(System.String,System.String)">
            <summary>Compose the path expression to select an alternate item by language.</summary>
            <remarks>
            The path syntax allows two forms of &quot;content addressing&quot; that may
            be used to select an item in an array of alternatives. The form used in
            ComposeLangSelector lets you select an item in an alt-text array based on
            the value of its <tt>xml:lang</tt> qualifier. The other form of content
            addressing is shown in ComposeFieldSelector. \note ComposeLangSelector
            does not supplant SetLocalizedText or GetLocalizedText. They should
            generally be used, as they provide extra logic to choose the appropriate
            language and maintain consistency with the 'x-default' value.
            ComposeLangSelector gives you an path expression that is explicitly and
            only for the language given in the langName parameter.
            </remarks>
            <param name="arrayName">
            The name of the array. May be a general path expression, must
            not be <c>null</c> or the empty string.
            </param>
            <param name="langName">The RFC 3066 code for the desired language.</param>
            <returns>
            Returns the composed path. This will be of the form
            <tt>ns:arrayName[@xml:lang='langName']</tt>, where
            &quot;ns&quot; is the prefix for schemaNS.
            </returns>
        </member>
        <member name="M:XmpCore.XmpPathFactory.ComposeFieldSelector(System.String,System.String,System.String,System.String)">
            <summary>Compose the path expression to select an alternate item by a field's value.</summary>
            <remarks>
            The path syntax allows two forms of &quot;content addressing&quot; that may be used to select an item in an
            array of alternatives. The form used in ComposeFieldSelector lets you select an item in an
            array of structs based on the value of one of the fields in the structs. The other form of
            content addressing is shown in ComposeLangSelector. For example, consider a simple struct
            that has two fields, the name of a city and the URI of an FTP site in that city. Use this to
            create an array of download alternatives. You can show the user a popup built from the values
            of the city fields. You can then get the corresponding URI as follows:
            <para />
            <code>
            String path = composeFieldSelector ( schemaNS, &quot;Downloads&quot;, fieldNS, &quot;City&quot;, chosenCity );
            XMPProperty prop = xmpObj.getStructField ( schemaNS, path, fieldNS, &quot;URI&quot; );
            </code>
            </remarks>
            <param name="arrayName">
            The name of the array. May be a general path expression, must not be
            <c>null</c> or the empty string.
            </param>
            <param name="fieldNs">
            The namespace URI for the field used as the selector. Must not be
            <c>null</c> or the empty string.
            </param>
            <param name="fieldName">
            The name of the field used as the selector. Must be a simple XML name, must
            not be <c>null</c> or the empty string. It must be the name of a field that is
            itself simple.
            </param>
            <param name="fieldValue">The desired value of the field.</param>
            <returns>
            Returns the composed path. This will be of the form
            <tt>ns:arrayName[fNS:fieldName='fieldValue']</tt>, where &quot;ns&quot; is the
            prefix for schemaNS and &quot;fNS&quot; is the prefix for fieldNS.
            </returns>
            <exception cref="T:XmpCore.XmpException">Thrown if the path to create is not valid.</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.AssertQualNs(System.String)">
            <summary>ParameterAsserts that a qualifier namespace is set.</summary>
            <param name="qualNs">a qualifier namespace</param>
            <exception cref="T:XmpCore.XmpException">Qualifier schema is null or empty</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.AssertQualName(System.String)">
            <summary>ParameterAsserts that a qualifier name is set.</summary>
            <param name="qualName">a qualifier name or path</param>
            <exception cref="T:XmpCore.XmpException">Qualifier name is null or empty</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.AssertFieldNs(System.String)">
            <summary>ParameterAsserts that a struct field namespace is set.</summary>
            <param name="fieldNs">a struct field namespace</param>
            <exception cref="T:XmpCore.XmpException">Struct field schema is null or empty</exception>
        </member>
        <member name="M:XmpCore.XmpPathFactory.AssertFieldName(System.String)">
            <summary>ParameterAsserts that a struct field name is set.</summary>
            <param name="fieldName">a struct field name or path</param>
            <exception cref="T:XmpCore.XmpException">Struct field name is null or empty</exception>
        </member>
        <member name="T:XmpCore.XmpUtils">
            <summary>Utility methods for XMP.</summary>
            <author>Stefan Makswit</author>
            <since>21.02.2006</since>
        </member>
        <member name="M:XmpCore.XmpUtils.CatenateArrayItems(XmpCore.IXmpMeta,System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>Create a single edit string from an array of strings.</summary>
            <param name="xmp">The XMP object containing the array to be catenated.</param>
            <param name="schemaNs">
            The schema namespace URI for the array. Must not be null or
            the empty string.
            </param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must
            not be null or the empty string. Each item in the array must
            be a simple string value.
            </param>
            <param name="separator">
            The string to be used to separate the items in the catenated
            string. Defaults to &quot;; &quot;, ASCII semicolon and space
            (U+003B, U+0020).
            </param>
            <param name="quotes">
            The characters to be used as quotes around array items that
            contain a separator. Defaults to &apos;&quot;&apos;
            </param>
            <param name="allowCommas">Option flag to control the catenation.</param>
            <returns>Returns the string containing the catenated array items.</returns>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.SeparateArrayItems(XmpCore.IXmpMeta,System.String,System.String,System.String,XmpCore.Options.PropertyOptions,System.Boolean)">
            <summary>Separate a single edit string into an array of strings.</summary>
            <param name="xmp">The XMP object containing the array to be updated.</param>
            <param name="schemaNs">
            The schema namespace URI for the array. Must not be null or
            the empty string.
            </param>
            <param name="arrayName">
            The name of the array. May be a general path expression, must
            not be null or the empty string. Each item in the array must
            be a simple string value.
            </param>
            <param name="catedStr">The string to be separated into the array items.</param>
            <param name="arrayOptions">Option flags to control the separation.</param>
            <param name="preserveCommas">Flag if commas shall be preserved</param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.RemoveProperties(XmpCore.IXmpMeta,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>Remove multiple properties from an XMP object.</summary>
            <remarks>
            Remove multiple properties from an XMP object.
            RemoveProperties was created to support the File Info dialog's Delete
            button, and has been been generalized somewhat from those specific needs.
            It operates in one of three main modes depending on the schemaNS and
            propName parameters:
            <list type="bullet">
            <item> Non-empty <c>schemaNS</c> and <c>propName</c> - The named property is
            removed if it is an external property, or if the
            flag <c>doAllProperties</c> option is true. It does not matter whether the
            named property is an actual property or an alias.</item>
            <item> Non-empty <c>schemaNS</c> and empty <c>propName</c> - The all external
            properties in the named schema are removed. Internal properties are also
            removed if the flag <c>doAllProperties</c> option is set. In addition,
            aliases from the named schema will be removed if the flag <c>includeAliases</c>
            option is set.</item>
            <item> Empty <c>schemaNS</c> and empty <c>propName</c> - All external properties in
            all schema are removed. Internal properties are also removed if the
            flag <c>doAllProperties</c> option is passed. Aliases are implicitly handled
            because the associated actuals are internal if the alias is.</item>
            </list>
            It is an error to pass an empty <c>schemaNS</c> and non-empty <c>propName</c>.
            </remarks>
            <param name="xmp">The XMP object containing the properties to be removed.</param>
            <param name="schemaNs">
            Optional schema namespace URI for the properties to be
            removed.
            </param>
            <param name="propName">Optional path expression for the property to be removed.</param>
            <param name="doAllProperties">
            Option flag to control the deletion: do internal properties in
            addition to external properties.
            </param>
            <param name="includeAliases">
            Option flag to control the deletion:
            Include aliases in the "named schema" case above.
            <em>Note:</em> Currently not supported.
            </param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.AppendProperties(XmpCore.IXmpMeta,XmpCore.IXmpMeta,System.Boolean,System.Boolean,System.Boolean)">
            <summary>Append properties from one XMP object to another.</summary>
            <remarks>
            Append properties from one XMP object to another.
            <para />XMPUtils#appendProperties was created to support the File Info dialog's Append button, and
            has been been generalized somewhat from those specific needs. It appends information from one
            XMP object (source) to another (dest). The default operation is to append only external
            properties that do not already exist in the destination. The flag
            <c>doAllProperties</c> can be used to operate on all properties, external and internal.
            The flag <c>replaceOldValues</c> option can be used to replace the values
            of existing properties. The notion of external
            versus internal applies only to top level properties. The keep-or-replace-old notion applies
            within structs and arrays as described below.
            <list type="bullet">
            <item>If <c>replaceOldValues</c> is true then the processing is restricted to the top
            level properties. The processed properties from the source (according to
            <c>doAllProperties</c>) are propagated to the destination,
            replacing any existing values.Properties in the destination that are not in the source
            are left alone.</item>
            <item>If <c>replaceOldValues</c> is not passed then the processing is more complicated.
            Top level properties are added to the destination if they do not already exist.
            If they do exist but differ in form (simple/struct/array) then the destination is left alone.
            If the forms match, simple properties are left unchanged while structs and arrays are merged.</item>
            <item>If <c>deleteEmptyValues</c> is passed then an empty value in the source XMP causes
            the corresponding destination XMP property to be deleted. The default is to treat empty
            values the same as non-empty values. An empty value is any of a simple empty string, an array
            with no items, or a struct with no fields. Qualifiers are ignored.</item>
            </list>
            <para />
            The detailed behavior is defined by the following pseudo-code:
            <code>
            appendProperties ( sourceXMP, destXMP, doAllProperties,
            replaceOldValues, deleteEmptyValues ):
            for all source schema (top level namespaces):
            for all top level properties in sourceSchema:
            if doAllProperties or prop is external:
            appendSubtree ( sourceNode, destSchema, replaceOldValues, deleteEmptyValues )
            appendSubtree ( sourceNode, destParent, replaceOldValues, deleteEmptyValues ):
            if deleteEmptyValues and source value is empty:
            delete the corresponding child from destParent
            else if sourceNode not in destParent (by name):
            copy sourceNode's subtree to destParent
            else if replaceOld:
            delete subtree from destParent
            copy sourceNode's subtree to destParent
            else:
            // Already exists in dest and not replacing, merge structs and arrays
            if sourceNode and destNode forms differ:
            return, leave the destNode alone
            else if form is a struct:
            for each field in sourceNode:
            AppendSubtree ( sourceNode.field, destNode, replaceOldValues )
            else if form is an alt-text array:
            copy new items by "xml:lang" value into the destination
            else if form is an array:
            copy new items by value into the destination, ignoring order and duplicates
            </code>
            <para /><em>Note:</em> appendProperties can be expensive if replaceOldValues is not passed and
            the XMP contains large arrays. The array item checking described above is n-squared.
            Each source item is checked to see if it already exists in the destination,
            without regard to order or duplicates.
            <para />Simple items are compared by value and "xml:lang" qualifier, other qualifiers are ignored.
            Structs are recursively compared by field names, without regard to field order. Arrays are
            compared by recursively comparing all items.
            </remarks>
            <param name="source">The source XMP object.</param>
            <param name="dest">The destination XMP object.</param>
            <param name="doAllProperties">Do internal properties in addition to external properties.</param>
            <param name="replaceOldValues">Replace the values of existing properties.</param>
            <param name="deleteEmptyValues">Delete destination values if source property is empty.</param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertToBoolean(System.String)">
            <summary>Convert from string to Boolean.</summary>
            <param name="value">The string representation of the Boolean.</param>
            <returns>
            The appropriate boolean value for the string. The checked values
            for <c>true</c> and <c>false</c> are:
            <list type="bullet">
            <item><see cref="F:XmpCore.XmpConstants.TrueString"/> and <see cref="F:XmpCore.XmpConstants.FalseString"/></item>
            <item>&quot;t&quot; and &quot;f&quot;</item>
            <item>&quot;on&quot; and &quot;off&quot;</item>
            <item>&quot;yes&quot; and &quot;no&quot;</item>
            <item>&quot;value &lt;&gt; 0&quot; and &quot;value == 0&quot;</item>
            </list>
            </returns>
            <exception cref="T:XmpCore.XmpException">If an empty string is passed.</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertFromBoolean(System.Boolean)">
            <summary>Convert from boolean to string.</summary>
            <param name="value">a boolean value</param>
            <returns>
            The XMP string representation of the boolean. The values used are
            given by the constants <see cref="F:XmpCore.XmpConstants.TrueString"/> and
            <see cref="F:XmpCore.XmpConstants.FalseString"/>.
            </returns>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertToInteger(System.String)">
            <summary>Converts a string value to an <c>int</c>.</summary>
            <param name="rawValue">the string value</param>
            <returns>Returns an int.</returns>
            <exception cref="T:XmpCore.XmpException">
            If the <c>rawValue</c> is <c>null</c> or empty or the
            conversion fails.
            </exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertFromInteger(System.Int32)">
            <summary>Convert from int to string.</summary>
            <param name="value">an int value</param>
            <returns>The string representation of the int.</returns>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertToLong(System.String)">
            <summary>Converts a string value to a <c>long</c>.</summary>
            <param name="rawValue">the string value</param>
            <returns>Returns a long.</returns>
            <exception cref="T:XmpCore.XmpException">
            If the <c>rawValue</c> is <c>null</c> or empty or the
            conversion fails.
            </exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertFromLong(System.Int64)">
            <summary>Convert from long to string.</summary>
            <param name="value">a long value</param>
            <returns>The string representation of the long.</returns>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertToDouble(System.String)">
            <summary>Converts a string value to a <c>double</c>.</summary>
            <param name="rawValue">the string value</param>
            <returns>Returns a double.</returns>
            <exception cref="T:XmpCore.XmpException">
            If the <c>rawValue</c> is <c>null</c> or empty or the
            conversion fails.
            </exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertFromDouble(System.Double)">
            <summary>Convert from long to string.</summary>
            <param name="value">a long value</param>
            <returns>The string representation of the long.</returns>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertToDate(System.String)">
            <summary>Converts a string value to an <c>XMPDateTime</c>.</summary>
            <param name="rawValue">the string value</param>
            <returns>Returns an <c>XMPDateTime</c>-object.</returns>
            <exception cref="T:XmpCore.XmpException">
            If the <c>rawValue</c> is <c>null</c> or empty or the
            conversion fails.
            </exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ConvertFromDate(XmpCore.IXmpDateTime)">
            <summary>Convert from <c>XMPDateTime</c> to string.</summary>
            <param name="value">an <c>XMPDateTime</c></param>
            <returns>The string representation of the long.</returns>
        </member>
        <member name="M:XmpCore.XmpUtils.EncodeBase64(System.Byte[])">
            <summary>Convert from a byte array to a base64 encoded string.</summary>
            <param name="buffer">the byte array to be converted</param>
            <returns>Returns the base64 string.</returns>
        </member>
        <member name="M:XmpCore.XmpUtils.DecodeBase64(System.String)">
            <summary>Decode from Base64 encoded string to raw data.</summary>
            <param name="base64String">a base64 encoded string</param>
            <returns>Returns a byte array containing the decoded string.</returns>
            <exception cref="T:XmpCore.XmpException">Thrown if the given string is not property base64 encoded</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.PackageForJPEG(XmpCore.IXmpMeta,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>Creates XMP serializations appropriate for a JPEG file.</summary>
            <remarks>The standard XMP in a JPEG file is limited to 64K bytes. This function
            serializes the XMP metadata in an XMP object into a string of RDF . If
            the data does not fit into the 64K byte limit, it creates a second packet
            string with the extended data.</remarks>
            <param name="origXMP">The XMP object containing the metadata.</param>
            <param name="stdStr">A string builder object in which to return the full standard XMP packet.</param>
            <param name="extStr">A string builder object in which to return the serialized extended XMP, empty if not needed.</param>
            <param name="digestStr">A string builder object in which to return an MD5 digest of the serialized extended XMP, empty if not needed.</param>
            @throws NoSuchAlgorithmException if fail to find algorithm for MD5
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.MergeFromJPEG(XmpCore.IXmpMeta,XmpCore.IXmpMeta)">
            <summary>Merges standard and extended XMP retrieved from a JPEG file.</summary>
            <remarks>When an extended partition stores properties that do not fit into the
            JPEG file limitation of 64K bytes, this function integrates those
            properties back into the same XMP object with those from the standard XMP
            packet.</remarks>
            <param name="fullXMP">An XMP object which the caller has initialized from the standard XMP packet in a JPEG file. The extended XMP is added to this object.</param>
            <param name="extendedXMP">An XMP object which the caller has initialized from the extended XMP packet in a JPEG file.</param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.ApplyTemplate(XmpCore.IXmpMeta,XmpCore.IXmpMeta,XmpCore.Options.TemplateOptions)">
            <summary>Modifies a working XMP object according to a template object.</summary>
            <remarks>
            The XMP template can be used to add, replace or delete properties from
            the working XMP object. The actions that you specify determine how the
            template is applied.Each action can be applied individually or combined;
            if you do not specify any actions, the properties and values in the
            working XMP object do not change.
            <para />
            These actions are available:
            <list type="bullet">
            <item>Clear <c>CLEAR_UNNAMED_PROPERTIES</c> : Deletes top-level
            properties.Any top-level property that is present in the template(even
            with empty value) is retained.All other top-level properties in the
            working object are deleted</item>
            <item>Add <c>ADD_NEW_PROPERTIES</c>: Adds new properties to the
            working object if the template properties have values.See additional
            detail below.</item>
            <item>Replace <c>REPLACE_EXISTING_PROPERTIES</c>: Replaces the
            values of existing top-level properties in the working XMP if the value
            forms match those in the template. Properties with empty values in the
            template are ignored. If combined with Clear or Add actions, those take
            precedence; values are cleared or added, rather than replaced.</item>
            <item>Replace/Delete empty <c>REPLACE_WITH_DELETE_EMPTY</c>:
            Replaces values in the same way as the simple Replace action, and also
            deletes properties if the value in the template is empty.If combined
            with Clear or Add actions, those take precedence; values are cleared or
            added, rather than replaced.</item>
            <item>Include internal <c>INCLUDE_INTERNAL_PROPERTIES</c>: Performs
            specified action on internal properties as well as external properties.
            By default, internal properties are ignored for all actions.</item>
            </list>
            <para />
            The Add behavior depends on the type of property:
            <list type="bullet">
            <item>If a top-level property is not in the working XMP, and has a value in
            the template, the property and value are added.Empty properties are not
            added.</item>
            <item>If a property is in both the working XMP and template, the value
            forms must match, otherwise the template is ignored for that property.</item>
            <item>If a struct is present in both the working XMP and template, the
            individual fields of the template struct are added as appropriate; that
            is, the logic is recursively applied to the fields.Struct values are
            equivalent if they have the same fields with equivalent values.</item>
            <item>If an array is present in both the working XMP and template, items
            from the template are added if the value forms match. Array values match
            if they have sets of equivalent items, regardless of order.</item>
            <item>Alt-text arrays use the \c xml:lang qualifier as a key, adding languages that are missing.</item>
            </list>
            <para />
            Array item checking is n-squared; this can be time-intensive if the
            Replace option is not specified.Each source item is checked to see if it
            already exists in the destination, without regard to order or duplicates.
            Simple items are compared by value and<code> xml:lang</code> qualifier;
            other qualifiers are ignored.Structs are recursively compared by field
            names, without regard to field order.Arrays are compared by recursively
            comparing all items.
            </remarks>
            <param name="workingXMP">The destination XMP object.</param>
            <param name="templateXMP">The template to apply to the destination XMP object.</param>
            <param name="options">Option flags to control the copying. If none are specified,
            the properties and values in the working XMP do not change. A logical OR of these bit-flag constants:
            <list type="bullet">
            <item><c>CLEAR_UNNAMED_PROPERTIES</c> Delete anything that is not in the template</item>
            <item><c>ADD_NEW_PROPERTIES</c> Add properties; see detailed description.</item>
            <item><c>REPLACE_EXISTING_PROPERTIES</c> Replace the values of existing properties.</item>
            <item><c>REPLACE_WITH_DELETE_EMPTY</c> Replace the values of existing properties and delete properties if the new value is empty.</item>
            <item><c>INCLUDE_INTERNAL_PROPERTIES</c> Operate on internal properties as well as external properties.</item>
            </list>
            </param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="M:XmpCore.XmpUtils.DuplicateSubtree(XmpCore.IXmpMeta,XmpCore.IXmpMeta,System.String,System.String,System.String,System.String,XmpCore.Options.PropertyOptions)">
            <summary>Replicate a subtree from one XMP object into another, possibly at a
            different location.</summary>
            <param name="source">The source XMP object.</param>
            <param name="dest">The destination XMP object.</param>
            <param name="sourceNS">The schema namespace URI for the source subtree.</param>
            <param name="sourceRoot">The root location for the source subtree. May be a general path expression, must not be null or the empty string.</param>
            <param name="destNS">The schema namespace URI for the destination. Defaults to the source namespace.</param>
            <param name="destRoot">The root location for the destination. May be a general path expression. Defaults to the source location.</param>
            <param name="options">Option flags to control the separation. (For now, this argument is ignored. 0 should be passed.</param>
            <exception cref="T:XmpCore.XmpException">Forwards the Exceptions from the metadata processing</exception>
        </member>
        <member name="T:Sharpen.PushbackReader">
            <summary>
            http://grepcode.com/file_/repository.grepcode.com/java/root/jdk/openjdk/6-b14/java/io/PushbackReader.java/?v=source
            </summary>
        </member>
    </members>
</doc>
