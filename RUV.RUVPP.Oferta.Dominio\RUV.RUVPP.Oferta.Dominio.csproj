﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{BDEDFAB0-F491-43B0-8DC4-8FB85A772425}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RUV.RUVPP.Oferta.Dominio</RootNamespace>
    <AssemblyName>RUV.RUVPP.Oferta.Domino</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RUV.RUVPP.Oferta.Domino.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>
    </DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Integracion|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Integracion\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>bin\Debug\RUV.RUVPP.Oferta.Domino.xml</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\QA\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>bin\Debug\RUV.RUVPP.Oferta.Domino.xml</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Produccion|AnyCPU'">
    <OutputPath>bin\Produccion\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ClosedXML, Version=0.88.0.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.88.0\lib\net452\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="CoordinateSharp, Version=2.6.1.1, Culture=neutral, PublicKeyToken=f45e02df057b7725, processorArchitecture=MSIL">
      <HintPath>..\packages\CoordinateSharp.2.6.1.1\lib\net40\CoordinateSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Dapper, Version=1.50.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.50.2\lib\net451\Dapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v12.2.Core, Version=12.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Web.2.6.2.3\lib\net452\DevExpress.RichEdit.v12.2.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=*******, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.7.2\lib\net40\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="FastMember.Signed, Version=*******, Culture=neutral, PublicKeyToken=9e8f22703bef9a29, processorArchitecture=MSIL">
      <HintPath>..\packages\FastMember.Signed.1.1.0\lib\net40\FastMember.Signed.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=*******99, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.0.0\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="itextsharp, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.********\lib\itextsharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MetadataExtractor, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MetadataExtractor.2.4.2\lib\net45\MetadataExtractor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.0.7\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.2.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.2.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.2.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.2.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.2.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.2.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.PersistenceChannel, Version=1.2.3.490, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PersistenceChannel.1.2.3\lib\net45\Microsoft.ApplicationInsights.PersistenceChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Edm, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.6.4\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.6.4\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Services.Client.5.6.4\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Diagnostics.Tracing.EventSource, Version=1.1.28.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.EventSource.Redist.1.1.28\lib\net40\Microsoft.Diagnostics.Tracing.EventSource.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocol.Extensions, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocol.Extensions.1.0.0\lib\net45\Microsoft.IdentityModel.Protocol.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Cors.3.0.0\lib\net45\Microsoft.Owin.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.0.1\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Jwt, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Jwt.3.0.0\lib\net45\Microsoft.Owin.Security.Jwt.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.0.1\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OpenIdConnect, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OpenIdConnect.3.0.0\lib\net45\Microsoft.Owin.Security.OpenIdConnect.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.TransientFaultHandling.6.0.1304.0\lib\portable-net45+win+wp8\Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.TransientFaultHandling.Data.6.0.1304.1\lib\NET45\Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.1.3\lib\portable-net4+sl5+netcore45+wpa81+wp8\Microsoft.Practices.ServiceLocation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.ReportViewer.11.0.3366.16\lib\Microsoft.ReportViewer.WebForms.DLL</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions.Desktop, Version=1.0.168.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.Desktop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.1\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=7.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.7.0.0\lib\net40\Microsoft.WindowsAzure.Storage.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.9.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI, Version=2.4.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.4.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OOXML.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.4.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.4.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.12.1.24160719\lib\net40\Oracle.ManagedDataAccess.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RestSharp, Version=10*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun, Version=2.6.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.2.6.0\lib\net452\RUV.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Datos, Version=2.6.2.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Datos.2.6.2.1\lib\net452\RUV.Comun.Datos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Negocio, Version=2.6.0.2, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Negocio.2.6.0.2\lib\net452\RUV.Comun.Negocio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Seguridad.JWT, Version=2.6.0.8, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Seguridad.JWT.2.6.0.8\lib\net452\RUV.Comun.Seguridad.JWT.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Servicios, Version=2.6.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Servicios.2.6.1\lib\net452\RUV.Comun.Servicios.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Utilerias, Version=2.6.2.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Utilerias.2.6.2.4\lib\net452\RUV.Comun.Utilerias.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Web, Version=2.6.2.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Web.2.6.2.3\lib\net452\RUV.Comun.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Integracion.Entidades.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.Integracion.Entidades.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Integracion.Procesos.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.Integracion.Procesos.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento.Historico, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.Contratos, Version=2.5.0.7, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Datos.Comun.Contratos.2.5.0.7\lib\net452\RUV.RUVPP.Datos.Comun.Contratos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.Historico, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Comun.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Comun.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Empresa, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Empresa.SqlAzure, Version=2.5.0.5, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Datos.Empresa.SqlAzure.2.5.0.5\lib\net452\RUV.RUVPP.Datos.Empresa.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Entorno, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Entorno.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Entorno.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Entorno.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.General, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.General.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.General.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.General.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Oferta.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta.Historico, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Oferta.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Oferta.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.SAP.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.SAP.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ubicador, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Ubicador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ubicador.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.Empresa.2.5.0.11\lib\net452\RUV.RUVPP.Datos.Ubicador.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Ayuntamiento, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Ayuntamiento.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Comun, Version=1.0.0.15, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.Comun.1.0.0.15\lib\net452\RUV.RUVPP.Entidades.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Empresa, Version=2.5.0.12, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Entidades.Empresa.2.5.0.11\lib\net452\RUV.RUVPP.Entidades.Empresa.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Entorno, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Entorno.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Bam, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Bam.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Documentos, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Documentos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Indicadores, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Indicadores.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Notificaciones, Version=2.5.1.33, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.General.Notificaciones.2.5.1.33\lib\net452\RUV.RUVPP.Entidades.General.Notificaciones.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.OrdenServicio, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.OrdenServicio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Seguridad, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Seguridad.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tareas, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Tareas.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tarificador, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Tarificador.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tutoriales, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Tutoriales.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Oferta, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Oferta.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Ubicador, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Ubicador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Datos, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Datos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Domino, Version=1.0.0.37, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Domino.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Interop, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Interop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Modelo, Version=1.0.0.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Modelo.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Integracion.Procesos.Empresa, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Integracion.Procesos.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.Comun.ProductosServicios, Version=2.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Negocio.Comun.ProductosServicios.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.Empresa, Version=2.5.0.14, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Bam, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.General.Bam.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Documentos, Version=2.5.2.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Documentos.2.5.2.1\lib\net452\RUV.RUVPP.Negocio.General.Documentos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Notificaciones, Version=2.5.1.23, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Notificaciones.2.5.1.23\lib\net452\RUV.RUVPP.Negocio.General.Notificaciones.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.OrdenServicio, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.General.OrdenServicio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.OrdenTrabajo, Version=2.5.0.10, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.OrdenTrabajo.2.5.0.10\lib\net452\RUV.RUVPP.Negocio.General.OrdenTrabajo.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Seguridad, Version=2.5.0.16, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Seguridad.2.5.0.16\lib\net452\RUV.RUVPP.Negocio.General.Seguridad.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Tarificador, Version=2.5.2.77, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Negocio.General.Tarificador.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=1.0.316.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.1.0.394\lib\net45\StackExchange.Redis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Abstractions, Version=2.0.2.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.Abstractions.2.0.2.26\lib\net45\System.Configuration.Abstractions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Primitives.4.3.0\lib\net45\System.Drawing.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.4.0.1\lib\net45\System.IdentityModel.Tokens.Jwt.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Spatial, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.6.4\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.3\lib\net45\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.3\lib\net45\System.Web.Http.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="XmpCore, Version=6.1.10.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\XmpCore.6.1.10\lib\net35\XmpCore.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Avaluo\Implementacion\ServicioAvaluo.cs" />
    <Compile Include="Avaluo\IServicioAvaluo.cs" />
    <Compile Include="Bancos\Implementacion\ServicioBancos.cs" />
    <Compile Include="Bancos\IServicioBanco.cs" />
    <Compile Include="Dictaminacion\Implementacion\ServicioDictaminacion.cs" />
    <Compile Include="Dictaminacion\IServicioDictaminacion.cs" />
    <Compile Include="Empresa\Implementacion\ServicioEmpresa.cs" />
    <Compile Include="Empresa\IServicioEmpresa.cs" />
    <Compile Include="Excepciones\ExcepcionBase.cs" />
    <Compile Include="Excepciones\ExcepcionSig.cs" />
    <Compile Include="Excepciones\ExceptionProyectos.cs" />
    <Compile Include="Historico\Implementacion\ServicioHistoricos.cs" />
    <Compile Include="Historico\IServicioHistoricos.cs" />
    <Compile Include="Mediciones\Implementacion\ServicioMediciones.cs" />
    <Compile Include="Mediciones\IServicioMediciones.cs" />
    <Compile Include="Oferta\Implementacion\ServicioOferta.cs" />
    <Compile Include="Oferta\IServicioOferta.cs" />
    <Compile Include="OrdenVerificacion\Implementacion\ServicioOrdenVerificacion.cs" />
    <Compile Include="OrdenVerificacion\IServicioOrdenVerficacion.cs" />
    <Compile Include="ProductoServicio\Implementacion\ServicioProductoServicio.cs" />
    <Compile Include="ProductoServicio\IServicioProductoServicio.cs" />
    <Compile Include="Promotor\Implementacion\ServicioPromotor.cs" />
    <Compile Include="Promotor\IServicioPromotor.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Prototipo\Implementacion\ServicioPrototipo.cs" />
    <Compile Include="Prototipo\IServicioPrototipoCatalogos.cs" />
    <Compile Include="Prototipo\IServicioPrototipo.cs" />
    <Compile Include="Prototipo\Implementacion\ServicioPrototipoCatalogos.cs" />
    <Compile Include="Proyectos\Implementacion\ServicioProyectos.cs" />
    <Compile Include="Proyectos\Implementacion\ServicioProyectosCatalogos.cs" />
    <Compile Include="Proyectos\IServicioProyectos.cs" />
    <Compile Include="Proyectos\IServicioProyectosCatalogos.cs" />
    <Compile Include="SeguroCalidad\Implementacion\ExcelPOI.cs" />
    <Compile Include="SeguroCalidad\Implementacion\ServicioSeguroCalidad.cs" />
    <Compile Include="SeguroCalidad\IServicioSeguroCalidad.cs" />
    <Compile Include="Service References\ServiceClientSAP\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\ServicioCUV\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\Servlet_recibeFoto\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="Util\FileIO.cs" />
    <Compile Include="Util\Reflections.cs" />
    <Compile Include="Util\TableStorage.cs" />
    <Compile Include="Util\ValidacionesServiceActualizaSiniestro.cs" />
    <Compile Include="Util\ValidacionesServiceSiniestro.cs" />
    <Compile Include="Viviendas\Implementacion\ServicioVivienda.cs" />
    <Compile Include="Viviendas\IServicioVivienda.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Datos\RUV.RUVPP.Oferta.Datos.csproj">
      <Project>{9a22925d-5f26-47b6-8777-b8d87a412ed8}</Project>
      <Name>RUV.RUVPP.Oferta.Datos</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.GeneralesInterop\RUV.RUVPP.Oferta.GeneralesInterop.csproj">
      <Project>{a429aec1-ba17-4f6f-96c7-d4a52f760f09}</Project>
      <Name>RUV.RUVPP.Oferta.GeneralesInterop</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Modelo\RUV.RUVPP.Oferta.Modelo.csproj">
      <Project>{6266a8ca-7163-4580-8bb8-7a0fa604e5d3}</Project>
      <Name>RUV.RUVPP.Oferta.Modelo</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Reportes\RUV.RUVPP.Oferta.Reportes.csproj">
      <Project>{ca0945aa-9314-4816-85f8-5f3f6a61fdfc}</Project>
      <Name>RUV.RUVPP.Oferta.Reportes</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="RUV.RUVPP.Oferta.Dominio.nuspec" />
    <None Include="Service References\ServiceClientSAP\service.wsdl" />
    <None Include="Service References\ServicioCUV\RUV.RUVPP.Oferta.Dominio.ServicioCUV.ConsultaBitacora.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServicioCUV\RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServicioCUV\RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertice.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\ServicioCUV\ServicioCUV.wsdl">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\Servlet_recibeFoto\MessageServlet_recibeFoto_QA.wsdl" />
    <None Include="Service References\Servlet_recibeFoto\MessageServlet_recibeFoto_QA.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\Servlet_recibeFoto\RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\Servlet_recibeFoto\RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\ServicioCUV\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Service References\ServicioCUV\configuration.svcinfo" />
    <None Include="Service References\ServicioCUV\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\ServiceClientSAP\" />
    <WCFMetadataStorage Include="Service References\ServicioCUV\" />
    <WCFMetadataStorage Include="Service References\Servlet_recibeFoto\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\ServiceClientSAP\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Service References\ServiceClientSAP\configuration.svcinfo" />
    <None Include="Service References\ServiceClientSAP\configuration91.svcinfo" />
    <None Include="Service References\ServiceClientSAP\service.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\Servlet_recibeFoto\configuration91.svcinfo" />
    <None Include="Service References\Servlet_recibeFoto\configuration.svcinfo" />
    <None Include="Service References\Servlet_recibeFoto\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="Recursos\FichaPago.html" />
    <Content Include="Recursos\FichaPago2.html" />
    <Content Include="Resources\ruv.png" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir).nuget\NuGet.targets" Condition="Exists('$(SolutionDir).nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir).nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir).nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>  -->
  <Target Name="AfterBuild" Condition=" '$(Configuration)' == 'Release'">
    <Exec Command="$(SolutionDir).nuget\nuget pack $(ProjectDir)$(ProjectFileName) -IncludeReferencedProjects -Prop Configuration=$(Configuration)">
    </Exec>
  </Target>
</Project>