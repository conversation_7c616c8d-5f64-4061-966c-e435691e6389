﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Negocio;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using RUV.RUVPP.Oferta.Modelo.Viviendas.Api;
using Newtonsoft.Json;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System.Web.Script.Serialization;
using RUV.Comun.Core;
using RUV.RUVPP.Oferta.Dominio.ServicioCUV;
using System.ServiceModel;
using System.Configuration;

namespace RUV.RUVPP.Oferta.Dominio.Viviendas.Implementacion
{
    /// <summary>
    /// 
    /// </summary>
    public class ServicioVivienda : ServicioDominioBase, IServicioVivienda
    {

        private int activarSig = Convert.ToInt32(ConfigurationManager.AppSettings["ActivarIntegracionSIG"]);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        public ServicioVivienda(TelemetryClient clienteTelemetria) : base(clienteTelemetria)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <param name="vivienda"></param>
        /// <param name="fechaRegitro"></param>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        public async Task<List<Modelo.Proyectos.Data.Vivienda>> GenerarCuvs(int idProyecto, List<Vivienda> vivienda, DateTime fechaRegitro, string idEstado, string idMunicipio)
        {
            vivienda = await GenerarCuvTabular(idProyecto, vivienda, fechaRegitro, idEstado, idMunicipio);
            vivienda = GenerarCuvGeografica(vivienda);

            return vivienda;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto">id del proyecto</param>
        /// <param name="viviendas">Viviendas correspondientes al proyecto</param>
        /// <param name="fechaRegitro">Fecha de registro del proyecto</param>
        /// <param name="idEstado">id del municipio de 2 posiciones</param>
        /// <param name="idMunicipio">id del municipio de 3 posiciones</param>
        /// <returns></returns>
        private async Task<List<Vivienda>> GenerarCuvTabular(int idProyecto, List<Vivienda> viviendas, DateTime fechaRegitro, string idEstado, string idMunicipio)
        {
            if (viviendas != null)
            {
                DatosCuvTabular datosCuvTabular = new DatosCuvTabular();

                int[] idsVivienda = new int[viviendas.Count];
                int x = 0;
                foreach (Vivienda vivienda in viviendas)
                {
                    idsVivienda[x] = vivienda.identificadorVivienda;
                    x++;
                }
                datosCuvTabular.fechaRegistro = fechaRegitro;
                datosCuvTabular.idEstado = idEstado;
                datosCuvTabular.idMunicipio = idMunicipio;
                datosCuvTabular.idProyecto = idProyecto;
                datosCuvTabular.idViviendas = idsVivienda;

                using (var client = new HttpClient())
                {
                    string BaseAddress = AppConfiguration.GetAppSetting<string>("UrlBaseConvivencia");

                    client.BaseAddress = new Uri(BaseAddress);
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    // Serialize our concrete class into a JSON String
                    var datosGeneracionCUV = await Task.Run(() => JsonConvert.SerializeObject(datosCuvTabular));

                    // Wrap our JSON inside a StringContent which then can be used by the HttpClient class
                    var httpContents = new StringContent(datosGeneracionCUV, Encoding.UTF8, "application/json");
                    string requestUri = AppConfiguration.GetAppSetting<string>("UrlAsIsGenerarCuvs");

                    HttpResponseMessage response = await client.PostAsync(requestUri, httpContents);
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonString = await response.Content.ReadAsStringAsync();

                        JavaScriptSerializer jss = new JavaScriptSerializer();
                        ResultadoGeneracionCUV resultado = jss.Deserialize<ResultadoGeneracionCUV>(jsonString);

                        if (resultado.model != null)
                        {
                            foreach (CuvTabular cuvTabular in resultado.model)
                            {
                                Vivienda viviendaCuv = viviendas.Find(y => y.identificadorVivienda == cuvTabular.idVivienda && y.idProyecto == cuvTabular.idProyecto);
                                viviendaCuv.cuv = cuvTabular.cuv;
                            }
                        }
                    }else
                    {
                        throw new Exception("Error al actualizar la cuv en AsIs");
                    }
                    client.Dispose();
                }

            }

            return viviendas;
        }
        /// <summary>
        /// Metodo para invocar el servicio de SIG para generar la CUV 
        /// </summary>
        /// <param name="viviendas">Viviendas pertenecientes al proyecto que se le generaran las CUVs</param>
        /// <returns>Regresa el listado de viviendas con las CUVs generadas</returns>
        private List<Vivienda> GenerarCuvGeografica(List<Vivienda> viviendas)
        {
            string endPointUrl = AppConfiguration.GetAppSetting<string>("UrlSigCuvGeografica");

            BasicHttpBinding binding = new BasicHttpBinding();
            EndpointAddress endpointAddress = new EndpointAddress(endPointUrl);

            foreach (Vivienda vivienda in viviendas)
            {
                Cuv cuv = new Cuv();
                cuv.Esquema = AppConfiguration.GetAppSetting<string>("CuvGeografica.Esquema");
                cuv.GeometriaNombreCampo = AppConfiguration.GetAppSetting<string>("CuvGeografica.GeometriaNombreCampo");
                cuv.IdNombreCampo = AppConfiguration.GetAppSetting<string>("CuvGeografica.IdNombreCampo");
                cuv.IdServicio = AppConfiguration.GetAppSetting<int>("CuvGeografica.IdServicio");
                //Se resta 4 debido a que el nivel que maneja el SIG respecto al catalogo de la BD de RUV++ tiene esa diferencia
                cuv.NivelDecimal = vivienda.idNivelVivienda - 4;
                cuv.NombreTabla = AppConfiguration.GetAppSetting<string>("CuvGeografica.NombreTabla");
                cuv.TipoGeometria = AppConfiguration.GetAppSetting<int>("CuvGeografica.TipoGeometria");
                cuv.IdValorCampo = vivienda.featID;
                cuv.Tipologia = vivienda.tipologia;
                ServicioCUV.ServicioCUVClient servicioClient = new ServicioCUV.ServicioCUVClient(binding, endpointAddress);
                ResultadoCuv resultadoCUV = servicioClient.CrearCuv(cuv);
                if (resultadoCUV.Clave == null || resultadoCUV.Clave.Equals(""))
                {
                    Consulta consulta = new Consulta();
                    consulta.Esquema = AppConfiguration.GetAppSetting<string>("CuvGeografica.Esquema");
                    consulta.IdNombreCampo = AppConfiguration.GetAppSetting<string>("CuvGeografica.IdNombreCampo");
                    consulta.NivelDecimal = vivienda.idNivelVivienda - 4;
                    consulta.NombreTabla = AppConfiguration.GetAppSetting<string>("CuvGeografica.NombreTabla");
                    consulta.IdValorCampo = vivienda.featID.ToString();
                    ConsultaBitacora consultaBitacora = servicioClient.ConsultaCuv(consulta);
                    vivienda.cuvGeografica = consultaBitacora.Cuv;
                }
                else
                {
                    vivienda.cuvGeografica = resultadoCUV.Clave;
                }
                if ( string.IsNullOrEmpty(vivienda.cuvGeografica))
                {
                    throw new Exception(String.Format("Error en el featId: {0} del proyecto: {1}, Detalle: {2}", vivienda.featID, vivienda.idProyecto, resultadoCUV.Descripcion));
                }

            }
            return viviendas;
        }
    }
}