<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.Comun.Datos</name>
    </assembly>
    <members>
        <member name="M:RUV.Comun.Datos.DatabaseFunctionProxy.Execute_sp``1(System.String,System.String,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Permite ejecutar un procedimiento almacenado y devolver una coleccion de objetos tipificados.
            </summary>
            <typeparam name="T">Tipo generic</typeparam>
            <param name="function">Nombre del procedimiento almacenado en la base de datos.</param>
            <param name="objs">Parametros que espera el procedimiendo almacenado.</param>
            <returns>Coleccion de objetos tipificados.</returns>
        </member>
        <member name="M:RUV.Comun.Datos.DatabaseFunctionProxy.Execute_spOut``1(System.String,System.String,System.String,System.Object@,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Permite ejecutar un procedimiento almacenado y devolver una coleccion de objetos tipificados.
            </summary>
            <typeparam name="T">Tipo generic</typeparam>
            <param name="function">Nombre del procedimiento almacenado en la base de datos.</param>
            <param name="objs">Parametros que espera el procedimiendo almacenado.</param>
            <returns>Coleccion de objetos tipificados.</returns>
        </member>
        <member name="M:RUV.Comun.Datos.DatabaseFunctionProxy.ExecuteQueryFirstRow``1(System.String,System.String,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Permite ejecutar un procedimiento almacenado y devolver una coleccion de objetos tipificados.
            </summary>
            <typeparam name="T">Tipo generic</typeparam>
            <param name="connectionName"></param>
            <param name="function">Nombre del procedimiento almacenado en la base de datos.</param>
            <param name="objs">Parametros que espera el procedimiendo almacenado.</param>
            <returns>Coleccion de objetos tipificados.</returns>
        </member>
        <member name="M:RUV.Comun.Datos.DatabaseFunctionProxy.Execute_spToDataReader(System.Data.SqlClient.SqlConnection,System.Data.CommandType,System.String,System.Collections.Generic.KeyValuePair{System.String,System.Object}[])">
            <summary>
            Permite ejecutar un stored procedure y devolver un DataSet.
            </summary>
            <param name="Database">Contexto de base de datos.</param>
            <param name="function">Nombre del procedimiento almacenado en la base de datos.</param>
            <param name="objs">Parametros que espera el procedimiendo almacenado.</param>
            <returns>DataSet con el set de resultados.</returns>
        </member>
        <member name="M:RUV.Comun.Datos.DatabaseFunctionProxy.AddParametersToCommand(System.Data.SqlClient.SqlCommand,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.Object}})">
            <summary>
             Agrega parametros a un comando tipo <see cref="T:System.Data.SqlClient.SqlCommand"/>
            </summary>
            <param name="dbCommand">Comando al que se agregaran los parametros</param>
            <param name="objs">Enumerable que contiene los parametros a agregar</param>
        </member>
        <member name="T:RUV.Comun.Datos.DbFunctionAttribute">
            <summary>
            Atributo que persisitra el nombre del objeto de base de datos a ejecutar
            </summary>
        </member>
        <member name="T:RUV.Comun.Datos.DbColumnAttribute">
            <summary>
            Atributo que se utilizara para obtener el valor desde un datareader
            Behavior se utiliza para saber si la propiedad se mapeara o no
            NameColumn nombre de columna que se buscara en el datareader
            </summary>
        </member>
        <member name="T:RUV.Comun.Datos.ColumndBehaviorType">
            <summary>
            Se utiliza para saber si la propiedad se mapeara o no
            </summary>
        </member>
        <member name="T:RUV.Comun.Datos.Extensions.SqlTransientExtensions">
            <summary>
            Contiene extensiones para manejo de operaciones de SQL Server con resistencia a fallos y reintentos.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Extensions.SqlTransientExtensions.SqlRetryManager">
            <summary>
            Gestor de reintentos.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Extensions.SqlTransientExtensions.SqlCommandRetryPolicy">
            <summary>
            Política de reintento para comandos de SQL.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Extensions.SqlTransientExtensions.SqlConnectionRetryPolicy">
            <summary>
            Política de reintento para conexiones de SQL.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Extensions.SqlTransientExtensions.OpenWithRetry(System.Data.SqlClient.SqlConnection)">
            <summary>
            Abre una conexión utilizando la politica de reintentos.
            </summary>
            <param name="cnn">Conexión a abrir.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Extensions.SqlTransientExtensions.ExecuteWithRetry(System.Action)">
            <summary>
            Executa una acción de SQL en el contexto del gestor de reintentos.
            </summary>
            <param name="body">Accion a ejecutar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Extensions.SqlTransientExtensions.ExecuteWithRetry``1(System.Func{``0})">
            <summary>
            Executa una acción de SQL en el contexto del gestor de reintentos.
            </summary>
            <typeparam name="R">Tipo del resultado.</typeparam>
            <param name="cnn">Conexión a abrir.</param>
            <returns>Resultado de la ejecución.</returns>
        </member>
        <member name="M:RUV.Comun.Datos.Extensions.SqlTransientExtensions.ExecuteWithRetryAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Executa una acción de SQL en el contexto del gestor de reintentos asincronamente.
            </summary>
            <param name="body">Accion a ejecutar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Extensions.SqlTransientExtensions.ExecuteWithRetryAsync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Executa una acción de SQL en el contexto del gestor de reintentos asincronamente.
            </summary>
            <typeparam name="R">Tipo del resultado.</typeparam>
            <param name="cnn">Conexión a abrir.</param>
            <returns>Resultado de la ejecución.</returns>
        </member>
        <member name="T:RUV.Comun.Datos.Mapper.DataMapperBase`1">
            <summary>
            Clase base de un Data Mapper
            </summary>
            <typeparam name="C">Tipo de la conexión.</typeparam>
        </member>
        <member name="F:RUV.Comun.Datos.Mapper.DataMapperBase`1._utilizaGestorConexiones">
            <summary>
            Indica si se esta utilizando el gestor de conexiones o no.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Mapper.DataMapperBase`1._conexion">
            <summary>
            Conexión a la base de datos.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Mapper.DataMapperBase`1._cadenaConexion">
            <summary>
            Nombre de la cadena de conexión.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Mapper.DataMapperBase`1._gestorConexiones">
            <summary>
            Gestor de conexiones a utilizar.
            </summary>
        </member>
        <member name="F:RUV.Comun.Datos.Mapper.DataMapperBase`1._clienteTelemetria">
            <summary>
            Cliente para comunicación con Applications Insights.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.DataMapperBase`1.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la clase, utilizando el gestor de conexiones.
            </summary>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a obtener de la configuración.</param>
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.DataMapperBase`1.#ctor(Microsoft.ApplicationInsights.TelemetryClient,System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la clase, utilizando el gestor de conexiones.
            </summary>
            <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a obtener de la configuración.</param>
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.DataMapperBase`1.CambiarCadenaDeConexion(System.String)">
            <summary>
            Cambia la cadena de conexión activa en el Mapper.
            </summary>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a obtener de la configuración.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.DataMapperBase`1.GenerarCadenadeDependencia">
            <summary>
            Genera la cadena de la dependencia a utilizarse en logeos de Application Insights
            </summary>
            <example>"servidor | BD | objeto"</example>
            <returns>Cadena de dependencia</returns>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.DataMapperBase`1.AbrirNuevaConexion(System.String)">
            <summary>
            Define la lógica para crea una nueva cadena de conexión.
            </summary>
            <param name="cadenaConexion">Cadena de conexión a utilizar.</param>
            <returns>Cadena de conexión creada.</returns>
        </member>
        <member name="T:RUV.Comun.Datos.Mapper.GestorConexiones">
            <summary>
            Se encarga de gestionar las conexión a la base de datos, para no crear multiuples conexiones en una sola petición.
            </summary>
            <remarks>La idea de esta clas es funcionar como un puntero inteligente de C++, pero enfocado a conexiones a BD y de manera global a cada petición.</remarks>
        </member>
        <member name="T:RUV.Comun.Datos.Mapper.GestorConexiones.ReferenciaConexionBd">
            <summary>
            Representa una conexión a BD y el conteo de sus referencias.
            </summary>
        </member>
        <member name="P:RUV.Comun.Datos.Mapper.GestorConexiones.ReferenciaConexionBd.Conexion">
            <summary>
            Conexión a BD.
            </summary>
        </member>
        <member name="P:RUV.Comun.Datos.Mapper.GestorConexiones.ReferenciaConexionBd.Referencias">
            <summary>
            Conteo de referencias a la conexión.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.GestorConexiones.ReferenciaConexionBd.#ctor(System.Data.IDbConnection)">
            <summary>
            Constructor de la clase
            </summary>
            <param name="conexion">Conexión de BD.</param>
        </member>
        <member name="F:RUV.Comun.Datos.Mapper.GestorConexiones.conexiones">
            <summary>
            Mapa de referencias a conexiones de BD por cadena de conexión.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.GestorConexiones.#ctor">
            <summary>
            Constructor de la clase.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.GestorConexiones.SolicitarConexion``1(System.String,System.Func{System.String,``0})">
            <summary>
            Obtiene o crea una nueva conexión de BD, con base en su cadena.
            </summary>
            <typeparam name="C">Tipo de la conexión.</typeparam>
            <param name="cadenaConexion">Cadena de conexión a utilizar.</param>
            <param name="creadorConexion">Función que crea la cadena de conexión especifica.</param>
            <returns>Conexión de BD.</returns>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.GestorConexiones.DevolverConexion(System.String)">
            <summary>
            Indica que la conexión ha sido dejada de usar.
            </summary>
            <param name="cadenaConexion">Cadena de conexión dejada de usar.</param>
        </member>
        <member name="T:RUV.Comun.Datos.Mapper.OracleDataMapperBase">
            <summary>
            Clase base para un Data Mapper que utiliza Oracle Database.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.OracleDataMapperBase.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la clase, utilizando el gestor de conexiones.
            </summary>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a utilizar.</param>        
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.OracleDataMapperBase.#ctor(Microsoft.ApplicationInsights.TelemetryClient,System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la clase, utilizando el gestor de conexiones.
            </summary> 
            <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a utilizar.</param>       
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.OracleDataMapperBase.AbrirNuevaConexion(System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.OracleDataMapperBase.GenerarCadenadeDependencia">
            <inheritdoc />
        </member>
        <member name="T:RUV.Comun.Datos.Mapper.SqlDataMapperBase">
            <summary>
            Clase base para un Data Mapper que utiliza SQL Server.
            </summary>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.SqlDataMapperBase.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la clase.
            </summary>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a utilizar.</param>        
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.SqlDataMapperBase.#ctor(Microsoft.ApplicationInsights.TelemetryClient,System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            Constructor de la clase.
            </summary>
            <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
            <param name="nombreCadenaConexion">Nombre de la cadena de conexión a utilizar.</param>        
            <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.SqlDataMapperBase.AbrirNuevaConexion(System.String)">
            <inheritdoc />
        </member>
        <member name="M:RUV.Comun.Datos.Mapper.SqlDataMapperBase.GenerarCadenadeDependencia">
            <inheritdoc />
        </member>
        <member name="M:RUV.Comun.Datos.DbDataReaderExtensions.GetNameColumn(System.Type,System.String)">
            <summary>
            Obtiene el valor de NameColumn del attributo DbColumn para una propiedad del tipo especificado
            Se utiliza para obtener el valor desde el DbDataReader, con el NameColumn obtenido
            </summary>
            <param name="type">Tipo donde se buscara la propiedad</param>
            <param name="propertyName">Propiedad</param>
            <returns>Nombre de la propiedad</returns>
        </member>
        <member name="M:RUV.Comun.Datos.DbDataReaderExtensions.GetDbColumnAttributte(System.Type,System.String)">
            <summary>
            Busca el atributo DBColumn del <see cref="T:System.Type"/> especificado
            </summary>
            <param name="type">Tipo de objeto donde se buscara el atributo</param>
            <param name="propertyName">Propiedad del tipo de dato a buscar</param>
            <returns>El atributo DBColumn</returns>
        </member>
        <member name="M:RUV.Comun.Datos.DbDataReaderExtensions.GetColumnBehavior(System.Type,System.String)">
            <summary>
            Obtiene el valor de behavior del attributo DbColumn para una propiedad del tipo especificado
            Se utiliza para saber si la propiedad se debera mapear o no
            </summary>
            <param name="type"></param>
            <param name="propertyName"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Datos.DbDataReaderExtensions.CanConvertTo(System.Object,System.Type)">
            <summary>
            Se utiliza para saber si un objeto se puede convertir a un tipo especificado
            </summary>
            <param name="value">objeto a convertir</param>
            <param name="propertyType">Tipo del objeto a convertir</param>
            <returns>true si el objeto se puede convertir, false si no se puede convertir</returns>
        </member>
    </members>
</doc>
