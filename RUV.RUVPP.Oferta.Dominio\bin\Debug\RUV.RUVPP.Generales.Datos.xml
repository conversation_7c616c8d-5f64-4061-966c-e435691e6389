<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Generales.Datos</name>
    </assembly>
    <members>
        <member name="T:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ObtenerTareasFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary>
            Obtiene la lista de tareas dependiento el parametro enviado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idModuloTarea"></param>
            <param name="idTipoTarea"></param>
            <param name="nombreTarea"></param>
            <param name="nombrePaquete"></param>
            <param name="mostrarPaquete"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.AgregarRelacionComercialXTarea(System.Int32,System.Int32)">
            <summary>
            Agrega la relacion entre relacion comercial y la tarea
            </summary>
            <param name="idRelacionComercial"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.AgregarIncidenciaXTarea(System.Int32,System.Int32)">
            <summary>
            Agrega la relacion entre incidencia y tarea
            </summary>
            <param name="idIncidencia"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.AgregarSancionXTarea(System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="idSancion"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ObtenerTareasXEmpresaFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idProducto"></param>
            <param name="idServicio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.AgregarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.AgregarPaqueteGarantiaXTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ActualizarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ActualizarPaqueteGarantiaXTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ObtenerCatalogoModuloTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ObtenerCatalogoTipoTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.IGeneralesDataMapper.ObtenerTareasPorIdEmpresa(System.Int32,System.Nullable{System.Boolean})">
            <summary>
            Método para obtener las tareas por idEmpresa
            </summary>
            <param name="idEmpresa">Identificador de la Empresa</param>
            <param name="tareasAtendidas">Indica si se recuperan las tareas atendias, no atendidas o ambas. true: solo tareas atendidas| false: solo tareas no atendidas | null: tareas atendidas y no atendidas</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.#ctor(System.String,RUV.Comun.Datos.Mapper.GestorConexiones)">
            <summary>
            
            </summary>
            <param name="nombreCadenaConexion"></param>
            <param name="gestorConexiones"></param>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.ObtenerTareasFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary>
            Obtiene la lista de tareas dependiento el parametro enviado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idModuloTarea"></param>
            <param name="idTipoTarea"></param>
            <param name="nombreTarea"></param>
            <param name="nombrePaquete"></param>
            <param name="mostrarPaquete"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.ObtenerTareasXEmpresaFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Obtiene las tareas por empresa por producto por servicio
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idProducto"></param>
            <param name="idServicio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.AgregarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.AgregarTareaDto(RUV.RUVPP.Generales.Modelo.Generales.Tarea)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.AgregarPaqueteGarantiaXTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.ActualizarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.ActualizarPaqueteGarantiaXTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.ObtenerCatalogoModuloTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.Generales.GeneralesDataMapper.ObtenerCatalogoTipoTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.ObtenerJSONConfiguracionReglasAsync(System.Int32,System.Int32)">
            <summary>
            Método para obtener los archivos de reglas y configuraciones
            desde la base de datos por medio del ID de servicio.
            </summary>
            <param name="idServicio"></param>
            /// <param name="conReglas">Parámetro que especifica si se traerá
            el contenido de las reglas, en caso contrario regresará NULL</param>
            <returns>Dupla de "string" conteniendo JSON de ambos archivos.
            (Configuraciones, Reglas)</returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de la etapa de registro o vyd por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">>Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.Implementacion.ProductoServicioDataMapper.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Generales.Datos.ProductoServicio.IProductoServicioDataMapper">
            <summary>
            Interface que incluye métodos relacionados con los archivos
            JSON de configuraciones y reglas.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.IProductoServicioDataMapper.ObtenerJSONConfiguracionReglasAsync(System.Int32,System.Int32)">
            <summary>
            Método para obtener los archivos de reglas y configuraciones
            desde la base de datos por medio del ID de servicio.
            </summary>
            <param name="idServicio"></param>
            /// <param name="conReglas">Parámetro que especifica si se traerá
            el contenido de las reglas, en caso contrario regresará NULL</param>
            <returns>Dupla de "string" conteniendo JSON de ambos archivos.
            (Configuraciones, Reglas)</returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.IProductoServicioDataMapper.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de la etapa de registro o vyd por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">>Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.IProductoServicioDataMapper.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus por registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.IProductoServicioDataMapper.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena que contiene el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Datos.ProductoServicio.IProductoServicioDataMapper.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
    </members>
</doc>
