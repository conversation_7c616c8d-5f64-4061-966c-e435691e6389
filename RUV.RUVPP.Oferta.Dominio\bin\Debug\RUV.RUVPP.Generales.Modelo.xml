<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Generales.Modelo</name>
    </assembly>
    <members>
        <member name="T:RUV.RUVPP.Generales.Modelo.Estatus.CuerpoEstatus">
            <summary>
            Representa el JSON de estatus de un formulario.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Estatus.CuerpoEstatus.EstatusJSON">
            <summary>
            JSON de estatus.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Estatus.ParametroBodyDictaminacion.DictaminacionJSON">
            <summary>
            JSON de dictaminacion
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Estatus.ParametroBodyDictaminacion.aceptacion">
            <summary>
            Indica si se dictamina con aceptación o rechazo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Estatus.ParametroBodyDictaminacion.idUsuario">
            <summary>
            Identificador del usuario que dictamina
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Estatus.ParametroBodyDictaminacion.seActualizaDictaminacion">
            <summary>
            Indica si se crea o se actualizan las dictaminaciones en BD
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.idElemento">
            Identificador del elemento (nombre) 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.estatus">
            Estatus de dictaminación del elemento, TRUE probado, FALSE rechazado, NULL no dictaminado o inconcluso 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.mensajeRechazo">
            Mensaje o comentario de rechazo
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.mensajeRechazoAnterior">
            Mensaje o comentario de rechazo anterior
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento._mostrarMensajeRechazo">
            Bandera que indica si se muestra el cuadro de comentario de rechazo 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento._mensajeRechazoTemporal">
            Texto temporal del mensaje de rechazo mostrato en el cuadro de texto
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.actualizadoPorUsuario">
            Bandera que indica si el elemento ha sido actualizado por el usuario
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento._actualizadoPorSeccion">
            Guarda el idSeccion si el campo fué dictaminado por un tab padre.
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento._mostrarMensajeRechazoAnterior">
            Bandera que indica si se muestra el popover de comentario de rechazo anterior 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.fechaDictaminacion">
            Fecha UTC en la que se dictaminó
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionElemento.fechaDictaminacionAnterior">
            Fecha UTC en que se realizó la dictaminación anterior 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.idSeccion">
            Identificador de la sección
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.estatus">
            Estatus de dictaminación de la sección, TRUE probado, FALSE rechazado, NULL no dictaminado o inconcluso 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.mensajeRechazo">
            Mensaje o comentario de rechazo guardado para el tab o sección
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.mensajeRechazoAnterior">
            Mensaje o comentario de rechazo anterior para el tab o sección
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion._mostrarMensajeRechazo">
            Bandera que indica si se muestra el cuadro de comentario de rechazo 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion._mensajeRechazoTemporal">
            Texto temporal del mensaje de rechazo mostrato en el cuadro de texto
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.actualizadoPorUsuario">
            Bandera que indica si algun elemento o seccion contenido en la seccion ha sido actualizado por el usuario
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion._actualizadoPorSeccion">
            Guarda el idSeccion si la sección fué dictaminada por un tab padre.
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.secciones">
            tabs o secciones hijas
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.elementos">
            Elementos del tab o sección
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion._mostrarMensajeRechazoAnterior">
            Bandera que indica si se muestra el popover de comentario de rechazo anterior 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.fechaDictaminacion">
            Fecha UTC en la que se dictaminó
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.DictaminacionSeccion.fechaDictaminacionAnterior">
            Fecha UTC en que se realizó la dictaminación anterior 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.consecutivo">
            Consecutivo del motivo de rechazo. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.secciones">
            Textop que representa el camino de secciones de donde se realizo el rechazo. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.elemento">
            idNombre del elemento donde se realizo el rechazo, si es que hubo uno. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.nombreElemento">
            Nombre del elemento donde se realizo el rechazo, si es que hubo uno. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.indice">
            Iindice del elemento donde se realizo el rechazo, cuando se trata de una lista. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.idSeccion">
            Id de la sección donde se genero el rechzo. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.estActualizado">
            Indica si esta actualizado o no el motivo de rechazo. 
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Dictaminacion.MotivoRechazo.estaActivo">
            Indica si el motivo de rechazo se encuentra en la sección seleccionada actual. 
        </member>
        <member name="T:RUV.RUVPP.Generales.Modelo.Documentos.DocumentoRuv">
            <summary>
            Representa una documento cargado en el sistema del RUV++.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Documentos.DocumentoRuv.IdDocumento">
            <summary>
            Id del documento.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Documentos.DocumentoRuv.NombreArchivo">
            <summary>
            Nombre del archivo.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Documentos.DocumentoRuv.UrlArchivo">
            <summary>
            URL para descarga el archivo.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Documentos.DocumentoRuv.IdCatalogoDocumento">
            <summary>
            Id catalogo documento.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Generales.Modelo.Documentos.DocumentoRuv.#ctor">
            <summary>
            Constructor de la clase
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Generales.Modelo.Generales.ModuloTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.ModuloTarea.IdModuloTarea">
            <summary>
            Id Empresa (Banco)
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.ModuloTarea.Descripcion">
            <summary>
            Razon social (Banco)
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.ModuloTarea.FechaRegistro">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.ModuloTarea.FechaActualizacion">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.ModuloTarea.Activo">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Generales.Modelo.Generales.Tarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.IdTarea">
             <summary>
            
             </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.IdEstatusTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.IdTipoTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.IdEmpresa">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.IdUsuarioCreaTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.IdUsuarioAtiendeTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.Descripcion">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.FechaAtiendaTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.FechaRegistro">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.FechaActualizacion">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.Tarea.Activo">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.IdPaqueteGarantia">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.clavePaqueteGarantia">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.IdModuloTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.IdServicio">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.RazonSocial">
            <summary>
            Razon social (Banco)
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.Modulo">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TareaBancos.NombrePaquete">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Generales.Modelo.Generales.TipoTarea">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TipoTarea.IdTipoTarea">
            <summary>
            Id Empresa (Banco)
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TipoTarea.Descripcion">
            <summary>
            Razon social (Banco)
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TipoTarea.FechaRegistro">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TipoTarea.FechaActualizacion">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Modelo.Generales.TipoTarea.Activo">
            <summary>
            
            </summary>
        </member>
    </members>
</doc>
