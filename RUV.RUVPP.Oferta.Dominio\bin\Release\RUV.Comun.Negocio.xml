<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.Comun.Negocio</name>
    </assembly>
    <members>
        <member name="M:RUV.Comun.Negocio.Auditing.LogExceptions.WriteToRegistry">
            <summary>
            Método para escribir un registro en el table storage EventRegistry.
            </summary>
            <returns>true si termino bien, false en caso de error</returns>
        </member>
        <member name="M:RUV.Comun.Negocio.Auditing.LogExceptions.RollbackRegistry">
            <summary>
            Método para hacer rollback a un registro en el table storage EventRegistry.
            </summary>
            <returns>true si termino bien, false en caso de error</returns>
        </member>
        <member name="M:RUV.Comun.Negocio.Auditing.LoggerHandler.LogDelegate(System.String,System.String,System.String,System.String)">
            <summary>
            Se usa como delegado para persistir el trace al logger
            </summary>
            <param name="message"></param>
            <param name="level"></param>
        </member>
        <member name="T:RUV.Comun.Negocio.Auditing.Registry.TipoTransaccion">
            <summary>
            Tipos de transacción que ejecuta el proceso.
            </summary>
        </member>
        <member name="P:RUV.Comun.Negocio.Auditing.Registry.timeStamp">
            <summary>
            Fecha y hora de registro de la transacción.
            </summary>
        </member>
        <member name="P:RUV.Comun.Negocio.Auditing.Registry.connectionString">
            <summary>
            Variable que almacena la cadena de conexión al storage account.
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.Auditing.Registry.WriteToRegistry">
            <summary>
            Método para escribir un registro en el table storage.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.Comun.Negocio.Auditing.Registry.RollbackRegistry">
            <summary>
            Método para hacer rollback a un registro en el table storage.
            </summary>
            <returns></returns>
        </member>
        <member name="T:RUV.Comun.Negocio.CustomExceptions.RuvBusinessException">
            <summary>
            Excepcion de tipo negocio
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.CustomExceptions.RuvBusinessException.#ctor(System.Exception,System.String,System.String,System.String,System.Int32)">
            <summary>
            Constructor estandar por defecto
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.CustomExceptions.RuvBusinessException.#ctor(RUV.Comun.Core.ExceptionHandling.SeverityLevel,RUV.Comun.Core.ExceptionHandling.LogLevel,System.Exception,System.String,System.String,System.String,System.Int32)">
            <summary>
            Constructor con parametros 
            </summary>
        </member>
        <member name="T:RUV.Comun.Negocio.CustomExceptions.RuvPlatformException">
            <summary>
                Exception tipo plataforma
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.CustomExceptions.RuvPlatformException.#ctor(System.Exception,System.String,System.String,System.String,System.Int32)">
            <summary>
                Constructor estandard por defecto
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.CustomExceptions.RuvPlatformException.#ctor(RUV.Comun.Core.ExceptionHandling.SeverityLevel,RUV.Comun.Core.ExceptionHandling.LogLevel,System.Exception,System.String,System.String,System.String,System.Int32)">
            <summary>
                Constructor con parametros
            </summary>
        </member>
        <member name="T:RUV.Comun.Negocio.CustomExceptions.RuvWebException">
            <summary>
                Excepcion RUV
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.CustomExceptions.RuvWebException.#ctor(System.Exception,System.String,RUV.Comun.Core.ExceptionHandling.NetworkInformation,System.String,System.String,System.Int32)">
            <summary>
                Constructor estandar por defecto
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.CustomExceptions.RuvWebException.#ctor(RUV.Comun.Core.ExceptionHandling.SeverityLevel,RUV.Comun.Core.ExceptionHandling.LogLevel,System.Exception,System.String,RUV.Comun.Core.ExceptionHandling.NetworkInformation,System.String,System.String,System.Int32)">
            <summary>
                Constructor con parametros
            </summary>
        </member>
        <member name="P:RUV.Comun.Negocio.CustomExceptions.RuvWebException.IdError">
            <summary>
            Id para rastrear el error.
            </summary>
        </member>
        <member name="T:RUV.Comun.Negocio.ServicioDominioBase">
            <summary>
            Clase base para un servicio de dominio.
            </summary>
        </member>
        <member name="F:RUV.Comun.Negocio.ServicioDominioBase._clienteTelemetria">
            <summary>
            Cliente para comunicación con Applications Insights
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.ServicioDominioBase.#ctor">
            <summary>
            Constructor de la clase
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.ServicioDominioBase.#ctor(Microsoft.ApplicationInsights.TelemetryClient)">
            <summary>
            Constructor de la clase
            </summary>
            <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
        </member>
        <member name="M:RUV.Comun.Negocio.ServicioDominioBase.DisposeManagedResources">
            <summary>
            Dispone de recursos administrados del servicio.
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.ServicioDominioBase.DisposeUnmanagedResources">
            <summary>
            Dispone de recursos no administrados del servicio.
            </summary>
        </member>
        <member name="T:RUV.Comun.Negocio.StateMachine.Core.State`2">
            <summary>
                Base class for a state
                implementations derive from it
            </summary>
        </member>
        <member name="M:RUV.Comun.Negocio.StateMachine.PassiveStateMachine`2.GetPreviousState">
            <exception cref="T:System.InvalidOperationException">Doesn't exist a previous state</exception>
        </member>
        <member name="M:RUV.Comun.Negocio.StateMachine.PassiveStateMachine`2.GetCurrentState">
            <exception cref="T:System.InvalidOperationException">state machine not initialized</exception>
        </member>
    </members>
</doc>
