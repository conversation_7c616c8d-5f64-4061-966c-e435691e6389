<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.Comun.Web</name>
    </assembly>
    <members>
        <member name="F:RUV.Comun.Web.ConversorPDF._disposed">
            <summary>
            especifica si se ha ejecutado dispose()
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.ConversorPDF.Dispose">
            <summary>
            Marca la clase para que sus recursos sean reciclados por el Garbage Collector.
            </summary>
        </member>
        <member name="T:RUV.Comun.Web.Extensions.HttpContextBaseExtension">
            <summary>
            Contiene metodos de extension para la clase de <see cref="T:System.Web.HttpContextBase"/>
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.Extensions.HttpContextBaseExtension.GetNetworkInformation(System.Web.HttpContextBase)">
            <summary>
            Permite obtener la informacion detallada de la peticion
            </summary>
            <param name="httpContext">
            The http context.
            </param>
            <returns>
            The <see cref="T:RUV.Comun.Core.ExceptionHandling.NetworkInformation"/>.
            </returns>
            <exception cref="T:System.NotImplementedException">Always.</exception>
        </member>
        <member name="T:RUV.Comun.Web.HttpContentExtensions">
            <summary>
            COntiene métodos extención para <see cref="T:System.Net.Http.HttpContent"/>
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.HttpContentExtensions.ParseMultipartAsync(System.Net.Http.HttpContent)">
            <summary>
            Interpreta el contenido multipart y lo estructura para un más facil acceso
            </summary>
            <param name="postedContent">Contenido el cual interpretar</param>
            <returns>Información envíada como multipart</returns>
        </member>
        <member name="T:RUV.Comun.Web.HttpPostedData">
            <summary>
            Representa información enviada en una petición como multipart
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedData.Fields">
            <summary>
            Obtiene los campos enviados en la petición
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedData.Files">
            <summary>
            Obtiene la lista de archivos enviados en la petición
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.HttpPostedData.#ctor(System.Collections.Generic.IDictionary{System.String,RUV.Comun.Web.HttpPostedField},System.Collections.Generic.IDictionary{System.String,RUV.Comun.Web.HttpPostedFile})">
            <summary>
            Constructor de la clase
            </summary>
            <param name="fields">Lista de campos</param>
            <param name="files">Lista de archivos</param>
        </member>
        <member name="T:RUV.Comun.Web.HttpPostedField">
            <summary>
            Representa un campo enviado como multipart
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedField.Name">
            <summary>
            Obtiene el nombre del campo
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedField.Value">
            <summary>
            Obtiene el valor del campo
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.HttpPostedField.#ctor(System.String,System.String)">
            <summary>
            Contructor de la clase
            </summary>
            <param name="name">Nombre del campo</param>
            <param name="value">Valor del campo</param>
        </member>
        <member name="T:RUV.Comun.Web.HttpPostedFile">
            <summary>
            Representa una rchivo enviado como multipart
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedFile.Name">
            <summary>
            Obtiene el nombre del archivo (en la petición)
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedFile.Filename">
            <summary>
            Obtiene el nombre del archivo (en el sistema de archivos)
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.HttpPostedFile.Data">
            <summary>
            Obtiene el contenido del archivo
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.HttpPostedFile.#ctor(System.String,System.String,System.Byte[])">
            <summary>
            Coontructor de la clase
            </summary>
            <param name="name">Nombre del archivo (en la petición)</param>
            <param name="filename">Nombre del archivo (en el sistema de archivos)</param>
            <param name="data">Contenido del archivo</param>
        </member>
        <member name="T:RUV.Comun.Web.Http.ApiControllerBase">
            <summary>
            Controller Api Base
            </summary>
        </member>
        <member name="F:RUV.Comun.Web.Http.ApiControllerBase.MensajeErrorModelo">
            <summary>
            Nombre del evento cuando se genera un error en la validación del modelo.
            </summary>
        </member>
        <member name="F:RUV.Comun.Web.Http.ApiControllerBase._clienteTelemetria">
            <summary>
            Cliente para comunicación con Applications Insights.
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.Http.ApiControllerBase.#ctor">
            <summary>
            Constructor de la clase
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.Http.ApiControllerBase.#ctor(Microsoft.ApplicationInsights.TelemetryClient)">
            <summary>
            Constructor de la clase
            </summary>
            <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
        </member>
        <member name="M:RUV.Comun.Web.Http.ApiControllerBase.TrasValidarModelo(System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}},System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
            <summary>
            Realiza la validación del modelo recibído y dependiendo de si es válido o no, ejecut una acción que devuelve una <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <remarks>De no ser válido el modelo, guarda la información en Applicaction Insights como un evento con el nombre "ErrorModelo".</remarks>
            <param name="accionCuerpo">Cuerpo a ejecutar cuando el modelo es válido.</param>
            <param name="accionModeloInvalido">Cuerpo a ejecutar si el cuerpo el inválido.</param>
            <returns>Respuesta de la acción.</returns>
        </member>
        <member name="T:RUV.Comun.Web.Http.FiltroExcepcionesApiAttribute">
            <summary>
            Filtro que actua ante las excepciones no controladas registrandolas en Application Insights y devolviendo un mensaje o, en desarrollo, el detalle de la excepción
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.Http.FiltroExcepcionesApiAttribute._telemetryClient">
            <summary>
            Cliente de telemetría para logear.
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.Http.FiltroExcepcionesApiAttribute.#ctor">
            <summary>
            Constructor de la clase.
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.Http.FiltroExcepcionesApiAttribute.OnExceptionAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
            <summary>
            Función que sobrecarga el metodo OnException  para el manejo de excepciones
            </summary>
            <param name="filterContext"></param>
        </member>
        <member name="M:RUV.Comun.Web.Http.FiltroExcepcionesApiAttribute.ConvertToRuvWebException(System.Web.Http.Filters.HttpActionExecutedContext)">
            <summary>
            Convierte la excepción en una de tipo <see cref="T:RUV.Comun.Negocio.CustomExceptions.RuvWebException"/>
            </summary>
            <param name="actionExecutedContext">Contexto de ejecución de la aplicación.</param>
            <returns>Excepción convertida.</returns>
        </member>
        <member name="M:RUV.Comun.Web.Http.FiltroExcepcionesApiAttribute.TrackException(RUV.Comun.Negocio.CustomExceptions.RuvWebException)">
            <summary>
            Logea la excepción en Applications Insight.
            </summary>
            <param name="exception">Excepcion a logear.</param>
        </member>
        <member name="M:RUV.Comun.Web.JavascriptSerializer.serializeToJson``1(``0)">
            <summary>
            Serializa un objeto a una cadena en formato json
            </summary>
            <typeparam name="T">Tipo del objeto</typeparam>
            <param name="type"></param>
            <returns>Regresa una cadena en formato json</returns>
        </member>
        <member name="M:RUV.Comun.Web.JavascriptSerializer.deserializeToObject``1(System.String)">
            <summary>
            Regresa una instancia de un tipo especificado
            </summary>
            <typeparam name="T">Tipo del objeto</typeparam>
            <param name="json">Cadena en formato json a transformar</param>
            <returns>Regresa una instancia del tipo especificado</returns>
        </member>
        <member name="T:RUV.Comun.Web.ManejoErrorMvcAttribute">
            <summary>
            Filtro que actua ante las excepciones no controladas registrandolas en Application Insights y devolviendo un mensaje o, en desarrollo, el detalle de la excepción
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.ManejoErrorMvcAttribute._telemetryClient">
            <summary>
            Cliente de telemetría para logear.
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.ManejoErrorMvcAttribute.#ctor">
            <summary>
            Constructor de la clase.
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.ManejoErrorMvcAttribute.OnException(System.Web.Mvc.ExceptionContext)">
            <summary>
            Función que sobrecarga el metodo OnException  para el manejo de excepciones
            </summary>
            <param name="filterContext"></param>
        </member>
        <member name="M:RUV.Comun.Web.ManejoErrorMvcAttribute.ConvertToRuvWebException(System.Web.Mvc.ExceptionContext)">
            <summary>
            Convierte la excepción en una de tipo <see cref="T:RUV.Comun.Negocio.CustomExceptions.RuvWebException"/>
            </summary>
            <param name="filterContext">Contexto de ejecución de la aplicación.</param>
            <returns>Excepción convertida.</returns>
        </member>
        <member name="M:RUV.Comun.Web.ManejoErrorMvcAttribute.TrackException(RUV.Comun.Negocio.CustomExceptions.RuvWebException)">
            <summary>
            Logea la excepción en Applications Insight.
            </summary>
            <param name="exception">Excepcion a logear.</param>
        </member>
        <member name="T:RUV.Comun.Web.MensajeExcepcionWeb">
            <summary>
            Mensaje de excepción a mostrar en una página web.
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.MensajeExcepcionWeb.#ctor(System.Exception,System.String,System.String)">
            <summary>
            Obtiene informacion de la excepcion junto con el controlador y la accion que lo originaron
            </summary>
            <param name="exception"></param>
            <param name="controllerName"></param>
            <param name="actionName"></param>
        </member>
        <member name="M:RUV.Comun.Web.MensajeExcepcionWeb.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.MensajeExcepcionWeb.#ctor(System.Exception)">
            <summary>
            Obtiene informacion de la excepcion
            </summary>
            <param name="exception"></param>
        </member>
        <member name="P:RUV.Comun.Web.MensajeExcepcionWeb.CodeException">
            <summary>
            Codigo de exception que debe de ser mostrado al usuario
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.MensajeExcepcionWeb.ControllerName">
            <summary>
            Nombre del controlador que origino la excepcion
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.MensajeExcepcionWeb.ActionName">
            <summary>
            Accion del controlador que origino la excepcion
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.MensajeExcepcionWeb.InternException">
            <summary>
            Contenido de la propiedad InnerExcepcion de la excepcion a controlar
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.MensajeExcepcionWeb.StackTrace">
            <summary>
            Contenido de la propiedad StackTrace de la excepcion a controlar
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.MensajeExcepcionWeb.Description">
            <summary>
            Contenido de la propiedad Message de la excepcion a controlar
            </summary>
            
        </member>
        <member name="M:RUV.Comun.Web.MensajeExcepcionWeb.DecodeMensaje(System.Text.Encoding)">
            <summary>
            Decodifica el las propiedades de la excepcion
            </summary>
            <param name="encoding"></param>
        </member>
        <member name="F:RUV.Comun.Web.PdfConverter._disposed">
            <summary>
            especifica si se ha ejecutado dispose()
            </summary>
        </member>
        <member name="M:RUV.Comun.Web.PdfConverter.Dispose">
            <summary>
            Marca la clase para que sus recursos sean reciclados por el Garbage Collector.
            </summary>
        </member>
        <member name="T:RUV.Comun.Web.Resources.Mensajes">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.Resources.Mensajes.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.Resources.Mensajes.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.Resources.Mensajes.ExcepcionNoControlada">
            <summary>
              Looks up a localized string similar to Ocurrió un error al procesar tu solicitud, inténtalo nuevamente y si continúa presentándose el error comunícate con nosotros y proporciónanos el siguiente Id:    {0}.
            </summary>
        </member>
        <member name="P:RUV.Comun.Web.Resources.Mensajes.ModeloInvalido">
            <summary>
              Looks up a localized string similar to Los parámetros enviados son inválidos, para más información comunícate con nosotros y proporciónanos el siguiente Id:    {0}.
            </summary>
        </member>
        <member name="T:RUV.Comun.Web.Filters.IdentityBasicAuthenticationAttribute">
            <summary>
            Filtro de autenticación básica para Web API.
            </summary>
            <remarks>
            Es necesario definirn los parametros "RUV.RUVPP.Seguridad.Basica.Usuario" y "RUV.RUVPP.Seguridad.Basica.Contraseña" para que pueda funcionar correctamente.
            </remarks>
        </member>
    </members>
</doc>
