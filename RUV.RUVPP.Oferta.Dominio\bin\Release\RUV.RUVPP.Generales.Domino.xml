<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Generales.Domino</name>
    </assembly>
    <members>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.Implementacion.ServicioGenerales.AgregarTareaDto(RUV.RUVPP.Generales.Modelo.Generales.Tarea)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.Implementacion.ServicioGenerales.AgregarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.Implementacion.ServicioGenerales.ActualizarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.Implementacion.ServicioGenerales.ObtenerTareasFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary>
            Obtiene la lista de tareas dependiento el parametro enviado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idModuloTarea"></param>
            <param name="idTipoTarea"></param>
            <param name="nombreTarea"></param>
            <param name="nombrePaquete"></param>
            <param name="mostrarPaquete"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.Implementacion.ServicioGenerales.ObtenerCatalogoModuloTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.Implementacion.ServicioGenerales.ObtenerCatalogoTipoTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.AgregarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.ActualizarTarea(RUV.RUVPP.Generales.Modelo.Generales.TareaBancos)">
            <summary>
            
            </summary>
            <param name="tarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.ObtenerTareasFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary>
            Obtiene la lista de tareas dependiento el parametro enviado
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idModuloTarea"></param>
            <param name="idTipoTarea"></param>
            <param name="nombreTarea"></param>
            <param name="nombrePaquete"></param>
            <param name="mostrarPaquete"></param>
            <param name="idTarea"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.ObtenerTareasXEmpresaFiltro(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            
            </summary>
            <param name="tamanioPagina"></param>
            <param name="pagina"></param>
            <param name="idEmpresa"></param>
            <param name="idProducto"></param>
            <param name="idServicio"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.ObtenerCatalogoModuloTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.ObtenerCatalogoTipoTarea">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.Generales.IServicioGenerales.ObtenerTareasPorIdEmpresa(System.Int32,System.Nullable{System.Boolean})">
            <summary>
            Método para obtener las tareas por idEmpresa
            </summary>
            <param name="idEmpresa">Identificador de la Empresa</param>
            <param name="tareasAtendidas">Indica si se recuperan las tareas atendias, no atendidas o ambas. true: solo tareas atendidas| false: solo tareas no atendidas | null: tareas atendidas y no atendidas</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ObtenerConfiguracionAsync(System.Int32)">
            <summary>
            Obtiene el JSON de configuracion como objeto de un servicio especificado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ObtenerConfiguracionReglasAsync(System.Int32)">
            <summary>
            Obtiene una tupla que contiene el JSON de configuracion y de reglas como objeto y arreglo de un servicio determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de registro/vyd como objeto para un registro determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador de registro</param>
            <param name="jsonEstatus">Cadena conteniendo elJSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena conteniendo el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.Implementacion.ServicioProductoServicio.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.IServicioProductoServicio.ObtenerConfiguracionAsync(System.Int32)">
            <summary>
            Obtiene el JSON de configuracion como objeto de un servicio especificado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.IServicioProductoServicio.ObtenerConfiguracionReglasAsync(System.Int32)">
            <summary>
            Obtiene una tupla que contiene el JSON de configuracion y de reglas como objeto y arreglo de un servicio determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.IServicioProductoServicio.ActualizarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Actualiza el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="jsonEstatus">Cadena conteniendo el JSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.IServicioProductoServicio.ObtenerJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Obtiene el JSON de estatus de registro/vyd como objeto para un registro determinado
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>
            <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.IServicioProductoServicio.GuardarJsonEstatusAsync(System.Int16,System.Int32,System.String,System.Boolean,System.Boolean,System.Nullable{System.Boolean})">
            <summary>
            Guarda el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador de registro</param>
            <param name="jsonEstatus">Cadena conteniendo elJSON de estatus</param>
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Dominio.ProductoServicio.IServicioProductoServicio.EliminarJsonEstatusAsync(System.Int16,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Elimina el JSON de estatus de registro/vyd de un registro
            </summary>
            <param name="idServicio">Identificador del servicio</param>
            <param name="idRegistro">Identificador del registro</param>        
            <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
            <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
            <param name="esFinal">Bandera que indica si es el JSON final</param>
            <returns></returns>
        </member>
    </members>
</doc>
