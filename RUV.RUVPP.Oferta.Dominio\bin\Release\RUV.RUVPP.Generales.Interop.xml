<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Generales.Interop</name>
    </assembly>
    <members>
        <member name="P:RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto.archivoStream">
            <summary>
            Contenedor datos de  archivo 
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto.contenedorBlob">
            <summary>
             Nombre del contendor en repositorio Blob
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto.carpeta">
            <summary>
            Carpeta donde almacenar el archivo.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto.rfcEmpresa">
            <summary>
            RFC de la empresa dueña del documento.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Generales.Interop.Documentos.ServicioDocumentoOferta.AgregarYObtener(RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto)">
            <inheritdoc/>
        </member>
        <member name="M:RUV.RUVPP.Generales.Interop.Documentos.ServicioDocumentoOferta.CopiarYObtener(RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto,RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto)">
            <inheritdoc/>
        </member>
        <member name="M:RUV.RUVPP.Generales.Interop.Documentos.ServicioDocumentoOferta.MoverYObtener(RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto,RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto)">
            <inheritdoc/>
        </member>
        <member name="M:RUV.RUVPP.Generales.Interop.Documentos.ServicioDocumentoOferta.ValidarDocumento(System.String,System.String)">
             <summary>
            Verifica que el nombre del archivo y el nombre del contenedor de un documento contengan caracteres validos
             </summary>
             <param name="nombreArchivo">Nombre del documento</param>
             <param name="nombreContenedor">Nombre del contenedor</param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Interop.Documentos.ServicioDocumentoOferta.Eliminar(RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto,System.Boolean)">
            <summary>
            Elimina el documento en EntornoDocumento, en Comun Documento y opcionalmente en el Blob
            </summary>
            <param name="elemento">documento a eliminar</param>
            <param name="esEliminadoElBlob">true si se elimina tambien del blob</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Generales.Interop.Documentos.ServicioDocumentoOferta.Eliminar(RUV.RUVPP.Generales.Interop.Documentos.DocumentoRuvDto)">
            <summary>
            Elimina el documento
            </summary>
            <param name="elemento">Datos del documento</param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.DocumentoNombreContenedorInvalido">
            <summary>
              Looks up a localized string similar to nombreContenedor solo permite alfanumericos en minúsculas: {0}.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.DocumentoNombreContenedorMinMaxLength">
            <summary>
              Looks up a localized string similar to El nombre del contenedor debe tener entre 3 y 63 caracteres.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.DocumentoNombreInvalido">
            <summary>
              Looks up a localized string similar to El nombre del archivo solo puede contener alfanuméricos, paréntesis, espacios, guión alto y guión bajo: {0}.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.DocumentoNombreMinMaxLength">
            <summary>
              Looks up a localized string similar to El nombre del contenedor debe tener entre 1 y 1,024 caracteres.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Generales.Interop.Recursos.RecursosDocumentos.DocumentoNombrePatron">
            <summary>
              Looks up a localized string similar to ^[A-Za-z0-9()\-_\s\.]*$.
            </summary>
        </member>
    </members>
</doc>
