﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </configSections>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ApplicationInsights" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <publisherPolicy apply="no" />
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
        <bindingRedirect oldVersion="4.121.0.0 - 4.65535.65535.65535" newVersion="*********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AI.Agent.Intercept" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.7.0" newVersion="2.0.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.SqlServer.Types" publicKeyToken="89845dcd8080cc91" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <appSettings>
    
    <!--Web API-->
    <add key="RUV.RUVPP.UI.Usuarios.Suscripcion.AgregarOrdenTablero" value="http://ruvruppdevwebapi.cloudapp.net/api/Tableros/AgregarOrden" />
    
  </appSettings>
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" />
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </DbProviderFactories>
  </system.data>
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources>
        <dataSource alias="SampleDataSource" descriptor="(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL))) " />
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IServicioCUV" />
        <binding name="ServiceSoap" />
        <binding name="SI_recibeFoto_SOBinding">
        <security mode="Transport">
                <transport clientCredentialType="Basic" proxyCredentialType="None"></transport>
              </security>
        </binding>
        <binding name="SI_recibeFoto_SOBinding1">
          <security mode="Transport" />
        </binding>
        <binding name="SI_recibeFoto_SOBinding2" />
      </basicHttpBinding>
      <customBinding>
        <binding name="ServiceSoap12">
          <textMessageEncoding messageVersion="Soap12" />
          <httpTransport />
        </binding>
      </customBinding>
    </bindings>
    <client>
      <endpoint address="http://map-q3-w2k8r2.cloudapp.net/ws/cuv/ServicioCUV.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IServicioCUV" contract="ServicioCUV.IServicioCUV" name="BasicHttpBinding_IServicioCUV" />
      <endpoint address="http://sap-dev02-sd/WSOrdenPago/service.asmx" binding="basicHttpBinding" bindingConfiguration="ServiceSoap" contract="ServiceClientSAP.ServiceSoap" name="ServiceSoap" />
      <endpoint address="http://sap-dev02-sd/WSOrdenPago/service.asmx" binding="customBinding" bindingConfiguration="ServiceSoap12" contract="ServiceClientSAP.ServiceSoap" name="ServiceSoap12" />
      <endpoint address="https://serviciosweb.infonavit.org.mx:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA" binding="basicHttpBinding" bindingConfiguration="SI_recibeFoto_SOBinding" contract="Servlet_recibeFoto.SI_recibeFoto_SO" name="HTTP_Port" />
      <!--<endpoint address="https://************:8997/XISOAPAdapter/MessageServlet_recibeFotoSec1_QA"
        binding="basicHttpBinding" bindingConfiguration="SI_recibeFoto_SOBinding1"
        contract="Servlet_recibeFoto.SI_recibeFoto_SO" name="HTTPS_Port" />-->
    </client>
  </system.serviceModel>
</configuration>