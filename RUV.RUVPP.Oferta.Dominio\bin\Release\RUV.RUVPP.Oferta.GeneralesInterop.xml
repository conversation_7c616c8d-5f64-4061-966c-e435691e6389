<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Oferta.GeneralesInterop</name>
    </assembly>
    <members>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta.archivoStream">
            <summary>
            Contenedor datos de  archivo 
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta.contenedorBlob">
            <summary>
             Nombre del contendor en repositorio Blob
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta.carpeta">
            <summary>
            Carpeta donde almacenar el archivo.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta.rfcEmpresa">
            <summary>
            RFC de la empresa dueña del documento.
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.DocumentoNombreContenedorInvalido">
            <summary>
              Looks up a localized string similar to nombreContenedor solo permite alfanumericos en minúsculas: {0}.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.DocumentoNombreContenedorMinMaxLength">
            <summary>
              Looks up a localized string similar to El nombre del contenedor debe tener entre 3 y 63 caracteres.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.DocumentoNombreInvalido">
            <summary>
              Looks up a localized string similar to El nombre del archivo solo puede contener alfanuméricos, paréntesis, espacios, guión alto y guión bajo: {0}.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.DocumentoNombreMinMaxLength">
            <summary>
              Looks up a localized string similar to El nombre del contenedor debe tener entre 1 y 1,024 caracteres.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos.DocumentoNombrePatron">
            <summary>
              Looks up a localized string similar to ^[A-Za-z0-9()\-_\s\.]*$.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.ServicioDocumentoOferta.AgregarYObtener(RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta)">
            <inheritdoc/>
        </member>
        <member name="M:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.ServicioDocumentoOferta.CopiarYObtener(RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta,RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta)">
            <inheritdoc/>
        </member>
        <member name="M:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.ServicioDocumentoOferta.MoverYObtener(RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta,RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta)">
            <inheritdoc/>
        </member>
        <member name="M:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.ServicioDocumentoOferta.ValidarDocumento(System.String,System.String)">
             <summary>
            Verifica que el nombre del archivo y el nombre del contenedor de un documento contengan caracteres validos
             </summary>
             <param name="nombreArchivo">Nombre del documento</param>
             <param name="nombreContenedor">Nombre del contenedor</param>
             <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.ServicioDocumentoOferta.Eliminar(RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta,System.Boolean)">
            <summary>
            Elimina el documento en EntornoDocumento, en Comun Documento y opcionalmente en el Blob
            </summary>
            <param name="elemento">documento a eliminar</param>
            <param name="esEliminadoElBlob">true si se elimina tambien del blob</param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.GeneralesInterop.Documentos.ServicioDocumentoOferta.Eliminar(RUV.RUVPP.Oferta.GeneralesInterop.Documentos.DocumentoOferta)">
            <summary>
            Elimina el documento
            </summary>
            <param name="elemento">Datos del documento</param>
            <returns></returns>
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.idElemento">
            Identificador del elemento (nombre) 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.estatus">
            Estatus de dictaminación del elemento, TRUE probado, FALSE rechazado, NULL no dictaminado o inconcluso 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.mensajeRechazo">
            Mensaje o comentario de rechazo
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.mensajeRechazoAnterior">
            Mensaje o comentario de rechazo anterior
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento._mostrarMensajeRechazo">
            Bandera que indica si se muestra el cuadro de comentario de rechazo 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento._mensajeRechazoTemporal">
            Texto temporal del mensaje de rechazo mostrato en el cuadro de texto
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.actualizadoPorUsuario">
            Bandera que indica si el elemento ha sido actualizado por el usuario
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento._actualizadoPorSeccion">
            Guarda el idSeccion si el campo fué dictaminado por un tab padre.
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento._mostrarMensajeRechazoAnterior">
            Bandera que indica si se muestra el popover de comentario de rechazo anterior 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.fechaDictaminacion">
            Fecha UTC en la que se dictaminó
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionElemento.fechaDictaminacionAnterior">
            Fecha UTC en que se realizó la dictaminación anterior 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.idSeccion">
            Identificador de la sección
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.estatus">
            Estatus de dictaminación de la sección, TRUE probado, FALSE rechazado, NULL no dictaminado o inconcluso 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.mensajeRechazo">
            Mensaje o comentario de rechazo guardado para el tab o sección
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.mensajeRechazoAnterior">
            Mensaje o comentario de rechazo anterior para el tab o sección
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion._mostrarMensajeRechazo">
            Bandera que indica si se muestra el cuadro de comentario de rechazo 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion._mensajeRechazoTemporal">
            Texto temporal del mensaje de rechazo mostrato en el cuadro de texto
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.actualizadoPorUsuario">
            Bandera que indica si algun elemento o seccion contenido en la seccion ha sido actualizado por el usuario
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion._actualizadoPorSeccion">
            Guarda el idSeccion si la sección fué dictaminada por un tab padre.
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.secciones">
            tabs o secciones hijas
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.elementos">
            Elementos del tab o sección
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion._mostrarMensajeRechazoAnterior">
            Bandera que indica si se muestra el popover de comentario de rechazo anterior 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.fechaDictaminacion">
            Fecha UTC en la que se dictaminó
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.DictaminacionSeccion.fechaDictaminacionAnterior">
            Fecha UTC en que se realizó la dictaminación anterior 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.consecutivo">
            Consecutivo del motivo de rechazo. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.secciones">
            Textop que representa el camino de secciones de donde se realizo el rechazo. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.elemento">
            idNombre del elemento donde se realizo el rechazo, si es que hubo uno. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.nombreElemento">
            Nombre del elemento donde se realizo el rechazo, si es que hubo uno. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.indice">
            Iindice del elemento donde se realizo el rechazo, cuando se trata de una lista. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.idSeccion">
            Id de la sección donde se genero el rechzo. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.estActualizado">
            Indica si esta actualizado o no el motivo de rechazo. 
        </member>
        <member name="P:RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion.MotivoRechazo.estaActivo">
            Indica si el motivo de rechazo se encuentra en la sección seleccionada actual. 
        </member>
    </members>
</doc>
