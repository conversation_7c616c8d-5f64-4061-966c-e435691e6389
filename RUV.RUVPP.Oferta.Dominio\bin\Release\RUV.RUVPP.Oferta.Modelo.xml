<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RUV.RUVPP.Oferta.Modelo</name>
    </assembly>
    <members>
        <member name="T:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO">
            <summary>
            Objeto que contiene las propiedades necesarias para ejecutar el servicio de Cambio Estatus Vivienda.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO.#ctor(System.String,System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Constructor de la clase CancelacionCuvDTO.
            </summary>
            <param name="cuv">Clave Única de Vivienda que se quiere cancelar.</param>
            <param name="idUsuario">Identificador del usuario que ejecuta el servicio.</param>
            <param name="idEstatusVivienda">Identificador del Estatus al que se desea cambiar.</param>
            <param name="urlDocumento">Dirección en el blob del documento asociado.</param>
            <param name="fechaRegistro">Fecha en que se ejecuta el acción.</param>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO.cuv">
            <summary>
            Clave Única de Vivienda de 16 dígitos.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO.idUsuario">
            <summary>
            Usuario que ejecuta el servicio.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO.idEstatusVivienda">
            <summary>
            Identificador del Estatus al que se desea cambiar.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO.urlDocumento">
            <summary>
            Dirección en el blob del documento asociado.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CambioEstatusViviendaDTO.fechaRegistro">
            <summary>
            Fecha en que se ejecuta el acción.
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CancelacionCuvDTO">
            <summary>
            Objeto que contiene las propiedades necesarias para ejecutar el servicio de cancelación.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CancelacionCuvDTO.#ctor(System.String,System.String)">
            <summary>
            Constructor de la clase CancelacionCuvDTO.
            </summary>
            <param name="cuv">Clave Única de Vivienda que se quiere cancelar.</param>
            <param name="usuario">Usuario que ejecuta el servicio.</param>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CancelacionCuvDTO.cuv">
            <summary>
            Clave Única de Vivienda de 16 dígitos.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.AgenteServicio.CancelacionCuvDTO.usuario">
            <summary>
            Usuario que ejecuta el servicio.
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.DateTimeMX.Now(System.String)">
            <summary>
            Obtiene la fecha y hora actual en la CDMX
            </summary>
            <param name="formato"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Dictaminacion.CuerpoEstatus">
            <summary>
            Representa el JSON de estatus de un formulario.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Dictaminacion.CuerpoEstatus.EstatusJSON">
            <summary>
            JSON de estatus.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion.DictaminacionJSON">
            <summary>
            JSON de dictaminacion
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion.aceptacion">
            <summary>
            Indica si se dictamina con aceptación o rechazo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion.idUsuario">
            <summary>
            Identificador del usuario que dictamina
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Dictaminacion.ParametroBodyDictaminacion.seActualizaDictaminacion">
            <summary>
            Indica si se crea o se actualizan las dictaminaciones en BD
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.Historico.Data.DetalleEstatusVivienda.#ctor(System.Int32,RUV.RUVPP.Oferta.Modelo.Historico.Data.EnumCampoAdicional,System.String)">
            <summary>
            Construye un objeto de la clase DetalleEstatusVivienda.
            </summary>
            <param name="idCampoAdicional">Identificador del campo adicional.</param>
            <param name="nombreDelCampo">Nombre del campo.</param>
            <param name="descripcion">Descripción del campo.</param>
            <param name="tipoDato">Tipo del dato del campo.</param>
            <param name="activo">Si está activo.</param>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Historico.Data.EnumCampoAdicional">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoCUV">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoOferta">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoOV">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Historico.Data.EventoProyecto">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion">
            <summary>
            contenedor de resultado de consulta de historico de validaciones (lista de ordenes de trabajo de una oferta/proyecto)
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.Validador">
            <summary>
            Nombre del validador
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.claveODT">
            <summary>
            Clave de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.FechaInicio">
            <summary>
            Fecha de registro de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.FechaValidacion">
            <summary>
            Fecha de inicio de atencion de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.FechaFinAtencion">
            <summary>
            Fecha de termino de atencion de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.IdEstatus">
            <summary>
            Identificador del estatus de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.NombreEstatus">
            <summary>
            Nombre del estatus de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.IdOrdenTrabajo">
            <summary>
            Identificador de la orden de trabajo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.IdOrdenServicio">
            <summary>
            Identificador de la orden de servicio
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Historico.Data.OrdenTrabajoHistoricoValidacion.IdValidador">
            <summary>
            Identificador del usuario validador
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv">
            <summary>
            Clase que modela la informacion de consulta de una CUV
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.cuv">
            <summary>
            CUV
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.idProyecto">
            <summary>
            Identificador del proyecto
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.nombreProyecto">
            <summary>
            Nombre del proyecto
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.idOfertaVivienda">
            <summary>
            Identificador de la oferta
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.nombreFrente">
            <summary>
            Nombre de frente
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.ordenVerificacion">
            <summary>
            Orden Verificacion
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.idVivienda">
            <summary>
            Identificador de la vivienda
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.idEstatusVivienda">
            <summary>
            Identificador del estatus de la vivienda
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.estatusVivienda">
            <summary>
            Estatus de la vivienda
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.direccion">
            <summary>
            Direccion
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.idEstado">
            <summary>
            Identificador de la entidad federativa
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.nombreEstado">
            <summary>
            Nombre de la entidad federativa
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.ConsultaCuv.porcentajeAvanceObra">
            <summary>
            Porcentaje de avance de obra (0-100)
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv">
            <summary>
            Objeto contenedor de filtros para la consulta de CUVS
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.IdProyecto">
            <summary>
            Identificador del proyecto
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.IdOferta">
            <summary>
            Identificador de la oferta
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.ClaveOferta">
            <summary>
            Clave de la oferta
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.OrdenVerificacion">
            <summary>
            Orden de verificacion
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.Cuv">
            <summary>
            CUV
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.Cuvs">
            <summary>
            Lista de CUVs
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.IdEstatusJuridicoVivienda">
            <summary>
            Identificador del estatus de vivienda
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.IdEntidadFederativa">
            <summary>
            Identificador de la entidad federativa
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.NombreFrente">
            <summary>
            Nombre de frente
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Mediciones.FiltrosConsultaCuv.NumeroRegistroRuv">
            <summary>
            Numero de registro RUV
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdUsuario">
            <summary>
            Id del usuario que envia el proyecto.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.NombreUsuario">
            <summary>
            Nombre del usuario que envia el proyecto.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.TokenGuid">
            <summary>
            Token de acceso del usuario que envia el proyecto.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdEmpresa">
            <summary>
            Id de la empresa que envia el proyectos.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdOferta">
            <summary>
            Id del proyecto enviado.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdEstatusPrevio">
            <summary>
            Id del estatus previo del proyecto
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdOrdenTrabajo">
            <summary>
            Id de la orden de trabajo a dictaminar.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdServicio">
            <summary>
            Id del servicio a dictaminar
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.Aceptacion">
            <summary>
            Indica si la dictaminacion es aceptacion o rechazo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.SeActualizaDictaminacion">
            <summary>
            Indica si es una nueva dictaminacion o una actualizacion.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Api.MensajeDictaminacionOferta.IdRegistro">
            <summary>
            Id del proyecto a dictaminar
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Oferta.Data.ClaveOferta">
            <summary>
            Clase que contiene un elemento del catalogo de idoferta, clave
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Data.ClaveOferta.IdOferta">
            <summary>
            Identificador de la oferta
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Data.ClaveOferta.Clave">
            <summary>
            Clave de la oferta
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Oferta.Data.EstatusVivienda">
            <summary>
            Clase que modela un elemento del catalogo de estatus vivienda
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Data.EstatusVivienda.IdEstatusVivienda">
            <summary>
            Identificador del estatus de la vivienda
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Data.EstatusVivienda.Nombre">
            <summary>
            Nombre del estatus
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Oferta.Data.EstatusVivienda.Descripcion">
            <summary>
            Descripcion del estatus
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Data.OrdenVerificacion">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor">
            <summary>
            <AUTHOR> 
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.Estado">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.JsonPromotor">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.JsonPromotor.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.PromotoresOferta">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.OfertaVivienda">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.Promotor">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json.Promotor.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorDTO">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorPaginado">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacion">
            <summary>
            <AUTHOR> 
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCoberturaDTO">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Prototipos.Api.TipoDimensionAreaVivienda">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Prototipos.Api.AreaVivienda">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Prototipos.Api.DistribucionAreaVivienda">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Prototipos.Api.Prototipo">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeDictaminacionProyecto.IdOrdenTrabajo">
            <summary>
            Id de la orden de trabajo a dictaminar.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeDictaminacionProyecto.IdServicio">
            <summary>
            Id del servicio a dictaminar
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeDictaminacionProyecto.Aceptacion">
            <summary>
            Indica si la dictaminacion es aceptacion o rechazo
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeDictaminacionProyecto.SeActualizaDictaminacion">
            <summary>
            Indica si es una nueva dictaminacion o una actualizacion.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeDictaminacionProyecto.IdRegistro">
            <summary>
            Id del proyecto a dictaminar
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto">
            <summary>
            Representa un mensaje para procesar el envio de un proyecto
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto.IdUsuario">
            <summary>
            Id del usuario que envia el proyecto.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto.NombreUsuario">
            <summary>
            Nombre del usuario que envia el proyecto.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto.TokenGuid">
            <summary>
            Token de acceso del usuario que envia el proyecto.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto.IdEmpresa">
            <summary>
            Id de la empresa que envia el proyectos.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto.IdProyecto">
            <summary>
            Id del proyecto enviado.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.Proyectos.Api.MensajeEnvioProyecto.IdEstatusPrevio">
            <summary>
            Id del estatus previo del proyecto
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.EmpresaxDocumentoDto.archivoStream">
            <summary>
            Contenedor datos de  archivo 
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.EmpresaxDocumentoDto.contenedorBlob">
            <summary>
             Nombre del contendor en repositorio Blob
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.DiccionarioTipoMitigacion">
            <summary>
            
            </summary>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Resultado">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Resultado.descripcion">
            <summary>
            
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Resultado.valor">
            <summary>
            
            </summary>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.Util.CallService`1.PostEntity(System.String,System.Object)">
            <summary>
            
            </summary>
            <param name="url"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.Util.CallService`1.LISTPostEntity(System.String,System.Object)">
            <summary>
            
            </summary>
            <param name="url"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:RUV.RUVPP.Oferta.Modelo.Util.CallService`1.getEntity(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            
            </summary>
            <param name="url"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="T:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv">
            <summary>
            Representa una documento cargado en el sistema del RUV++.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv.IdDocumento">
            <summary>
            Id del documento.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv.NombreArchivo">
            <summary>
            Nombre del archivo.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv.UrlArchivo">
            <summary>
            URL para descarga el archivo.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv.IdCatalogoDocumento">
            <summary>
            Id catalogo documento.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv.idUsuarioCarga">
            <summary>
            Id del usuario que cargo el documento.
            </summary>
        </member>
        <member name="P:RUV.RUVPP.Oferta.Comun.Modelo.DocumentoRuv.EsNuevaCarga">
            <summary>
            indica si se cargo el documento o si se pre - cargo anteriormente
            </summary>
        </member>
    </members>
</doc>
