<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.IdentityModel.Tokens.Jwt</name>
    </assembly>
    <members>
        <member name="T:System.IdentityModel.DateTimeUtil">
            <summary>
            Helper class for adding DateTimes and Timespans.
            </summary>
        </member>
        <member name="M:System.IdentityModel.DateTimeUtil.Add(System.DateTime,System.TimeSpan)">
            <summary>
            Add a DateTime and a TimeSpan.
            The maximum time is DateTime.MaxTime.  It is not an error if time + timespan &gt; MaxTime.
            Just return MaxTime.
            </summary>
            <param name="time">Initial <see cref="T:System.DateTime"/> value.</param>
            <param name="timespan"><see cref="T:System.TimeSpan"/> to add.</param>
            <returns><see cref="T:System.DateTime"/> as the sum of time and timespan.</returns>
        </member>
        <member name="M:System.IdentityModel.DateTimeUtil.GetMaxValue(System.DateTimeKind)">
            <summary>
            Gets the Maximum value for a DateTime specifying kind.
            </summary>
            <param name="kind">DateTimeKind to use.</param>
            <returns>DateTime of specified kind.</returns>
        </member>
        <member name="M:System.IdentityModel.DateTimeUtil.GetMinValue(System.DateTimeKind)">
            <summary>
            Gets the Minimum value for a DateTime specifying kind.
            </summary>
            <param name="kind">DateTimeKind to use.</param>
            <returns>DateTime of specified kind.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.ErrorMessages">
            <summary>
            Error codes and messages
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Utility.SerializeAsSingleCommaDelimitedString(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Serializes the list of strings into string as follows:
            'str1','str2','str3' ...
            </summary>
            <param name="strings">
            The strings used to build a comma delimited string.
            </param>
            <returns>
            The single <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.AsymmetricSignatureProvider">
            <summary>
            Provides signing and verifying operations when working with an <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.SignatureProvider">
            <summary>
            This class defines the object model for types that provide signature services.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input'
            </summary>
            <param name="input">bytes to sign.</param>
            <returns>signed bytes</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that a signature created over the 'input' matches the signature.
            </summary>
            <param name="input">bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <returns>true if the computed signature matches the signature parameter, false otherwise.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProvider.Dispose">
            <summary>
            Calls <see cref="M:System.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)">
            <summary>
            Can be over written in descendants to dispose of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer</param>     
        </member>
        <member name="P:System.IdentityModel.Tokens.SignatureProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(System.IdentityModel.Tokens.AsymmetricSecurityKey,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.AsymmetricSignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">
            The <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/> that will be used for cryptographic operations.
            </param>
            <param name="algorithm">
            The signature algorithm to apply.
            </param>
            <param name="willCreateSignatures">
            If this <see cref="T:System.IdentityModel.Tokens.AsymmetricSignatureProvider"/> is required to create signatures then set this to true.
            <para>
            Creating signatures requires that the <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/> has access to a private key. 
            Verifying signatures (the default), does not require access to the private key.
            </para>
            </param>
            <exception cref="T:System.ArgumentNullException">
            'key' is null.
            </exception>
            <exception cref="T:System.ArgumentNullException">
            'algorithm' is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            'algorithm' contains only whitespace.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            willCreateSignatures is true and <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize is less than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumAsymmetricKeySizeInBitsForSigning"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize is less than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumAsymmetricKeySizeInBitsForVerifying"/>. Note: this is always checked.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.IdentityModel.Tokens.AsymmetricSecurityKey.GetHashAlgorithmForSignature(System.String)"/> throws.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.IdentityModel.Tokens.AsymmetricSecurityKey.GetHashAlgorithmForSignature(System.String)"/> returns null.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.IdentityModel.Tokens.AsymmetricSecurityKey.GetSignatureFormatter(System.String)"/> throws.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.IdentityModel.Tokens.AsymmetricSecurityKey.GetSignatureFormatter(System.String)"/> returns null.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.IdentityModel.Tokens.AsymmetricSecurityKey.GetSignatureDeformatter(System.String)"/> throws.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.IdentityModel.Tokens.AsymmetricSecurityKey.GetSignatureDeformatter(System.String)"/> returns null.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.Security.Cryptography.AsymmetricSignatureFormatter.SetHashAlgorithm(System.String)"/> throws.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            Is thrown if the <see cref="M:System.Security.Cryptography.AsymmetricSignatureDeformatter.SetHashAlgorithm(System.String)"/> throws.
            </exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/> and algorithm passed to <see cref="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(System.IdentityModel.Tokens.AsymmetricSecurityKey,System.String,System.Boolean)"/>.
            </summary>
            <param name="input">bytes to be signed.</param>
            <returns>a signature over the input.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null. </exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException">if <see cref="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called. </exception>
            <exception cref="T:System.InvalidOperationException">if the internal <see cref="T:System.Security.Cryptography.AsymmetricSignatureFormatter"/> is null. This can occur if the constructor parameter 'willBeUsedforSigning' was not 'true'.</exception>
            <exception cref="T:System.InvalidOperationException">if the internal <see cref="T:System.Security.Cryptography.HashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that a signature over the' input' matches the signature.
            </summary>
            <param name="input">the bytes to generate the signature over.</param>
            <param name="signature">the value to verify against.</param>
            <returns>true if signature matches, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0.</exception>
            <exception cref="T:System.ObjectDisposedException">if <see cref="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called. </exception>
            <exception cref="T:System.InvalidOperationException">if the internal <see cref="T:System.Security.Cryptography.AsymmetricSignatureDeformatter"/> is null. This can occur if a derived type does not call the base constructor.</exception>
            <exception cref="T:System.InvalidOperationException">if the internal <see cref="T:System.Security.Cryptography.HashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)">
            <summary>
            Calls <see cref="M:System.Security.Cryptography.HashAlgorithm.Dispose"/> to release this managed resources.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.Base64UrlEncoder">
            <summary>
            Encodes and Decodes strings as Base64Url encoding.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.String)">
            <summary>
            The following functions perform base64url encoding which differs from regular base64 encoding as follows
            * padding is skipped so the pad character '=' doesn't have to be percent encoded
            * the 62nd and 63rd regular base64 encoding characters ('+' and '/') are replace with ('-' and '_')
            The changes make the encoding alphabet file and URL safe.
            </summary>
            <param name="arg">string to encode.</param>
            <returns>Base64Url encoding of the UTF8 bytes.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation that is encoded with base-64-url digits. Parameters specify
            the subset as an offset in the input array, and the number of elements in the array to convert.
            </summary>
            <param name="inArray">An array of 8-bit unsigned integers.</param>
            <param name="length">An offset in inArray.</param>
            <param name="offset">The number of elements of inArray to convert.</param>
            <returns>The string representation in base 64 url encodingof length elements of inArray, starting at position offset.</returns>
            <exception cref="T:System.ArgumentNullException">'inArray' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or length is negative OR offset plus length is greater than the length of inArray.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.Byte[])">
            <summary>
            Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation that is encoded with base-64-url digits. Parameters specify
            the subset as an offset in the input array, and the number of elements in the array to convert.
            </summary>
            <param name="inArray">An array of 8-bit unsigned integers.</param>
            <returns>The string representation in base 64 url encodingof length elements of inArray, starting at position offset.</returns>
            <exception cref="T:System.ArgumentNullException">'inArray' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or length is negative OR offset plus length is greater than the length of inArray.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.Base64UrlEncoder.DecodeBytes(System.String)">
            <summary>
             Converts the specified string, which encodes binary data as base-64-url digits, to an equivalent 8-bit unsigned integer array.</summary>
            <param name="str">base64Url encoded string.</param>
            <returns>UTF8 bytes.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.Base64UrlEncoder.Decode(System.String)">
            <summary>
            Decodes the string from Base64UrlEncoded to UTF8.
            </summary>
            <param name="arg">string to decode.</param>
            <returns>UTF8 string.</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.ClaimTypeMapping">
            <summary>
            Defines the inbound and outbound mapping for claim claim types from jwt to .net claim 
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.ClaimTypeMapping.#cctor">
            <summary>
            Initializes static members of the <see cref="T:System.IdentityModel.Tokens.ClaimTypeMapping"/> class. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.ClaimTypeMapping.InboundClaimTypeMap">
            <summary>
            Gets the InboundClaimTypeMap used by JwtSecurityTokenHandler when producing claims from jwt. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.ClaimTypeMapping.OutboundClaimTypeMap">
            <summary>
            Gets the OutboundClaimTypeMap is used by JwtSecurityTokenHandler to shorten claim types when creating a jwt. 
            </summary>
        </member>
        <member name="T:System.IdentityModel.DiagnosticUtility">
            <summary>
            Provides common code for services to use in generating diagnostics and taking actions.
            </summary>
        </member>
        <member name="M:System.IdentityModel.DiagnosticUtility.IsFatal(System.Exception)">
            <summary>
            Returns true if the provided exception matches any of a list of hard system faults that should be allowed
            through to outer exception handlers.
            </summary>
            <param name="exception">The exception to check.</param>
            <remarks>
            <para>Typically this method is used when there is a need to catch all exceptions, but to ensure that .NET runtime
            and execution engine exceptions are not absorbed by the catch block. Use of this method also avoids FxCop
            warnings about not using general catch blocks.</para>
            <para>Please note that use of this method is expensive because of the amount of reflection it performs.
            If you can refactor your code to catch more specific exceptions than Exception to avoid using this method,
            you should.</para>
            <para>Example of use:</para>
            <code>
            try
            {
                // Code needing a full Exception catch block
            }
            catch (Exception ex)
            {
                if (DiagnosticUtility.IsFatal(ex))
                {
                    throw;
                }
                // Perform any needed logging and handling for absorbed exception.
            }
            </code>
            </remarks>
            <returns>true if the exception should NOT be trapped</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.EpochTime">
            <summary>
            Returns the absolute DateTime or the Seconds since Unix Epoch, where Epoch is UTC 1970-01-01T0:0:0Z.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.EpochTime.UnixEpoch">
            <summary>
            DateTime as UTV for UnixEpoch
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.EpochTime.GetIntDate(System.DateTime)">
            <summary>
            Per JWT spec:
            Gets the number of seconds from 1970-01-01T0:0:0Z as measured in UTC until the desired date/time.
            </summary>
            <param name="datetime">The DateTime to convert to seconds.</param>
            <remarks>if dateTimeUtc less than UnixEpoch, return 0</remarks>
            <returns>the number of seconds since Unix Epoch.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.EpochTime.DateTime(System.Int64)">
            <summary>
            Creates a DateTime from epoch time.
            </summary>
            <param name="secondsSinceUnixEpoch">Number of seconds.</param>
            <returns>The DateTime in UTC.</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.ISecurityTokenValidator">
            <summary>
            ISecurityTokenValidator
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.ISecurityTokenValidator.CanReadToken(System.String)">
            <summary>
            Returns true if the token can be read, false otherwise.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.ISecurityTokenValidator.ValidateToken(System.String,System.IdentityModel.Tokens.TokenValidationParameters,System.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Validates a token passed as a string using <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/>
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.ISecurityTokenValidator.MaximumTokenSizeInBytes">
            <summary>
            Gets and sets the maximum size in bytes, that a will be processed.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.ITokenReplayCache">
            <summary>
            Interface that defines a simple cache for tacking replaying of security tokens.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.ITokenReplayCache.TryAdd(System.String,System.DateTime)">
            <summary>
            Try to add a securityToken.
            </summary>
            <param name="securityToken">the security token to add.</param>
            <param name="expiresOn">the time when security token expires.</param>
            <returns>true if the security token was successfully added.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.ITokenReplayCache.TryFind(System.String)">
            <summary>
            Try to find securityToken
            </summary>
            <param name="securityToken">the security token to find.</param>
            <returns>true if the security token is found.</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.Serializer">
            <summary>
            Definition for a delegate that can be set on <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Serializer"/> to control serialization of objects into JSON.
            </summary>
            <param name="obj">Object to serialize</param>
            <returns>The serialized object.</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.Deserializer">
            <summary>
            Definition for a delegate that can be set on <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer"/> to control deserialization JSON into objects.
            </summary>
            <param name="obj">JSON to deserialize.</param>
            <param name="targetType">type expected.</param>
            <returns>The deserialized object.</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.JsonExtensions">
            <summary>
            Dictionary extensions for serializations
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JsonExtensions.SerializeToJson(System.Object)">
            <summary>
            Serializes an object to JSON.
            </summary>
            <param name="value">The object to serialize</param>
            <returns>the object as JSON.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JsonExtensions.DeserializeFromJson``1(System.String)">
            <summary>
            Deserialzes JSON into an instance of type T.
            </summary>
            <typeparam name="T">the object type.</typeparam>
            <param name="jsonString">the JSON to deserialze.</param>
            <returns>a new instance of type T.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JsonExtensions.DeserializeJwtHeader(System.String)">
            <summary>
            Deserialzes JSON into an instance of <see cref="T:System.IdentityModel.Tokens.JwtHeader"/>.
            </summary>
            <param name="jsonString">the JSON to deserialze.</param>
            <returns>a new instance <see cref="T:System.IdentityModel.Tokens.JwtHeader"/>.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JsonExtensions.DeserializeJwtPayload(System.String)">
            <summary>
            Deserialzes JSON into an instance of <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>.
            </summary>
            <param name="jsonString">the JSON to deserialze.</param>
            <returns>a new instance <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>.</returns>
        </member>
        <member name="P:System.IdentityModel.Tokens.JsonExtensions.Serializer">
            <summary>
            Gets or sets a <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Serializer"/> to use when serializing objects to JSON.
            </summary>
            <exception cref="T:System.ArgumentNullException">if 'value' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer">
            <summary>
            Gets or sets a <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer"/> to use when deserializing objects from JSON.
            </summary>
            <exception cref="T:System.ArgumentNullException">if 'value' is null.</exception>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtConfigurationStrings">
            <summary>
            contains the element and attribute names used in config when parsing the JwtSecurityTokenHandler from XML.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtConstants">
            <summary>
            Constants for Json Web tokens.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtConstants.HeaderType">
            <summary>
            Short header type.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtConstants.HeaderTypeAlt">
            <summary>
            Long header type.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtConstants.TokenType">
            <summary>
            Short token type.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtConstants.TokenTypeAlt">
            <summary>
            Long token type.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtConstants.JsonCompactSerializationRegex">
            <summary>
            Token format: 'header.payload.signature'. Signature is optional, but '.' is required.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtConstants.JsonClaimValueType">
            <summary>
            When mapping json to .Net Claim(s), if the value was not a string (or an enumeration of strings), the ClaimValue will serialized using the current JSON serializer, a property will be added with the .Net type and the ClaimTypeValue will be set to 'JsonClaimValueType'.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtAlgorithms">
            <summary>
            List of algorithms see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.ECDSA_SHA256">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.ECDSA_SHA384">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.ECDSA_SHA512">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.HMAC_SHA256">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.HMAC_SHA384">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.HMAC_SHA512">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.NONE">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.RSA_SHA256">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.RSA_SHA384">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtAlgorithms.RSA_SHA512">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-jose-json-web-algorithms-26#section-3
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtHeaderParameterNames">
            <summary>
            List of header parameter names see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.Alg">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.Cty">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.Kid">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.Jku">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.Jwk">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.Typ">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.X5c">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.X5t">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtHeaderParameterNames.X5u">
            <summary>
            see: http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-5
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtRegisteredClaimNames">
            <summary>
            List of registered claims from different sources
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            http://openid.net/specs/openid-connect-core-1_0.html#IDToken
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Actort">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Acr">
            <summary>
            http://openid.net/specs/openid-connect-core-1_0.html#IDToken
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Amr">
            <summary>
            http://openid.net/specs/openid-connect-core-1_0.html#IDToken
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Aud">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.AuthTime">
            <summary>
            http://openid.net/specs/openid-connect-core-1_0.html#IDToken
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Azp">
            <summary>
            http://openid.net/specs/openid-connect-core-1_0.html#IDToken
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Birthdate">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.CHash">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Email">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Exp">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Gender">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.FamilyName">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.GivenName">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Iat">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Iss">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Jti">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.NameId">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Nonce">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Nbf">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Prn">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Sub">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Typ">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.UniqueName">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtRegisteredClaimNames.Website">
            <summary>
            http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-20#section-4
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtHeader">
            <summary>
            Initializes a new instance of <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> which contains JSON objects representing the cryptographic operations applied to the JWT and optionally any additional properties of the JWT. 
            The member names within the JWT Header are referred to as Header Parameter Names. 
            <para>These names MUST be unique and the values must be <see cref="T:System.String"/>(s). The corresponding values are referred to as Header Parameter Values.</para>
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtHeader.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> class. Default string comparer <see cref="P:System.StringComparer.Ordinal"/>.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtHeader.#ctor(System.IdentityModel.Tokens.SigningCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> class. With the Header Parameters as follows: 
            <para>{ { typ, JWT }, { alg, Mapped( <see cref="P:System.IdentityModel.Tokens.SigningCredentials.SignatureAlgorithm"/> } }
            See: Algorithm Mapping below.</para>
            </summary>
            <param name="signingCredentials">The <see cref="P:System.IdentityModel.Tokens.JwtHeader.SigningCredentials"/> that will be or were used to sign the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</param>
            <remarks>
            <para>For each <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> in signingCredentials.SigningKeyIdentifier</para>
            <para>if the clause  is a <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> Header Parameter { clause.Name, clause.Id } will be added.</para>
            <para>For example, if clause.Name == 'kid' and clause.Id == 'SecretKey99'. The JSON object { kid, SecretKey99 } would be added.</para>
            <para>In addition, if the <see cref="P:System.IdentityModel.Tokens.JwtHeader.SigningCredentials"/> is a <see cref="T:System.IdentityModel.Tokens.X509SigningCredentials"/> the JSON object { x5t, Base64UrlEncoded( <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHashString"/> } will be added.</para>
            <para>This simplifies the common case where a X509Certificate is used.</para>
            <para>================= </para>
            <para>Algorithm Mapping</para>
            <para>================= </para>
            <para><see cref="P:System.IdentityModel.Tokens.SigningCredentials.SignatureAlgorithm"/> describes the algorithm that is discoverable by the CLR runtime.</para>
            <para>The  { alg, 'value' } placed in the header reflects the JWT specification.</para>
            <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundAlgorithmMap"/> contains a signature mapping where the 'value' above will be translated according to this mapping.
            <para>Current mapping is:</para>
            <para>    'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256' =&gt; 'RS256'</para>
            <para>    'http://www.w3.org/2001/04/xmldsig-more#hmac-sha256' =&gt; 'HS256'</para>
            </remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtHeader.SerializeToJson">
            <summary>
            Serializes this instance to JSON.
            </summary>
            <returns>this instance as JSON.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Serializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtHeader.Base64UrlEncode">
            <summary>
            Encodes this instance as Base64UrlEncoded JSON.
            </summary>
            <returns>Base64UrlEncoded JSON.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Serializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtHeader.Base64UrlDeserialize(System.String)">
            <summary>
            Deserializes Base64UrlEncoded JSON into a <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> instance.
            </summary>
            <param name="base64UrlEncodedJsonString">base64url encoded JSON to deserialize.</param>
            <returns>an instance of <see cref="T:System.IdentityModel.Tokens.JwtHeader"/>.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtHeader.Deserialize(System.String)">
            <summary>
            Deserialzes JSON into a <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> instance.
            </summary>
            <param name="jsonString"> the JSON to deserialize.</param>
            <returns>an instance of <see cref="T:System.IdentityModel.Tokens.JwtHeader"/>.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtHeader.Alg">
            <summary>
            Gets the signature algorithm that was used to create the signature.
            </summary>
            <remarks>If the signature algorithm is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtHeader.SigningCredentials">
            <summary>
            Gets the <see cref="P:System.IdentityModel.Tokens.JwtHeader.SigningCredentials"/> passed in the constructor.
            </summary>
            <remarks>This value may be null.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtHeader.Typ">
            <summary>
            Gets the mime type (Typ) of the token.
            </summary>
            <remarks>If the mime type is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtHeader.SigningKeyIdentifier">
            <summary>
            Gets a <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> that contains a <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> for each key found.
            </summary>
            <remarks>
            Keys are identified by matching a 'Reserved Header Parameter Name' found in the in JSON Web Signature specification.
            <para>Names recognized are: jku, jkw, kid, x5c, x5t, x5u</para>
            <para>'x5t' adds a <see cref="T:System.IdentityModel.Tokens.X509ThumbprintKeyIdentifierClause"/> passing a the Base64UrlDecoded( Value ) to the constructor.</para>
            <para>'jku', 'jkw', 'kid', 'x5u', 'x5c' each add a <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> with the { Name, Value } passed to the <see cref="M:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause.#ctor(System.String,System.String)"/>.</para>
            <para>   </para>
            <para>If no keys are found, an empty <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> will be returned.</para>
            </remarks>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtPayload">
            <summary>
            Initializes a new instance of <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> which contains JSON objects representing the claims contained in the JWT. Each claim is a JSON object of the form { Name, Value }.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> class with no claims. Default string comparer <see cref="P:System.StringComparer.Ordinal"/>. 
            Creates a empty <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.#ctor(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> class with <see cref="T:System.Collections.Generic.IEnumerable`1"/>. Default string comparer <see cref="P:System.StringComparer.Ordinal"/>.
            <param name="claims">the claims to add.</param>
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> class with claims added for each parameter specified. Default string comparer <see cref="P:System.StringComparer.Ordinal"/>. 
            </summary>
            <param name="issuer">if this value is not null, a { iss, 'issuer' } claim will be added.</param>
            <param name="audience">if this value is not null, a { aud, 'audience' } claim will be added</param>
            <param name="claims">if this value is not null then for each <see cref="T:System.Security.Claims.Claim"/> a { 'Claim.Type', 'Claim.Value' } is added. If duplicate claims are found then a { 'Claim.Type', List&lt;object&gt; } will be created to contain the duplicate values.</param>
            <param name="notBefore">if notbefore.HasValue is 'true' a { nbf, 'value' } claim is added.</param>
            <param name="expires">if expires.HasValue is 'true' a { exp, 'value' } claim is added.</param>
            <remarks>Comparison is set to <see cref="P:System.StringComparer.Ordinal"/>
            <para>The 4 parameters: 'issuer', 'audience', 'notBefore', 'expires' take precednece over <see cref="T:System.Security.Claims.Claim"/>(s) in 'claims'. The values in 'claims' will be overridden.</para></remarks>
            <exception cref="T:System.ArgumentException">if 'expires' &lt;= 'notbefore'.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.AddClaim(System.Security.Claims.Claim)">
            <summary>
            Adds a JSON object representing the <see cref="T:System.Security.Claims.Claim"/> to the <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>
            </summary>
            <param name="claim">{ 'Claim.Type', 'Claim.Value' } is added. If a JSON object is found with the name == <see cref="P:System.Security.Claims.Claim.Type"/> then a { 'Claim.Type', List&lt;object&gt; } will be created to contain the duplicate values.</param>
            <remarks>See <see cref="M:System.IdentityModel.Tokens.JwtPayload.AddClaims(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})"/> for details on how <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundClaimTypeMap"/> is applied.</remarks>
            <exception cref="T:System.ArgumentNullException">'claim' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.AddClaims(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Adds a number of <see cref="T:System.Security.Claims.Claim"/> to the <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> as JSON { name, value } pairs.
            </summary>
            <param name="claims">for each <see cref="T:System.Security.Claims.Claim"/> a JSON pair { 'Claim.Type', 'Claim.Value' } is added. If duplicate claims are found then a { 'Claim.Type', List&lt;object&gt; } will be created to contain the duplicate values.</param>
            <remarks><para>Each <see cref="T:System.Security.Claims.Claim"/> added will have <see cref="P:System.Security.Claims.Claim.Type"/> translated according to the mapping found in <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundClaimTypeMap"/>. Adding and removing to <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundClaimTypeMap"/> 
            will affect the name component of the Json claim</para>
            <para>Any <see cref="T:System.Security.Claims.Claim"/> in the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that is null, will be ignored.</para></remarks>
            <exception cref="T:System.ArgumentNullException">'claims' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.GetDateTime(System.String)">
            <summary>
            Gets the DateTime using the number of seconds from 1970-01-01T0:0:0Z (UTC)
            </summary>
            <param name="key">Claim in the payload that should map to an integer.</param>
            <remarks>If the claim is not found, the function returns: DateTime.MinValue
            </remarks>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenException">if an overflow exception is thrown by the runtime.</exception>
            <returns>the DateTime representation of a claim.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.SerializeToJson">
            <summary>
            Serializes this instance to JSON.
            </summary>
            <returns>this instance as JSON.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Serializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.Base64UrlEncode">
            <summary>
            Encodes this instance as Base64UrlEncoded JSON.
            </summary>
            <returns>Base64UrlEncoded JSON.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Serializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.Base64UrlDeserialize(System.String)">
            <summary>
            Deserializes Base64UrlEncoded JSON into a <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> instance.
            </summary>
            <param name="base64UrlEncodedJsonString">base64url encoded JSON to deserialize.</param>
            <returns>an instance of <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtPayload.Deserialize(System.String)">
            <summary>
            Deserialzes JSON into a <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> instance.
            </summary>
            <param name="jsonString">the JSON to deserialize.</param>
            <returns>an instance of <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>.</returns>
            <remarks>use <see cref="P:System.IdentityModel.Tokens.JsonExtensions.Deserializer"/> to customize JSON serialization.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Actort">
            <summary>
            Gets the 'value' of the 'actor' claim { actort, 'value' }.
            </summary>
            <remarks>If the 'actor' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Acr">
            <summary>
            Gets the 'value' of the 'acr' claim { acr, 'value' }.
            </summary>
            <remarks>If the 'acr' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Amr">
            <summary>
            Gets the 'value' of the 'amr' claim { amr, 'value' }.
            </summary>
            <remarks>If the 'amr' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.AuthTime">
            <summary>
            Gets the 'value' of the 'auth_time' claim { auth_time, 'value' }.
            </summary>
            <remarks>If the 'auth_time' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Aud">
            <summary>
            Gets the 'value' of the 'audience' claim { aud, 'value' } as a list of strings.
            </summary>
            <remarks>If the 'audience' claim is not found, an empty enumerable is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Azp">
            <summary>
            Gets the 'value' of the 'azp' claim { azp, 'value' }.
            </summary>
            <remarks>If the 'azp' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.CHash">
            <summary>
            Gets 'value' of the 'c_hash' claim { c_hash, 'value' }.
            </summary>
            <remarks>If the 'c_hash' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Exp">
            <summary>
            Gets the 'value' of the 'expiration' claim { exp, 'value' }.
            </summary>
            <remarks>If the 'expiration' claim is not found OR could not be converted to <see cref="T:System.Int32"/>, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Jti">
            <summary>
            Gets the 'value' of the 'JWT ID' claim { jti, 'value' }.
            </summary>
            <remarks>If the 'JWT ID' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Iat">
            <summary>
            Gets the 'value' of the 'Issued At' claim { iat, 'value' }.
            </summary>
            <remarks>If the 'Issued At' claim is not found OR cannot be converted to <see cref="T:System.Int32"/> null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Iss">
            <summary>
            Gets 'value' of the 'issuer' claim { iss, 'value' }.
            </summary>
            <remarks>If the 'issuer' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Nbf">
            <summary>
            Gets the 'value' of the 'expiration' claim { nbf, 'value' }.
            </summary>
            <remarks>If the 'notbefore' claim is not found OR could not be converted to <see cref="T:System.Int32"/>, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Nonce">
            <summary>
            Gets 'value' of the 'nonce' claim { nonce, 'value' }.
            </summary>
            <remarks>If the 'nonce' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Sub">
            <summary>
            Gets "value" of the 'subject' claim { sub, 'value' }.
            </summary>
            <remarks>If the 'subject' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.ValidFrom">
            <summary>
            Gets 'value' of the 'notbefore' claim { nbf, 'value' } converted to a <see cref="T:System.DateTime"/> assuming 'value' is seconds since UnixEpoch (UTC 1970-01-01T0:0:0Z).
            </summary>
            <remarks>If the 'notbefore' claim is not found, then <see cref="F:System.DateTime.MinValue"/> is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.ValidTo">
            <summary>
            Gets 'value' of the 'expiration' claim { exp, 'value' } converted to a <see cref="T:System.DateTime"/> assuming 'value' is seconds since UnixEpoch (UTC 1970-01-01T0:0:0Z).
            </summary>
            <remarks>If the 'expiration' claim is not found, then <see cref="F:System.DateTime.MinValue"/> is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtPayload.Claims">
            <summary>
            Gets a <see cref="T:System.Collections.Generic.IEnumerable`1"/><see cref="T:System.Security.Claims.Claim"/> for each JSON { name, value }.
            </summary>
            <remarks>Each <see cref="T:System.Security.Claims.Claim"/>(s) returned will have the <see cref="P:System.Security.Claims.Claim.Type"/> translated according to the mapping found in <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap"/>. Adding and removing to <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap"/> will affect the value of the <see cref="P:System.Security.Claims.Claim.Type"/>.
            <para><see cref="P:System.Security.Claims.Claim.Issuer"/> and <see cref="P:System.Security.Claims.Claim.OriginalIssuer"/> will be set to the value of <see cref="P:System.IdentityModel.Tokens.JwtPayload.Iss"/> ( <see cref="F:System.String.Empty"/> if null).</para></remarks>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtSecurityToken">
            <summary>
            A <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> designed for representing a JSON Web Token (JWT).
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> from a string in JWS Compact serialized format.
            </summary>
            <param name="jwtEncodedString">A JSON Web Token that has been serialized in JWS Compact serialized format.</param>
            <exception cref="T:System.ArgumentNullException">'jwtEncodedString' is null.</exception>
            <exception cref="T:System.ArgumentException">'jwtEncodedString' contains only whitespace.</exception>
            <exception cref="T:System.ArgumentException">'jwtEncodedString' is not in JWS Compact serialized format.</exception>
            <remarks>
            The contents of this <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> have not been validated, the JSON Web Token is simply decoded. Validation can be accomplished using <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateToken(System.String,System.IdentityModel.Tokens.TokenValidationParameters,System.IdentityModel.Tokens.SecurityToken@)"/>
            </remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> class where the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> contains the crypto algorithms applied to the encoded <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> and <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>. The jwtEncodedString is the result of those operations.
            </summary>
            <param name="header">Contains JSON objects representing the cryptographic operations applied to the JWT and optionally any additional properties of the JWT</param>
            <param name="payload">Contains JSON objects representing the claims contained in the JWT. Each claim is a JSON object of the form { Name, Value }</param>
            <param name="rawHeader">base64urlencoded JwtHeader</param>
            <param name="rawPayload">base64urlencoded JwtPayload</param>
            <param name="rawSignature">base64urlencoded JwtSignature</param>
            <exception cref="T:System.ArgumentNullException">'header' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'payload' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'rawSignature' is null.</exception>
            <exception cref="T:System.ArgumentException">'rawHeader' or 'rawPayload' is null or whitespace.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> class where the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> contains the crypto algorithms applied to the encoded <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> and <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>. The jwtEncodedString is the result of those operations.
            </summary>
            <param name="header">Contains JSON objects representing the cryptographic operations applied to the JWT and optionally any additional properties of the JWT</param>
            <param name="payload">Contains JSON objects representing the claims contained in the JWT. Each claim is a JSON object of the form { Name, Value }</param>
            <exception cref="T:System.ArgumentNullException">'header' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'payload' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.IdentityModel.Tokens.SigningCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> class specifying optional parameters.
            </summary>
            <param name="issuer">if this value is not null, a { iss, 'issuer' } claim will be added.</param>
            <param name="audience">if this value is not null, a { aud, 'audience' } claim will be added</param>
            <param name="claims">if this value is not null then for each <see cref="T:System.Security.Claims.Claim"/> a { 'Claim.Type', 'Claim.Value' } is added. If duplicate claims are found then a { 'Claim.Type', List&lt;object&gt; } will be created to contain the duplicate values.</param>
            <param name="expires">if expires.HasValue a { exp, 'value' } claim is added.</param>
            <param name="notBefore">if notbefore.HasValue a { nbf, 'value' } claim is added.</param>
            <param name="signingCredentials">The <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningCredentials"/> that will be used to sign the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>. See <see cref="M:System.IdentityModel.Tokens.JwtHeader.#ctor(System.IdentityModel.Tokens.SigningCredentials)"/> for details pertaining to the Header Parameter(s).</param>
            <exception cref="T:System.ArgumentException">if 'expires' &lt;= 'notbefore'.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityToken.ToString">
            <summary>
            Decodes the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> and <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>
            </summary>
            <returns>A string containing the header and payload in JSON format</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityToken.Decode(System.String)">
            <summary>
            Decodes the string into the header, payload and signature
            </summary>
            <param name="jwtEncodedString">Base64Url encoded string.</param>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Actor">
            <summary>
            Gets the 'value' of the 'actor' claim { actort, 'value' }.
            </summary>
            <remarks>If the 'actor' claim is not found, null is returned.</remarks> 
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Audiences">
            <summary>
            Gets the list of 'audience' claim { aud, 'value' }.
            </summary>
            <remarks>If the 'audience' claim is not found, enumeration will be empty.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Claims">
            <summary>
            Gets the <see cref="T:System.Security.Claims.Claim"/>(s) for this token.
            </summary>
            <remarks><para><see cref="T:System.Security.Claims.Claim"/>(s) returned will NOT have the <see cref="P:System.Security.Claims.Claim.Type"/> translated according to <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap"/></para></remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.EncodedHeader">
            <summary>
            Gets the Base64UrlEncoded <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> associated with this instance.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.EncodedPayload">
            <summary>
            Gets the Base64UrlEncoded <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> associated with this instance.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Header">
            <summary>
            Gets the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> associated with this instance.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Id">
            <summary>
            Gets the 'value' of the 'JWT ID' claim { jti, ''value' }.
            </summary>
            <remarks>If the 'JWT ID' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Issuer">
            <summary>
            Gets the 'value' of the 'issuer' claim { iss, 'value' }.
            </summary>
            <remarks>If the 'issuer' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Payload">
            <summary>
            Gets the <see cref="T:System.IdentityModel.Tokens.JwtPayload"/> associated with this instance.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.RawData">
            <summary>
            Gets the original raw data of this instance when it was created.
            </summary>
            <remarks>The original JSON Compact serialized format passed to one of the two constructors <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.String)"/>
            or <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload,System.String,System.String,System.String)"/></remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.RawHeader">
            <summary>
            Gets the original raw data of this instance when it was created.
            </summary>
            <remarks>The original JSON Compact serialized format passed to one of the two constructors <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.String)"/>
            or <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload,System.String,System.String,System.String)"/></remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.RawPayload">
            <summary>
            Gets the original raw data of this instance when it was created.
            </summary>
            <remarks>The original JSON Compact serialized format passed to one of the two constructors <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.String)"/>
            or <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload,System.String,System.String,System.String)"/></remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.RawSignature">
            <summary>
            Gets the original raw data of this instance when it was created.
            </summary>
            <remarks>The original JSON Compact serialized format passed to one of the two constructors <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.String)"/>
            or <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload,System.String,System.String,System.String)"/></remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.SecurityKeys">
            <summary>
            Gets the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>s for this instance.
            </summary>
            <remarks>By default an empty collection is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.SignatureAlgorithm">
            <summary>
            Gets the signature algorithm associated with this instance.
            </summary>
            <remarks>if there is a <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningCredentials"/> associated with this instance, a value will be returned.  Null otherwise.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningCredentials">
            <summary>
            Gets the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningCredentials"/> associated with this instance.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningKey">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that signed this instance.
            </summary>
            <remarks><see cref="T:System.IdentityModel.Tokens.JwtSecurityTokenHandler"/>.ValidateSignature(...) sets this value when a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> is used to successfully validate a signature.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningToken">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> that contains a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that signed this instance.
            </summary>
            <remarks><see cref="T:System.IdentityModel.Tokens.JwtSecurityTokenHandler"/>.ValidateSignature(...) sets this value when a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> is used to successfully validate a signature.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.Subject">
            <summary>
            Gets "value" of the 'subject' claim { sub, 'value' }.
            </summary>
            <remarks>If the 'subject' claim is not found, null is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.ValidFrom">
            <summary>
            Gets 'value' of the 'notbefore' claim { nbf, 'value' } converted to a <see cref="T:System.DateTime"/> assuming 'value' is seconds since UnixEpoch (UTC 1970-01-01T0:0:0Z).
            </summary>
            <remarks>If the 'notbefore' claim is not found, then <see cref="F:System.DateTime.MinValue"/> is returned.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityToken.ValidTo">
            <summary>
            Gets 'value' of the 'expiration' claim { exp, 'value' } converted to a <see cref="T:System.DateTime"/> assuming 'value' is seconds since UnixEpoch (UTC 1970-01-01T0:0:0Z).
            </summary>
            <remarks>If the 'expiration' claim is not found, then <see cref="F:System.DateTime.MinValue"/> is returned.</remarks>
        </member>
        <member name="T:System.IdentityModel.Tokens.JwtSecurityTokenHandler">
            <summary>
            A <see cref="T:System.IdentityModel.Tokens.SecurityTokenHandler"/> designed for creating and validating Json Web Tokens. See http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-07.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.JwtSecurityTokenHandler.DefaultTokenLifetimeInMinutes">
            <summary>
            Default lifetime of tokens created. When creating tokens, if 'expires' and 'notbefore' are both null, then a default will be set to: expires = DateTime.UtcNow, notbefore = DateTime.UtcNow + TimeSpan.FromMinutes(TokenLifetimeInMinutes).
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.JwtSecurityTokenHandler"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.LoadCustomConfiguration(System.Xml.XmlNodeList)">
            <summary>
            Obsolete method, use <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> when processing tokens.
            </summary>
            <exception cref="T:System.NotSupportedException"> use <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/>. when processing tokens.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CanReadToken(System.Xml.XmlReader)">
            <summary>
            Determines if the <see cref="T:System.Xml.XmlReader"/> is positioned on a well formed &lt;BinarySecurityToken&gt; element.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/> positioned at xml.</param>
            <returns>
            <para>'true' if the reader is positioned at an element &lt;BinarySecurityToken&gt;.
            in the namespace: 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd'</para>
            <para>With an attribute of 'valueType' equal to one of: </para>
            <para>    "urn:ietf:params:oauth:token-type:jwt", "JWT" </para>
            <para>
            For example: &lt;wsse:BinarySecurityToken valueType = "JWT"&gt; ...
            </para>
            'false' otherwise.
            </returns>
            <remarks>The 'EncodingType' attribute is optional, if it is set, it must be equal to: "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary".</remarks>
            <exception cref="T:System.ArgumentNullException">'reader' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CanReadToken(System.String)">
            <summary>
            Determines if the string is a well formed Json Web token (see http://tools.ietf.org/html/draft-ietf-oauth-json-web-token-07)
            </summary>
            <param name="tokenString">string that should represent a valid JSON Web Token.</param>
            <remarks>Uses <see cref="M:System.Text.RegularExpressions.Regex.IsMatch(System.String,System.String)"/>( token, @"^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$" ).
            </remarks>
            <returns>
            <para>'true' if the token is in JSON compact serialization format.</para>
            <para>'false' if token.Length * 2 &gt;  <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.MaximumTokenSizeInBytes"/>.</para>
            </returns>
            <exception cref="T:System.ArgumentNullException">'tokenString' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateSecurityTokenReference(System.IdentityModel.Tokens.SecurityToken,System.Boolean)">
            <summary>
            Creating <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> is not NotSupported.
            </summary>
            <exception cref="T:System.NotSupportedException"> to create a <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateToken(System.IdentityModel.Tokens.SecurityTokenDescriptor)">
            <summary>
            Creates a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> based on values found in the <see cref="T:System.IdentityModel.Tokens.SecurityTokenDescriptor"/>.
            </summary>
            <param name="tokenDescriptor">Contains the parameters used to create the token.</param>
            <returns>A <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</returns>
            <remarks>
            If <see cref="P:System.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials"/> is not null, <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.RawData"/> will be signed.
            </remarks>
            <exception cref="T:System.ArgumentNullException">'tokenDescriptor' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateToken(System.String,System.String,System.Security.Claims.ClaimsIdentity,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.IdentityModel.Tokens.SigningCredentials,System.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Uses the <see cref="M:System.IdentityModel.Tokens.JwtSecurityToken.#ctor(System.IdentityModel.Tokens.JwtHeader,System.IdentityModel.Tokens.JwtPayload,System.String,System.String,System.String)"/> constructor, first creating the <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> and <see cref="T:System.IdentityModel.Tokens.JwtPayload"/>.
            <para>If <see cref="T:System.IdentityModel.Tokens.SigningCredentials"/> is not null, <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.RawData"/> will be signed.</para>
            </summary>
            <param name="issuer">the issuer of the token.</param>
            <param name="audience">the audience for this token.</param>
            <param name="subject">the source of the <see cref="T:System.Security.Claims.Claim"/>(s) for this token.</param>
            <param name="notBefore">the notbefore time for this token.</param> 
            <param name="expires">the expiration time for this token.</param>
            <param name="signingCredentials">contains cryptographic material for generating a signature.</param>
            <param name="signatureProvider">optional <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>.</param>
            <remarks>If <see cref="P:System.Security.Claims.ClaimsIdentity.Actor"/> is not null, then a claim { actort, 'value' } will be added to the payload. <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateActorValue(System.Security.Claims.ClaimsIdentity)"/> for details on how the value is created.
            <para>See <seealso cref="T:System.IdentityModel.Tokens.JwtHeader"/> for details on how the HeaderParameters are added to the header.</para>
            <para>See <seealso cref="T:System.IdentityModel.Tokens.JwtPayload"/> for details on how the values are added to the payload.</para></remarks>
            <para>If signautureProvider is not null, then it will be used to create the signature and <see cref="M:System.IdentityModel.Tokens.SignatureProviderFactory.CreateForSigning(System.IdentityModel.Tokens.SecurityKey,System.String)"/> will not be called.</para>
            <returns>A <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</returns>
            <exception cref="T:System.ArgumentException">if 'expires' &lt;= 'notBefore'.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.GetTokenTypeIdentifiers">
            <summary>
            Gets the token type identifier(s) supported by this handler.
            </summary>
            <returns>A collection of strings that identify the tokens this instance can handle.</returns>
            <remarks>When receiving a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> wrapped inside a &lt;wsse:BinarySecurityToken&gt; element. The &lt;wsse:BinarySecurityToken&gt; element must have the ValueType attribute set to one of these values
            in order for this handler to recognize that it can read the token.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ReadToken(System.Xml.XmlReader)">
            <summary>
            Reads a JSON web token wrapped inside a WS-Security BinarySecurityToken xml element.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> pointing at the jwt.</param>
            <returns>An instance of <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/></returns>
            <remarks>First calls <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.CanReadToken
            <para>The reader must be positioned at an element named:</para>
            <para>BinarySecurityToken'.
            in the namespace: 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd'
            with a 'ValueType' attribute equal to one of: "urn:ietf:params:oauth:token-type:jwt", "JWT".</para>
            <para>
            For example &lt;wsse:BinarySecurityToken valueType = "JWT"&gt; ...
            </para>
            <para>
            The 'EncodingType' attribute is optional, if it is set, it must be equal to: "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary"
            </para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">'reader' is null.</exception>
            <exception cref="T:System.ArgumentException">if <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CanReadToken(System.Xml.XmlReader)"/> returns false.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ReadToken(System.String)">
            <summary>
            Reads a token encoded in JSON Compact serialized format.
            </summary>
            <param name="tokenString">A 'JSON Web Token' (JWT) that has been encoded as a JSON object. May be signed 
            using 'JSON Web Signature' (JWS).</param>
            <remarks>
            The JWT must be encoded using Base64Url encoding of the UTF-8 representation of the JWT: Header, Payload and Signature. 
            The contents of the JWT returned are not validated in any way, the token is simply decoded. Use ValidateToken to validate the JWT.
            </remarks>
            <returns>A <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/></returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateToken(System.IdentityModel.Tokens.SecurityToken)">
            <summary>
            Obsolete method, use <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateToken(System.String,System.IdentityModel.Tokens.TokenValidationParameters,System.IdentityModel.Tokens.SecurityToken@)"/>.
            </summary>
            <exception cref="T:System.NotSupportedException"> use <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateToken(System.String,System.IdentityModel.Tokens.TokenValidationParameters,System.IdentityModel.Tokens.SecurityToken@)"/>.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateToken(System.String,System.IdentityModel.Tokens.TokenValidationParameters,System.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Reads and validates a token encoded in JSON Compact serialized format.
            </summary>
            <param name="securityToken">A 'JSON Web Token' (JWT) that has been encoded as a JSON object. May be signed using 'JSON Web Signature' (JWS).</param>
            <param name="validationParameters">Contains validation parameters for the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</param>
            <param name="validatedToken">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> that was validated.</param>
            <exception cref="T:System.ArgumentNullException">'securityToken' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">'validationParameters' is null.</exception>
            <exception cref="T:System.ArgumentException">'securityToken.Length' &gt; <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.MaximumTokenSizeInBytes"/>.</exception>
            <returns>A <see cref="T:System.Security.Claims.ClaimsPrincipal"/> from the jwt. Does not include the header claims.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.WriteToken(System.Xml.XmlWriter,System.IdentityModel.Tokens.SecurityToken)">
            <summary>
            Writes the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> wrapped in a WS-Security BinarySecurityToken using the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="writer"><see cref="T:System.Xml.XmlWriter"/> used to write token.</param>
            <param name="token">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> that will be written.</param>
            <exception cref="T:System.ArgumentNullException">'writer' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'token' is null.</exception>
            <exception cref="T:System.ArgumentException">'token' is not a not <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</exception>
            <remarks>The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> current contents are encoded. If <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningCredentials"/> is not null, the encoding will contain a signature.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.WriteToken(System.IdentityModel.Tokens.SecurityToken)">
            <summary>
            Writes the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> as a JSON Compact serialized format string.
            </summary>
            <param name="token"><see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> to serialize.</param>
            <remarks>
            <para>If the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningCredentials"/> are not null, the encoding will contain a signature.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">'token' is null.</exception>
            <exception cref="T:System.ArgumentException">'token' is not a not <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</exception>
            <returns>The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> as a signed (if <see cref="T:System.IdentityModel.Tokens.SigningCredentials"/> exist) encoded string.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateSignature(System.String,System.IdentityModel.Tokens.SecurityKey,System.String,System.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> and algorithm specified.
            </summary>
            <param name="inputString">string to be signed</param>
            <param name="key">the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> to use.</param>
            <param name="algorithm">the algorithm to use.</param>
            <param name="signatureProvider">if provided, the <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/> will be used to sign the token</param>
            <returns>The signature over the bytes obtained from UTF8Encoding.GetBytes( 'input' ).</returns>
            <remarks>The <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/> used to created the signature is obtained by calling <see cref="M:System.IdentityModel.Tokens.SignatureProviderFactory.CreateForSigning(System.IdentityModel.Tokens.SecurityKey,System.String)"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.InvalidProgramException"><see cref="M:System.IdentityModel.Tokens.SignatureProviderFactory.CreateForSigning(System.IdentityModel.Tokens.SecurityKey,System.String)"/> returns null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateSignature(System.String,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates that the signature, if found and / or required is valid.
            </summary>
            <param name="token">A 'JSON Web Token' (JWT) that has been encoded as a JSON object. May be signed 
            using 'JSON Web Signature' (JWS).</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> that contains signing keys.</param>
            <exception cref="T:System.ArgumentNullException"> thrown if 'token is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException"> thrown if 'validationParameters is null.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenValidationException"> thrown if a signature is not found and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.RequireSignedTokens"/> is true.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"> thrown if the 'token' has a key identifier and none of the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) provided result in a validated signature. 
            This can indicate that a key refresh is required.</exception>
            <exception cref="T:System.IdentityModel.SignatureVerificationFailedException"> thrown if after trying all the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s), none result in a validated signture AND the 'token' does not have a key identifier.</exception>
            <returns><see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> that has the signature validated if token was signed and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.RequireSignedTokens"/> is true.</returns>
            <remarks><para>If the 'token' is signed, the signature is validated even if <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.RequireSignedTokens"/> is false.</para>
            <para>If the 'token' signature is validated, then the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningKey"/> will be set to the key that signed the 'token'.</para></remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateKeyString(System.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Produces a readable string for a key, used in error messages.
            </summary>
            <param name="securityKey"></param>
            <returns></returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateClaimsIdentity(System.IdentityModel.Tokens.JwtSecurityToken,System.String,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Creates a <see cref="T:System.Security.Claims.ClaimsIdentity"/> from a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.
            </summary>
            <param name="jwt">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> to use as a <see cref="T:System.Security.Claims.Claim"/> source.</param>
            <param name="issuer">The value to set <see cref="P:System.Security.Claims.Claim.Issuer"/></param>
            <param name="validationParameters"> contains parameters for validating the token.</param>
            <returns>A <see cref="T:System.Security.Claims.ClaimsIdentity"/> containing the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.Claims"/>.</returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CreateActorValue(System.Security.Claims.ClaimsIdentity)">
            <summary>
            Creates the 'value' for the actor claim: { actort, 'value' }
            </summary>
            <param name="actor"><see cref="T:System.Security.Claims.ClaimsIdentity"/> as actor.</param>
            <returns><see cref="T:System.String"/> representing the actor.</returns>
            <remarks>If <see cref="P:System.Security.Claims.ClaimsIdentity.BootstrapContext"/> is not null:
            <para>  if 'type' is 'string', return as string.</para>
            <para>  if 'type' is 'BootstrapContext' and 'BootstrapContext.SecurityToken' is 'JwtSecurityToken'</para>
            <para>    if 'JwtSecurityToken.RawData' != null, return RawData.</para>        
            <para>    else return <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.WriteToken(System.IdentityModel.Tokens.SecurityToken)"/>.</para>        
            <para>  if 'BootstrapContext.Token' != null, return 'Token'.</para>
            <para>default: <see cref="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.WriteToken(System.IdentityModel.Tokens.SecurityToken)"/> new ( <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>( actor.Claims ).</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">'actor' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateAudience(System.Collections.Generic.IEnumerable{System.String},System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if the audiences found in a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> are valid.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <remarks>see <see cref="M:System.IdentityModel.Tokens.Validators.ValidateAudience(System.Collections.Generic.IEnumerable{System.String},System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)"/> for additional details.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateLifetime(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the lifetime of a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.
            </summary>
            <param name="notBefore">The <see cref="T:System.DateTime"/> value of the 'nbf' claim if it exists in the 'jwt'.</param>
            <param name="expires">The <see cref="T:System.DateTime"/> value of the 'exp' claim if it exists in the 'jwt'.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <remarks><see cref="M:System.IdentityModel.Tokens.Validators.ValidateLifetime(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)"/> for additional details.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateIssuer(System.String,System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if an issuer found in a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> is valid.
            </summary>
            <param name="issuer">The issuer to validate</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the <see cref="T:System.Security.Claims.Claim"/>(s) in the <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</returns>
            <remarks><see cref="M:System.IdentityModel.Tokens.Validators.ValidateIssuer(System.String,System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)"/> for additional details.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ResolveIssuerSigningKey(System.String,System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.SecurityKeyIdentifier,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Returns a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> to use when validating the signature of a token.
            </summary>
            <param name="token">the <see cref="T:System.String"/> representation of the token that is being validated.</param>
            <param name="securityToken">the <SecurityToken> that is being validated.</SecurityToken></param>
            <param name="keyIdentifier">the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> found in the token.</param>
            <param name="validationParameters">A <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/>  required for validation.</param>
            <returns>Returns a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> to use for signature validation.</returns>
            <exception cref="T:System.ArgumentNullException">if 'keyIdentifier' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'validationParameters' is null.</exception>
            <remarks>If key fails to resolve, then null is returned</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ValidateIssuerSecurityKey(System.IdentityModel.Tokens.SecurityKey,System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningKey"/> is an expected value.
            </summary>
            <param name="securityKey">The <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> to validate.</param>
            <param name="validationParameters">the current <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/>.</param>
            <remarks>If the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.SigningKey"/> is a <see cref="T:System.IdentityModel.Tokens.X509SecurityKey"/> then the X509Certificate2 will be validated using <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.CertificateValidator"/>.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundAlgorithmMap">
            <summary>Gets or sets the <see cref="T:System.Collections.Generic.IDictionary`2"/> used to map Inbound Cryptographic Algorithms.</summary>
            <remarks>Strings that describe Cryptographic Algorithms that are understood by the runtime are not necessarily the same values used in the JsonWebToken specification.
            <para>When a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> signature is validated, the algorithm is obtained from the HeaderParameter { alg, 'value' }.
            The 'value' is translated according to this mapping and the translated 'value' is used when performing cryptographic operations.</para>
            <para>Default mapping is:</para>
            <para>    RS256 =&gt; http://www.w3.org/2001/04/xmldsig-more#rsa-sha256 </para>
            <para>    HS256 =&gt; http://www.w3.org/2001/04/xmldsig-more#hmac-sha256 </para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">'value' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundAlgorithmMap">
            <summary>Gets or sets the <see cref="T:System.Collections.Generic.IDictionary`2"/> used to map Outbound Cryptographic Algorithms.</summary>
            <remarks>Strings that describe Cryptographic Algorithms understood by the runtime are not necessarily the same in the JsonWebToken specification.
            <para>This property contains mappings the will be used to when creating a <see cref="T:System.IdentityModel.Tokens.JwtHeader"/> and setting the HeaderParameter { alg, 'value' }. 
            The 'value' set is translated according to this mapping.
            </para>
            <para>Default mapping is:</para>
            <para>    http://www.w3.org/2001/04/xmldsig-more#rsa-sha256  =&gt; RS256</para>
            <para>    http://www.w3.org/2001/04/xmldsig-more#hmac-sha256 =&gt; HS256</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">'value' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap">
            <summary>
            Gets or sets the <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap"/> that is used when setting the <see cref="P:System.Security.Claims.Claim.Type"/> for claims in the <see cref="T:System.Security.Claims.ClaimsPrincipal"/> extracted when validating a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>. 
            <para>The <see cref="P:System.Security.Claims.Claim.Type"/> is set to the JSON claim 'name' after translating using this mapping.</para>
            </summary>
            <exception cref="T:System.ArgumentNullException">'value is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundClaimTypeMap">
            <summary>
            <para>Gets or sets the <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.OutboundClaimTypeMap"/> that is used when creating a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> from <see cref="T:System.Security.Claims.Claim"/>(s).</para>
            <para>The JSON claim 'name' value is set to <see cref="P:System.Security.Claims.Claim.Type"/> after translating using this mapping.</para>
            </summary>
            <remarks>This mapping is applied only when using <see cref="M:System.IdentityModel.Tokens.JwtPayload.AddClaim(System.Security.Claims.Claim)"/> or <see cref="M:System.IdentityModel.Tokens.JwtPayload.AddClaims(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})"/>. Adding values directly will not result in translation.</remarks>
            <exception cref="T:System.ArgumentNullException">'value is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimFilter">
            <summary>Gets or sets the <see cref="T:System.Collections.Generic.ISet`1"/> used to filter claims when populating a <see cref="T:System.Security.Claims.ClaimsIdentity"/> claims form a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.
            When a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/> is validated, claims with types found in this <see cref="T:System.Collections.Generic.ISet`1"/> will not be added to the <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</summary>
            <exception cref="T:System.ArgumentNullException">'value' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.ShortClaimTypeProperty">
            <summary>
            Gets or sets the property name of <see cref="P:System.Security.Claims.Claim.Properties"/> the will contain the original JSON claim 'name' if a mapping occurred when the <see cref="T:System.Security.Claims.Claim"/>(s) were created.
            <para>See <seealso cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap"/> for more information.</para>
            </summary>
            <exception cref="T:System.ArgumentException">if <see cref="T:System.String"/>.IsIsNullOrWhiteSpace('value') is true.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.JsonClaimTypeProperty">
            <summary>
            Gets or sets the property name of <see cref="P:System.Security.Claims.Claim.Properties"/> the will contain .Net type that was recogninzed when JwtPayload.Claims serialized the value to JSON.
            <para>See <seealso cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.InboundClaimTypeMap"/> for more information.</para>
            </summary>
            <exception cref="T:System.ArgumentException">if <see cref="T:System.String"/>.IsIsNullOrWhiteSpace('value') is true.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CanValidateToken">
            <summary>
            Returns 'true' which indicates this instance can validate a <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.CanWriteToken">
            <summary>
            Returns 'true', which indicates this instance can write <see cref="T:System.IdentityModel.Tokens.JwtSecurityToken"/>.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.TokenLifetimeInMinutes">
            <summary>
            Gets and sets the token lifetime in minutes.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' less than 1.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.MaximumTokenSizeInBytes">
            <summary>
            Gets and sets the maximum size in bytes, that a will be processed.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' less than 1.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.SignatureProviderFactory">
            <summary>
            Gets or sets the <see cref="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.SignatureProviderFactory"/> for creating <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>(s).
            </summary>
            <remarks>This extensibility point can be used to insert custom <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>(s).
            <para><see cref="M:System.IdentityModel.Tokens.SignatureProviderFactory.CreateForVerifying(System.IdentityModel.Tokens.SecurityKey,System.String)"/> is called to obtain a <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>(s) when needed.</para></remarks>
            <exception cref="T:System.ArgumentNullException">'value' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.JwtSecurityTokenHandler.TokenType">
            <summary>
            Gets the <see cref="T:System.Type"/> supported by this handler.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver">
            <summary>
            <see cref="T:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver"/> represents a collection of named sets of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) that can be matched by a
            <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> and return a <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityToken"/> that contains <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s).
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver"/> class. 
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.#ctor(System.Collections.Generic.IDictionary{System.String,System.Collections.Generic.IList{System.IdentityModel.Tokens.SecurityKey}},System.IdentityModel.Tokens.IssuerTokenResolver)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver"/> class. 
            Populates this instance with a named collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) and an optional <see cref="T:System.IdentityModel.Selectors.SecurityTokenResolver"/> that will be called when a 
            <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> or <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> cannot be resolved.
            </summary>
            <param name="keys">
            A named collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s).
            </param>
            <param name="innerTokenResolver">
            A <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.IssuerTokenResolver"/> to call when resolving fails, before calling base.
            </param>
            <remarks>
            if 'keys' is null an empty collection will be created. A named collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) can be added by accessing the property <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.SecurityKeys"/>.
            </remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.LoadCustomConfiguration(System.Xml.XmlNodeList)">
            <summary>
            Populates the <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.SecurityKeys"/> from xml.
            </summary>
            <param name="nodeList">xml for processing.</param>
            <exception cref="T:System.ArgumentNullException">'nodeList' is null.</exception>
            <remarks>Only <see cref="T:System.Xml.XmlNode"/>(s) with <see cref="P:System.Xml.XmlElement.LocalName"/> == 'securityKey' will be processed. Unprocessed nodes will added to a list and can be accessed using the <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.UnprocessedXmlNodes"/> property.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.ReadSecurityKey(System.Xml.XmlElement)">
            <summary>
            When processing xml in <see cref="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.LoadCustomConfiguration(System.Xml.XmlNodeList)"/> each <see cref="T:System.Xml.XmlElement"/> that has <see cref="P:System.Xml.XmlElement.LocalName"/> = "securityKey' is passed here for processing.
            </summary>
            <param name="element">contains xml to map to a named <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>.</param>
            <remarks>
            <para>A single <see cref="T:System.Xml.XmlElement"/> is expected with up to three attributes: {'expected values'}.</para>
            <para>&lt;securityKey</para>
            <para>    symmetricKey {required}</para>
            <para>    name         {required}</para>
            <para>    EncodingType or encodingType {optional}</para>
            <para>&gt;</para>
            <para>&lt;/securityKey&gt;</para>
            <para>If "EncodingType' type is specified only:</para>
            <para>    'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary'</para>
            <para>    'Base64Binary'</para>
            <para>    'base64Binary'</para>
            <para>are allowed and have the same meaning.</para>
            <para>When a symmetricKey is found, Convert.FromBase64String( value ) is applied to create the key.</para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">'element' is null.</exception>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">attribute 'symmetricKey' is not found.</exception>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">value of 'symmetricKey' is empty or whitespace.</exception>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">attribute 'name' is not found.</exception>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">value of 'name' is empty or whitespace.</exception>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">value of 'encodingType' is not valid.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.TryResolveSecurityKeyCore(System.IdentityModel.Tokens.SecurityKeyIdentifierClause,System.IdentityModel.Tokens.SecurityKey@)">
            <summary>
            Finds the first <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> in a named collection that match the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>.
            </summary>
            <param name="keyIdentifierClause">
            The <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> to resolve to a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>
            </param>
            <param name="key">
            The resolved <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>.
            </param>
            <remarks>
            If there is no match, then <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.IssuerTokenResolver"/> and 'base' are called in order.
            </remarks>
            <returns>
            true if key resolved, false otherwise.
            </returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.TryResolveTokenCore(System.IdentityModel.Tokens.SecurityKeyIdentifier,System.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Finds a named collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) that match the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> and returns a <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityToken"/> that contains the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s).
            </summary>
            <param name="keyIdentifier">The <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> to resolve to a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/></param>
            <param name="token">The resolved <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <remarks>
            <para>
            A <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> can contain multiple <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>(s). This method will return the named collection that matches the first <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>
            </para>
            <para>
            If there is no match, then <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.IssuerTokenResolver"/> and 'base' are called in order.
            </para>
            </remarks>
            <returns>
            true is the keyIdentifier is resolved, false otherwise.
            </returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.TryResolveTokenCore(System.IdentityModel.Tokens.SecurityKeyIdentifierClause,System.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Finds a named collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) that match the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> and returns a <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityToken"/> that contains the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s).
            </summary>
            <param name="keyIdentifierClause">The <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> to resolve to a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/></param>
            <param name="token">The resolved <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <remarks>If there is no match, then <see cref="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.IssuerTokenResolver"/> and 'base' are called in order.</remarks>
            <returns>true if token was resolved.</returns>
            <exception cref="T:System.ArgumentNullException">if 'keyIdentifierClause' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.SecurityKeys">
            <summary>
            Gets the named collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s).
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.IssuerTokenResolver">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Selectors.SecurityTokenResolver"/> to call when <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> or <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> fails to resolve, before calling base.
            </summary>
            <exception cref="T:System.ArgumentNullException">'value' is null.</exception>
            <exception cref="T:System.ArgumentException">'object.ReferenceEquals( this, value)' is true.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.UnprocessedXmlNodes">
            <summary>
            Gets the unprocessed <see cref="T:System.Xml.XmlNode"/>(s) from <see cref="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.LoadCustomConfiguration(System.Xml.XmlNodeList)"/>.
            </summary>
            <remarks><see cref="M:System.IdentityModel.Tokens.NamedKeyIssuerTokenResolver.LoadCustomConfiguration(System.Xml.XmlNodeList)"/> processes only <see cref="T:System.Xml.XmlElement"/>(s) that have the <see cref="P:System.Xml.XmlElement.LocalName"/> == 'securityKey'. Unprocessed <see cref="T:System.Xml.XmlNode"/>(s) are accessible here.</remarks>
        </member>
        <member name="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause">
            <summary>
            A <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> that can be used to match <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityToken"/>.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> class. The 'name' for matching key identifiers found in the securityToken.
            </summary>
            <param name="name">Used to identify a named collection of keys.</param>
            <param name="id">Additional information for matching.</param>
            <exception cref="T:System.ArgumentNullException">if 'name' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">if 'id' is null or whitespace</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause.Matches(System.IdentityModel.Tokens.SecurityKeyIdentifierClause)">
            <summary>
            Determines if a <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> matches this instance.
            </summary>
            <param name="keyIdentifierClause">The <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> to match.</param>
            <returns>true if:
            <para>    1. keyIdentifierClause is a <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/>.</para>
            <para>    2. string.Equals( keyIdentifierClause.Name, this.Name, StringComparison.Ordinal).</para>
            <para>    2. string.Equals( keyIdentifierClause.Id, this.Id, StringComparison.Ordinal).</para>
            <para>Otherwise calls base.Matches( keyIdentifierClause ).</para>
            </returns>
            <exception cref="T:System.ArgumentNullException">'keyIdentifierClause' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause.Name">
            <summary>
            Gets the name of the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) this <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> represents.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.NamedKeySecurityToken">
            <summary>
            A <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> that contains multiple <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that have a name.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeySecurityToken.#ctor(System.String,System.String,System.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityToken"/> class that contains a single <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
            <param name="name">A name for the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>.</param>
            <param name="id">the identifier for this token.</param>
            <param name="key">A <see cref="T:System.IdentityModel.Tokens.SecurityKey"/></param>
            <exception cref="T:System.ArgumentNullException">if 'name' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">if 'id' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">if 'key' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeySecurityToken.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.IdentityModel.Tokens.SecurityKey})">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityToken"/> class that contains a <see cref="T:System.Collections.Generic.IEnumerable`1"/>(System.IdentityModel.Tokens.SecurityKey) that can be matched by name.
            </summary>
            <param name="id">the identifier for this token.</param>
            <param name="name">A name for the <see cref="T:System.Collections.Generic.IEnumerable`1"/>(System.IdentityModel.Tokens.SecurityKey).</param>
            <param name="keys">A collection of <see cref="T:System.IdentityModel.Tokens.SecurityKey"/></param>
            <exception cref="T:System.ArgumentNullException">if 'name' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">if 'id' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">if 'keys' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeySecurityToken.ResolveKeyIdentifierClause(System.IdentityModel.Tokens.SecurityKeyIdentifierClause)">
            <summary>
            Gets the first<see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that matches a <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>
            </summary>
            <param name="keyIdentifierClause">the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> to match.</param>
            <returns>The first <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that matches the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>.
            <para>null if there is no match.</para></returns>
            <para>Only <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> are matched.</para>
            <exception cref="T:System.ArgumentNullException">'keyIdentifierClause' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.NamedKeySecurityToken.MatchesKeyIdentifierClause(System.IdentityModel.Tokens.SecurityKeyIdentifierClause)">
            <summary>
            Answers if the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/> is a match.
            </summary>
            <param name="keyIdentifierClause">The <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause"/></param>
            <returns>true if matched.</returns>
            <remarks><para>A successful match occurs when <see cref="P:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause.Name"/> == <see cref="P:System.IdentityModel.Tokens.NamedKeySecurityToken.Id"/>.</para>
            <para>Only <see cref="T:System.IdentityModel.Tokens.NamedKeySecurityKeyIdentifierClause"/> are matched.</para></remarks>
            <exception cref="T:System.ArgumentNullException">'keyIdentifierClause' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeySecurityToken.Id">
            <summary>
            Gets the id of the security token.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeySecurityToken.Name">
            <summary>
            Gets the Name of the security token.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeySecurityToken.ValidFrom">
            <summary>
            Gets the creation time as a <see cref="T:System.DateTime"/>.
            </summary>
            <remarks>The default is: <see cref="P:System.DateTime.UtcNow"/>.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeySecurityToken.ValidTo">
            <summary>
            Gets the expiration time as a <see cref="T:System.DateTime"/>
            </summary>
            <remarks>The default is: <see cref="F:System.DateTime.MaxValue"/>.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.NamedKeySecurityToken.SecurityKeys">
            <summary>
            Gets the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s).
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">
            <summary>
            This exception is thrown when 'audience' of a token was not valid.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">
            <summary>
            This exception is thrown when 'issuer' of a token was not valid.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException">
            <summary>
            This exception is thrown when 'lifetime' of a token was not valid.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException">
            <summary>
            This exception is thrown when a security is missing an ExpirationTime.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">
            <summary>
            This exception is thrown when an add to the TokenReplayCache fails.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException">
            <summary>
            This exception is thrown when a security token contained a key identifier but the key was not found by the runtime.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.SignatureProviderFactory">
            <summary>
            Creates <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>s by specifying a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            <para>Supports both <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/> and <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>.</para>
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.SignatureProviderFactory.AbsoluteMinimumAsymmetricKeySizeInBitsForSigning">
            <summary>
            This is the minimum <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize when creating signatures.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.SignatureProviderFactory.AbsoluteMinimumAsymmetricKeySizeInBitsForVerifying">
            <summary>
            This is the minimum <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize when verifying signatures.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.SignatureProviderFactory.AbsoluteMinimumSymmetricKeySizeInBits">
            <summary>
            This is the minimum <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize when creating and verifying signatures.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProviderFactory.CreateForSigning(System.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates a <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/> that supports the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            </summary>
            <param name="key">
            The <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> to use for signing.
            </param>
            <param name="algorithm">
            The algorithm to use for signing.
            </param>
            <exception cref="T:System.ArgumentNullException">
            'key' is null.
            </exception>
            <exception cref="T:System.ArgumentNullException">
            'algorithm' is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            'algorithm' contains only whitespace.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            '<see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>' is smaller than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumAsymmetricKeySizeInBitsForSigning"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            '<see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>' is smaller than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumSymmetricKeySizeInBits"/>.
            </exception>
            <exception cref="T:System.ArgumentException">
            '<see cref="T:System.IdentityModel.Tokens.SecurityKey"/>' is not a <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/> or a <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>.
            </exception>
            <remarks>
            AsymmetricSignatureProviders require access to a PrivateKey for Signing.
            </remarks>
            <returns>
            The <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>.
            </returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProviderFactory.CreateForVerifying(System.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Returns a <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/> instance supports the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            </summary>
            <param name="key">
            The <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> to use for signing.
            </param>
            <param name="algorithm">
            The algorithm to use for signing.
            </param>
            <exception cref="T:System.ArgumentNullException">
            'key' is null.
            </exception>
            <exception cref="T:System.ArgumentNullException">
            'algorithm' is null.
            </exception>
            <exception cref="T:System.ArgumentException">
            'algorithm' contains only whitespace.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            '<see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>' is smaller than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumAsymmetricKeySizeInBitsForVerifying"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            '<see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>' is smaller than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumSymmetricKeySizeInBits"/>.
            </exception>
            <exception cref="T:System.ArgumentException">
            '<see cref="T:System.IdentityModel.Tokens.SecurityKey"/>' is not a <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/> or a <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>.
            </exception>
            <returns>
            The <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/>.
            </returns>
        </member>
        <member name="M:System.IdentityModel.Tokens.SignatureProviderFactory.ReleaseProvider(System.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            When finished with a <see cref="T:System.IdentityModel.Tokens.SignatureProvider"/> call this method for cleanup. The default behavior is to call <see cref="M:System.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)"/>
            </summary>
            <param name="signatureProvider"><see cref="T:System.IdentityModel.Tokens.SignatureProvider"/> to be released.</param>
        </member>
        <member name="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumSymmetricKeySizeInBits">
            <summary>
            Gets or sets the minimum <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize"/&gt;.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' is smaller than <see cref="F:System.IdentityModel.Tokens.SignatureProviderFactory.AbsoluteMinimumSymmetricKeySizeInBits"/>.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumAsymmetricKeySizeInBitsForSigning">
            <summary>
            Gets or sets the minimum <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize for creating signatures.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' is smaller than <see cref="F:System.IdentityModel.Tokens.SignatureProviderFactory.AbsoluteMinimumAsymmetricKeySizeInBitsForSigning"/>.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumAsymmetricKeySizeInBitsForVerifying">
            <summary>
            Gets or sets the minimum <see cref="T:System.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize for verifying signatures.
            <exception cref="T:System.ArgumentOutOfRangeException">'value' is smaller than <see cref="F:System.IdentityModel.Tokens.SignatureProviderFactory.AbsoluteMinimumAsymmetricKeySizeInBitsForVerifying"/>.</exception>
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.SymmetricSignatureProvider">
            <summary>
            Provides signing and verifying operations using a <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/> and specifying an algorithm.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(System.IdentityModel.Tokens.SymmetricSecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.SymmetricSignatureProvider"/> class that uses an <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/> to create and / or verify signatures over a array of bytes.
            </summary>
            <param name="key">The <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/> used for signing.</param>
            <param name="algorithm">The signature algorithm to use.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null.</exception>
            <exception cref="T:System.ArgumentException">'algorithm' contains only whitespace.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">'<see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize' is smaller than <see cref="P:System.IdentityModel.Tokens.SignatureProviderFactory.MinimumSymmetricKeySizeInBits"/>.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="M:System.IdentityModel.Tokens.SymmetricSecurityKey.GetKeyedHashAlgorithm(System.String)"/> throws.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="M:System.IdentityModel.Tokens.SymmetricSecurityKey.GetKeyedHashAlgorithm(System.String)"/> returns null.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="M:System.IdentityModel.Tokens.SymmetricSecurityKey.GetSymmetricKey"/> throws.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(System.IdentityModel.Tokens.SymmetricSecurityKey,System.String)"/>.
            </summary>
            <param name="input">bytes to sign.</param>
            <returns>signed bytes</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null. </exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that a signature created over the 'input' matches the signature. Using <see cref="T:System.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(System.IdentityModel.Tokens.SymmetricSecurityKey,System.String)"/>.
            </summary>
            <param name="input">bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException">if the internal <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)">
            <summary>
            Disposes of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="M:System.IdentityModel.Tokens.SymmetricSignatureProvider.AreEqual(System.Byte[],System.Byte[])">
            <summary>
            Compares two byte arrays for equality. Hash size is fixed normally it is 32 bytes.
            The attempt here is to take the same time if an attacker shortens the signature OR changes some of the signed contents.
            </summary>
            <param name="a">
            One set of bytes to compare.
            </param>
            <param name="b">
            The other set of bytes to compare with.
            </param>
            <returns>
            true if the bytes are equal, false otherwise.
            </returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.AudienceValidator">
            <summary>
            Definition for AudienceValidator.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.IssuerSigningKeyResolver">
            <summary>
            Definition for IssuerSigningKeyRetriever. When validating signatures, this method will return key to use.
            </summary>
            <param name="token">the <see cref="T:System.String"/> representation of the token that is being validated.</param>
            <param name="securityToken">the <SecurityToken> that is being validated. It may be null.</SecurityToken></param>
            <param name="keyIdentifier">the <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifier"/> found in the token. It may be null.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns></returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.IssuerValidator">
            <summary>
            Definition for IssuerValidator.
            </summary>
            <param name="issuer">The issuer to validate.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
        </member>
        <member name="T:System.IdentityModel.Tokens.LifetimeValidator">
            <summary>
            Definition for LifetimeValidator.
            </summary>
            <param name="notBefore">The 'notBefore' time found in the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="expires">The 'expiration' time found in the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:System.IdentityModel.Tokens.TokenValidationParameters">
            <summary>
            Contains a set of parameters that are used by a <see cref="T:System.IdentityModel.Tokens.SecurityTokenHandler"/> when validating a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.TokenValidationParameters.DefaultMaximumTokenSizeInBytes">
            <summary>
            Default for the maximm token size.
            </summary>
            <remarks>2 MB (mega bytes).</remarks>
        </member>
        <member name="F:System.IdentityModel.Tokens.TokenValidationParameters.DefaultAuthenticationType">
            <summary>
            This is the fallback authenticationtype that a <see cref="T:System.IdentityModel.Tokens.ISecurityTokenValidator"/> will use if nothing is set.
            </summary>
        </member>
        <member name="F:System.IdentityModel.Tokens.TokenValidationParameters.DefaultClockSkew">
            <summary>
            Default for the clock skew.
            </summary>
            <remarks>300 seconds (5 minutes).</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.TokenValidationParameters.#ctor(System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Copy constructor for <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.TokenValidationParameters.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> class.
            </summary>        
        </member>
        <member name="M:System.IdentityModel.Tokens.TokenValidationParameters.Clone">
            <summary>
            Returns a new instance of <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> with values copied from this object.
            </summary>
            <returns>A new <see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> object copied from this object</returns>
            <remarks>This is a shallow Clone.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.TokenValidationParameters.CreateClaimsIdentity(System.IdentityModel.Tokens.SecurityToken,System.String)">
            <summary>
            Creates a <see cref="T:System.Security.Claims.ClaimsIdentity"/> using:
            <para><see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType"/></para>
            <para>'NameClaimType' is calculated: If NameClaimTypeRetriever call that else use NameClaimType. If the result is a null or empty string, use <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultNameClaimType"/></para>.
            <para>'RoleClaimType' is calculated: If RoleClaimTypeRetriever call that else use RoleClaimType. If the result is a null or empty string, use <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultRoleClaimType"/></para>.
            </summary>
            <returns>A <see cref="T:System.Security.Claims.ClaimsIdentity"/> with Authentication, NameClaimType and RoleClaimType set.</returns>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.AudienceValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the audience of the tokens
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType">
            <summary>
            Gets or sets the AuthenticationType when creating a <see cref="T:System.Security.Claims.ClaimsIdentity"/> during token validation.
            </summary>
            <exception cref="T:System.ArgumentNullException"> if 'value' is null or whitespace.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.CertificateValidator">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Selectors.X509CertificateValidator"/> for validating X509Certificate2(s).
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ClientDecryptionTokens">
            <summary>
            Gets or sets the <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1"/> that is to be used for decrypting inbound tokens.
            </summary>
            <exception cref="T:System.ArgumentNullException">if 'value' is null.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ClockSkew">
            <summary>
            Gets or sets the clock skew to apply when validating times
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> if 'value' is less than 0.</exception>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidator">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that is to be used for validating signed tokens. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKey">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that is to be used for validating signed tokens. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolver">
            <summary>
            Gets or sets a delegate that will be used to retreive <see cref="T:System.IdentityModel.Tokens.SecurityKey"/>(s) used for checking signatures.
            </summary>
            <remarks>Each <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> will be used to check the signature. Returning multiple key can be helpful when the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> does not contain a key identifier. 
            This can occur when the issuer has multiple keys available. This sometimes occurs during key rollover.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeys">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that are to be used for validating signed tokens. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningToken">
            <summary>
            Gets or sets the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> that is used for validating signed tokens. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningTokens">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that are to be used for validating signed tokens. 
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the issuer of the token. The delegate returns the issuer to use.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.LifetimeValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the lifetime of the token
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.NameClaimType">
            <summary>
            Gets or sets the <see cref="T:System.String"/> passed to <see cref="M:System.Security.Claims.ClaimsIdentity.#ctor(System.String,System.String,System.String)"/>. 
            </summary>
            <remarks>
            Controls the value <see cref="P:System.Security.Claims.ClaimsIdentity.Name"/> returns. It will return the first <see cref="P:System.Security.Claims.Claim.Value"/> where the <see cref="P:System.Security.Claims.Claim.Type"/> equals <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.NameClaimType"/>.
            </remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.RoleClaimType">
            <summary>
            Gets or sets the <see cref="T:System.String"/> passed to <see cref="M:System.Security.Claims.ClaimsIdentity.#ctor(System.String,System.String,System.String)"/>.
            </summary>
            <remarks>
            <para>Controls the <see cref="T:System.Security.Claims.Claim"/>(s) returned from <see cref="M:System.Security.Claims.ClaimsPrincipal.IsInRole(System.String)"/>.</para>
            <para>Each <see cref="T:System.Security.Claims.Claim"/> returned will have a <see cref="P:System.Security.Claims.Claim.Type"/> equal to <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.RoleClaimType"/>.</para>
            </remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.NameClaimTypeRetriever">
            <summary>
            Gets or sets a delegate that will be called to obtain the NameClaimType to use when creating a ClaimsIdentity
            when validating a token.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.RequireExpirationTime">
            <summary>
            Gets or sets a value indicating whether tokens must have an 'expiration' value.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.RequireSignedTokens">
            <summary>
            Gets or sets a value indicating whether a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> can be valid if not signed.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.RoleClaimTypeRetriever">
            <summary>
            Gets or sets a delegate that will be called to obtain the RoleClaimType to use when creating a ClaimsIdentity
            when validating a token.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.SaveSigninToken">
            <summary>
            Gets or sets a boolean to control if the original token is saved when a session is created.       /// </summary>
            <remarks>The SecurityTokenValidator will use this value to save the orginal string that was validated.</remarks>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache">
            <summary>
            Gets or set the <see cref="T:System.IdentityModel.Tokens.ITokenReplayCache"/> that will be checked to help in detecting that a token has been 'seen' before.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateActor">
            <summary>
            Gets or sets a value indicating whether the <see cref="P:System.IdentityModel.Tokens.JwtSecurityToken.Actor"/> should be validated.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience">
            <summary>
            Gets or sets a boolean to control if the audience will be validated during token validation.
            </summary>        
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer">
            <summary>
            Gets or sets a boolean to control if the issuer will be validated during token validation.
            </summary>                
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateLifetime">
            <summary>
            Gets or sets a boolean to control if the lifetime will be validated during token validation.
            </summary>                
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuerSigningKey">
            <summary>
            Gets or sets a boolean that controls if validation of the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that signed the securityToken is called.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidAudience">
            <summary>
            Gets or sets a string that represents a valid audience that will be used during token validation.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.ICollection`1"/> that contains valid audiences that will be used during token validation.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer">
            <summary>
            Gets or sets a <see cref="T:System.String"/> that represents a valid issuer that will be used during token validation.
            </summary>
        </member>
        <member name="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.ICollection`1"/> that contains valid issuers that will be used during token validation.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.Validators">
            <summary>
            AudienceValidator
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.Validators.ValidateAudience(System.Collections.Generic.IEnumerable{System.String},System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if the audiences found in a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> are valid.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException"> if 'vaidationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'audiences' is null and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience"/> is true.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"> if <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidAudience"/> is null or whitespace and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences"/> is null.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"> if none of the 'audiences' matched either <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidAudience"/> or one of <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.Validators.ValidateIssuer(System.String,System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if an issuer found in a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> is valid.
            </summary>
            <param name="issuer">The issuer to validate</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <exception cref="T:System.ArgumentNullException"> if 'vaidationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'issuer' is null or whitespace and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is true.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"> if <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> is null or whitespace and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> is null.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"> if 'issuer' failed to matched either <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> or one of <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.Validators.ValidateIssuerSecurityKey(System.IdentityModel.Tokens.SecurityKey,System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that signed a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="securityKey">The <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException"> if 'vaidationParameters' is null.</exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.Validators.ValidateLifetime(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.IdentityModel.Tokens.SecurityToken,System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the lifetime of a <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="notBefore">The 'notBefore' time found in the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="expires">The 'expiration' time found in the <see cref="T:System.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException"> if 'vaidationParameters' is null.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException"> if 'expires.HasValue' is false and <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.RequireExpirationTime"/> is true.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"> if 'notBefore' is &gt; 'expires'.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenNotYetValidException"> if 'notBefore' is &gt; DateTime.UtcNow.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenExpiredException"> if 'expires' is &lt; DateTime.UtcNow.</exception>
            <remarks>All time comparisons apply <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.ClockSkew"/>.</remarks>
        </member>
        <member name="M:System.IdentityModel.Tokens.Validators.ValidateTokenReplay(System.String,System.Nullable{System.DateTime},System.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates if a token has been replayed.
            </summary>
            <param name="securityToken">The <see cref="T:System.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="expirationTime">When does the security token expire.</param>
            <param name="validationParameters"><see cref="T:System.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">if 'securityToken' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">if 'validationParameters' is null or whitespace.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenNoExpirationException">if <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/> is not null and expirationTime.HasValue is false. When a TokenReplayCache is set, tokens require an expiration time.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenReplayDetectedException">if the 'securityToken' is found in the cache.</exception>
            <exception cref="T:System.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">if the 'securityToken' could not be added to the <see cref="P:System.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/>.</exception>
        </member>
        <member name="T:System.IdentityModel.Tokens.WSSecurityConstantsInternal">
            <summary>
            Defines constants needed from WS-Security 1.0.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.WSSecurityUtilityConstantsInternal">
            <summary>
            Defines constants needed from WS-SecureUtility standard schema.
            </summary>
        </member>
        <member name="T:System.IdentityModel.Tokens.X509CertificateValidatorEx">
            <summary>
            This class also resets the chainPolicy.VerificationTime = DateTime.Now each time a certificate is validated otherwise certificates created after the validator is created will not chain.
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.X509CertificateValidatorEx.#ctor(System.ServiceModel.Security.X509CertificateValidationMode,System.Security.Cryptography.X509Certificates.X509RevocationMode,System.Security.Cryptography.X509Certificates.StoreLocation)">
            <summary>
            Initializes a new instance of the <see cref="T:System.IdentityModel.Tokens.X509CertificateValidatorEx"/> class.
            </summary>
            <param name="certificateValidationMode">
            The certificate validation mode.
            </param>
            <param name="revocationMode">
            The revocation mode.
            </param>
            <param name="trustedStoreLocation">
            The trusted store location.
            </param>
            <exception cref="T:System.InvalidOperationException"> thrown if the certificationValidationMode is custom or unknown.
            </exception>
        </member>
        <member name="M:System.IdentityModel.Tokens.X509CertificateValidatorEx.Validate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Validates a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>.
            </summary>
            <param name="certificate">
            The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> to validate.
            </param>
        </member>
        <member name="T:System.IdentityModel.Tokens.X509SecurityKey">
            <summary>
            Security key that allows access to cert
            </summary>
        </member>
        <member name="M:System.IdentityModel.Tokens.X509SecurityKey.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Instantiates a <see cref="T:System.IdentityModel.Tokens.SecurityKey"/> using a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>
            </summary>
            <param name="certificate"> cert to use.</param>
        </member>
        <member name="P:System.IdentityModel.Tokens.X509SecurityKey.Certificate">
            <summary>
            Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>.
            </summary>
        </member>
    </members>
</doc>
